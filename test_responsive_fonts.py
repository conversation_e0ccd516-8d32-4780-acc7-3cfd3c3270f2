#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الخطوط المتجاوبة
Responsive Fonts Test
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_font_responsiveness():
    """اختبار تجاوب الخطوط مع الإعدادات"""
    print("🔤 اختبار الخطوط المتجاوبة")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.models.settings import Settings
        from src.utils.font_utils import FontManager, apply_font_settings, get_responsive_style
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # اختبار مدير الخطوط
        font_manager = FontManager()
        print("✅ تم إنشاء مدير الخطوط بنجاح")
        
        # اختبار الحصول على الخط الحالي
        current_font = font_manager.get_current_font()
        print(f"✅ الخط الحالي: {current_font.family()}, الحجم: {current_font.pointSize()}")
        
        # اختبار حساب ارتفاع الحقول
        field_height = font_manager.get_field_height()
        print(f"✅ ارتفاع الحقول المحسوب: {field_height} بكسل")
        
        # اختبار الأنماط المتجاوبة
        responsive_style = get_responsive_style()
        print("✅ تم إنشاء الأنماط المتجاوبة بنجاح")
        
        # اختبار تغيير حجم الخط
        settings = Settings()
        original_size = settings.get_setting('font_size')
        
        test_sizes = [8, 12, 16, 20, 24]
        for size in test_sizes:
            settings.set_setting('font_size', size)
            new_height = font_manager.get_field_height()
            print(f"✅ حجم الخط {size}: ارتفاع الحقول {new_height} بكسل")
            
        # إعادة الحجم الأصلي
        settings.set_setting('font_size', original_size)
        
        # تنظيف
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الخطوط: {str(e)}")
        return False

def test_dialog_font_application():
    """اختبار تطبيق الخطوط على النوافذ"""
    print("\n🖥️ اختبار تطبيق الخطوط على النوافذ")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.customer_dialog import CustomerDialog
        from src.ui.order_dialog import OrderDialog
        from src.ui.payment_dialog import PaymentDialog
        from src.ui.cutting_payment_dialog import CuttingPaymentDialog
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # قائمة النوافذ للاختبار
        dialogs_to_test = [
            ("نافذة العملاء", CustomerDialog),
            ("نافذة الطلبات", OrderDialog),
            ("نافذة الدفعات", PaymentDialog),
            ("نافذة دفعات النشر", CuttingPaymentDialog),
        ]
        
        results = {}
        
        for dialog_name, dialog_class in dialogs_to_test:
            try:
                # إنشاء النافذة
                dialog = dialog_class()
                
                # التحقق من تطبيق الخط
                font = dialog.font()
                if font.pointSize() > 0:
                    print(f"✅ {dialog_name}: الخط مطبق - {font.family()}, {font.pointSize()}pt")
                    results[dialog_name] = True
                else:
                    print(f"⚠️ {dialog_name}: الخط غير مطبق بشكل صحيح")
                    results[dialog_name] = False
                
                # تنظيف
                dialog.close()
                
            except Exception as e:
                print(f"❌ {dialog_name}: خطأ - {str(e)}")
                results[dialog_name] = False
                
        # تنظيف التطبيق
        app.quit()
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ عام في اختبار النوافذ: {str(e)}")
        return {}

def test_different_font_sizes():
    """اختبار أحجام خطوط مختلفة"""
    print("\n📏 اختبار أحجام الخطوط المختلفة")
    print("=" * 50)
    
    try:
        from src.models.settings import Settings
        from src.utils.font_utils import FontManager
        
        settings = Settings()
        font_manager = FontManager()
        
        # حفظ الإعدادات الأصلية
        original_size = settings.get_setting('font_size')
        original_family = settings.get_setting('font_family')
        
        # اختبار أحجام مختلفة
        test_sizes = [8, 10, 12, 14, 16, 18, 20, 24, 28, 32]
        
        print("حجم الخط | ارتفاع الحقل | نسبة التكيف")
        print("-" * 45)
        
        for size in test_sizes:
            settings.set_setting('font_size', size)
            field_height = font_manager.get_field_height()
            ratio = field_height / size
            print(f"{size:8}pt | {field_height:11}px | {ratio:10.2f}")
            
        # اختبار خطوط مختلفة
        test_fonts = ['Arial', 'Tahoma', 'Calibri', 'Times New Roman']
        
        print(f"\n📝 اختبار الخطوط المختلفة (حجم {original_size}pt):")
        print("-" * 40)
        
        for font_family in test_fonts:
            settings.set_setting('font_family', font_family)
            field_height = font_manager.get_field_height()
            print(f"{font_family:15} | {field_height}px")
            
        # إعادة الإعدادات الأصلية
        settings.set_setting('font_size', original_size)
        settings.set_setting('font_family', original_family)
        
        print("✅ تم اختبار جميع الأحجام والخطوط بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأحجام: {str(e)}")
        return False

def test_cutting_payments_feature():
    """اختبار ميزة دفعات النشر"""
    print("\n💰 اختبار ميزة دفعات النشر")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.cutting_payment_dialog import CuttingPaymentDialog
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء نافذة دفعات النشر
        dialog = CuttingPaymentDialog(customer_id=1)
        print("✅ تم إنشاء نافذة دفعات النشر بنجاح")
        
        # التحقق من العناصر الأساسية
        if hasattr(dialog, 'customer_name_label'):
            print("✅ عنصر اسم العميل موجود")
        if hasattr(dialog, 'operations_table'):
            print("✅ جدول العمليات موجود")
        if hasattr(dialog, 'payment_amount'):
            print("✅ حقل مبلغ الدفعة موجود")
        if hasattr(dialog, 'payment_method'):
            print("✅ قائمة طرق الدفع موجودة")
            
        # تنظيف
        dialog.close()
        app.quit()
        
        print("✅ جميع عناصر نافذة دفعات النشر تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دفعات النشر: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار الخطوط المتجاوبة ودفعات النشر")
    print("=" * 60)
    
    # تشغيل جميع الاختبارات
    font_test = test_font_responsiveness()
    dialog_test = test_dialog_font_application()
    size_test = test_different_font_sizes()
    cutting_test = test_cutting_payments_feature()
    
    # تقرير النتائج
    print("\n📊 تقرير الاختبار:")
    print("=" * 30)
    print(f"اختبار الخطوط المتجاوبة: {'✅ نجح' if font_test else '❌ فشل'}")
    print(f"اختبار تطبيق الخطوط: {'✅ نجح' if isinstance(dialog_test, dict) and len(dialog_test) > 0 else '❌ فشل'}")
    print(f"اختبار أحجام الخطوط: {'✅ نجح' if size_test else '❌ فشل'}")
    print(f"اختبار دفعات النشر: {'✅ نجح' if cutting_test else '❌ فشل'}")
    
    if isinstance(dialog_test, dict):
        print("\nتفاصيل اختبار النوافذ:")
        for dialog_name, result in dialog_test.items():
            status = "✅ نجح" if result else "❌ فشل"
            print(f"  - {dialog_name}: {status}")
    
    # حساب النتيجة الإجمالية
    total_tests = 4
    passed_tests = sum([
        font_test,
        isinstance(dialog_test, dict) and len(dialog_test) > 0,
        size_test,
        cutting_test
    ])
    
    percentage = (passed_tests / total_tests) * 100
    print(f"\n🎯 النتيجة الإجمالية: {passed_tests}/{total_tests} ({percentage:.1f}%)")
    
    if percentage >= 75:
        print("🎉 ممتاز! الخطوط المتجاوبة ودفعات النشر تعمل بشكل صحيح")
        return 0
    else:
        print("⚠️ يحتاج بعض الإصلاحات")
        return 1

if __name__ == "__main__":
    sys.exit(main())
