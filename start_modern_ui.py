#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام مع الواجهة الحديثة المطبقة على جميع الأقسام
Start System with Modern UI Applied to All Sections
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل النظام مع الواجهة الحديثة"""
    print("🎨 نظام إدارة مصنع الجرانيت - الواجهة الحديثة")
    print("=" * 55)
    print("✨ جميع الأقسام محدثة بواجهة حديثة واحترافية")
    print()
    
    try:
        # استيراد المكونات الأساسية
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("نظام إدارة مصنع الجرانيت - الحسن ستون")
        app.setApplicationVersion("3.0 - Modern UI")
        
        # إعداد الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم تحميل PyQt5 وإعداد التطبيق")
        
        # بيانات مستخدم افتراضية
        user_data = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        # تحميل النافذة الرئيسية
        try:
            from src.ui.main_window import MainWindow
            
            print("✅ تم تحميل النافذة الرئيسية")
            
            # إنشاء وعرض النافذة
            main_window = MainWindow(user_data)
            main_window.show()
            
            print("🎉 تم تشغيل النظام بنجاح!")
            print()
            print("🎨 الواجهات المحدثة:")
            print("  💰 المبيعات والفواتير - واجهة حديثة مع تبويبات")
            print("  💸 المصاريف - إحصائيات ملونة وجداول محسنة")
            print("  📋 الطلبات والحجوزات - تصميم أنيق مع حالات ملونة")
            print("  📈 التقارير - بطاقات تقارير منظمة")
            print("  🚛 الجرارات - إدارة محسنة مع إحصائيات")
            print("  ⚡ التقطيع ودفعات النشر - نظام متكامل")
            print("  📊 لوحة التحكم - إحصائيات تفاعلية")
            print()
            print("✨ جميع الواجهات تتميز بـ:")
            print("  🎨 ألوان متدرجة حديثة")
            print("  📱 تصميم متجاوب")
            print("  ⚡ أزرار سريعة")
            print("  🔍 بحث وفلترة")
            print("  📊 إحصائيات مباشرة")
            
            # عرض رسالة ترحيب
            QMessageBox.information(None, "مرحباً بك", 
                                   "🎉 مرحباً بك في النظام المحدث!\n\n"
                                   "✨ تم تطبيق الواجهة الحديثة على جميع الأقسام\n"
                                   "🎨 تصميم احترافي مع ألوان متدرجة\n"
                                   "📱 واجهة متجاوبة وسهلة الاستخدام\n\n"
                                   "استمتع بالتجربة الجديدة!")
            
            return app.exec_()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل النافذة الرئيسية: {str(e)}")
            print("\n💡 تأكد من وجود ملفات النظام في مجلد src/")
            return 1
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt5: {str(e)}")
        print("\n💡 لحل المشكلة:")
        print("1. تثبيت PyQt5: pip install PyQt5")
        print("2. أو: pip install -r requirements.txt")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
