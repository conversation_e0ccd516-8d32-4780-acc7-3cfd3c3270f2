#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🚀 إصلاح مباشر للأخطاء")
print("=" * 30)

# 1. إنشاء ملفات __init__.py
init_files = [
    "src/__init__.py",
    "src/models/__init__.py", 
    "src/ui/__init__.py",
    "src/database/__init__.py",
    "src/utils/__init__.py"
]

for init_file in init_files:
    if not os.path.exists(init_file):
        os.makedirs(os.path.dirname(init_file), exist_ok=True)
        with open(init_file, 'w') as f:
            f.write('# -*- coding: utf-8 -*-\n')
        print(f"✅ تم إنشاء {init_file}")

# 2. اختبار الاستيرادات
try:
    import config
    print("✅ config")
except Exception as e:
    print(f"❌ config: {e}")

try:
    from src.database.database_manager import DatabaseManager
    print("✅ DatabaseManager")
except Exception as e:
    print(f"❌ DatabaseManager: {e}")

try:
    from src.utils.font_utils import FontManager
    print("✅ FontManager")
except Exception as e:
    print(f"❌ FontManager: {e}")

# 3. اختبار قاعدة البيانات
try:
    db = DatabaseManager()
    if db.connect():
        print("✅ قاعدة البيانات متصلة")
        db.disconnect()
    else:
        print("⚠️ قاعدة البيانات (SQLite)")
except Exception as e:
    print(f"❌ قاعدة البيانات: {e}")

print("\n🎉 انتهى الإصلاح المباشر")
