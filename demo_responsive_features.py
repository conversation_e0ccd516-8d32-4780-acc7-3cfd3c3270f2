#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي للميزات المتجاوبة الجديدة
Responsive Features Demo
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_responsive_fonts():
    """عرض توضيحي للخطوط المتجاوبة"""
    print("🎨 عرض توضيحي: الخطوط المتجاوبة")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.models.settings import Settings
        from src.ui.customer_dialog import CustomerDialog
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        settings = Settings()
        original_size = settings.get_setting('font_size')
        
        print(f"الحجم الأصلي للخط: {original_size} نقطة")
        print("\nسيتم عرض نافذة العملاء بأحجام خطوط مختلفة...")
        print("(ستظهر النوافذ لثوانٍ قليلة ثم تختفي تلقائياً)")
        
        # عرض بأحجام مختلفة
        demo_sizes = [10, 14, 18, 24]
        
        for size in demo_sizes:
            print(f"\n📏 عرض بحجم خط {size} نقطة...")
            
            # تغيير حجم الخط
            settings.set_setting('font_size', size)
            
            # إنشاء وعرض النافذة
            dialog = CustomerDialog()
            dialog.show()
            
            # انتظار قصير للعرض
            app.processEvents()
            import time
            time.sleep(2)
            
            dialog.close()
            print(f"✅ تم عرض النافذة بحجم {size} نقطة")
            
        # إعادة الحجم الأصلي
        settings.set_setting('font_size', original_size)
        
        print(f"\n✅ تم إعادة حجم الخط إلى {original_size} نقطة")
        print("🎉 انتهى العرض التوضيحي للخطوط المتجاوبة!")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {str(e)}")
        return False

def demo_cutting_payments():
    """عرض توضيحي لدفعات النشر"""
    print("\n💰 عرض توضيحي: دفعات النشر للعملاء")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.cutting_payment_dialog import CuttingPaymentDialog
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        print("سيتم عرض نافذة دفعات النشر...")
        print("(النافذة ستظهر لمدة 5 ثوانٍ)")
        
        # إنشاء وعرض نافذة دفعات النشر
        dialog = CuttingPaymentDialog(customer_id=1)
        dialog.show()
        
        print("✅ تم عرض نافذة دفعات النشر")
        print("📋 المكونات المعروضة:")
        print("   - معلومات العميل")
        print("   - جدول عمليات النشر")
        print("   - الإجماليات المالية")
        print("   - نموذج إضافة دفعة جديدة")
        
        # انتظار للعرض
        app.processEvents()
        import time
        time.sleep(5)
        
        dialog.close()
        print("✅ تم إغلاق النافذة")
        print("🎉 انتهى العرض التوضيحي لدفعات النشر!")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {str(e)}")
        return False

def demo_font_scaling():
    """عرض توضيحي لتدرج أحجام الخطوط"""
    print("\n📊 عرض توضيحي: تدرج أحجام الحقول")
    print("=" * 50)
    
    try:
        from src.utils.font_utils import FontManager
        from src.models.settings import Settings
        
        settings = Settings()
        font_manager = FontManager()
        
        print("جدول تدرج أحجام الحقول حسب حجم الخط:")
        print("-" * 45)
        print("حجم الخط | ارتفاع الحقل | الفئة")
        print("-" * 45)
        
        test_sizes = [8, 10, 12, 14, 16, 18, 20, 24, 28, 32]
        
        for size in test_sizes:
            settings.set_setting('font_size', size)
            height = font_manager.get_field_height()
            
            # تحديد الفئة
            if size <= 10:
                category = "صغير"
            elif size <= 14:
                category = "متوسط"
            elif size <= 20:
                category = "كبير"
            else:
                category = "كبير جداً"
                
            print(f"{size:8}pt | {height:11}px | {category}")
            
        print("-" * 45)
        print("✅ يتكيف ارتفاع الحقول تلقائياً مع حجم الخط")
        print("🎯 ضمان قابلية القراءة في جميع الأحجام")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {str(e)}")
        return False

def show_features_summary():
    """عرض ملخص الميزات الجديدة"""
    print("\n🌟 ملخص الميزات الجديدة")
    print("=" * 50)
    
    features = [
        "🔤 خطوط متجاوبة تلقائياً",
        "📏 حقول تتكيف مع حجم الخط",
        "🎨 أنماط CSS ديناميكية",
        "💰 نظام دفعات النشر للعملاء",
        "📊 تتبع عمليات النشر والدفعات",
        "🔧 أدوات إدارة الخطوط المتقدمة",
        "✅ اختبارات شاملة للجودة",
        "📱 واجهات سهلة الاستخدام"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i:2}. {feature}")
        
    print("\n🎯 الفوائد:")
    print("   • تجربة مستخدم محسنة")
    print("   • إمكانية وصول أفضل")
    print("   • مرونة في التخصيص")
    print("   • إدارة مالية دقيقة")
    
    print("\n📋 طرق الاستخدام:")
    print("   • الإعدادات → المظهر والخط → تغيير الحجم")
    print("   • المبيعات → اختيار عميل → دفعات النشر")
    print("   • التقطيع → دفعات العملاء")

def main():
    """الدالة الرئيسية للعرض التوضيحي"""
    print("🚀 عرض توضيحي شامل للميزات الجديدة")
    print("=" * 60)
    print("سيتم عرض الميزات الجديدة التالية:")
    print("1. الخطوط المتجاوبة")
    print("2. دفعات النشر للعملاء")
    print("3. تدرج أحجام الحقول")
    print("=" * 60)
    
    # تشغيل العروض التوضيحية
    font_demo = demo_responsive_fonts()
    cutting_demo = demo_cutting_payments()
    scaling_demo = demo_font_scaling()
    
    # عرض الملخص
    show_features_summary()
    
    # تقرير النتائج
    print("\n📊 تقرير العرض التوضيحي:")
    print("=" * 40)
    print(f"عرض الخطوط المتجاوبة: {'✅ نجح' if font_demo else '❌ فشل'}")
    print(f"عرض دفعات النشر: {'✅ نجح' if cutting_demo else '❌ فشل'}")
    print(f"عرض تدرج الأحجام: {'✅ نجح' if scaling_demo else '❌ فشل'}")
    
    success_count = sum([font_demo, cutting_demo, scaling_demo])
    print(f"\nالنتيجة الإجمالية: {success_count}/3")
    
    if success_count == 3:
        print("🎉 جميع العروض التوضيحية نجحت!")
        print("✨ النظام جاهز للاستخدام مع الميزات الجديدة")
    else:
        print("⚠️ بعض العروض التوضيحية واجهت مشاكل")
        
    print("\n" + "=" * 60)
    print("🏁 انتهى العرض التوضيحي")
    print("💡 لتجربة الميزات: python main.py")
    
    return 0 if success_count == 3 else 1

if __name__ == "__main__":
    sys.exit(main())
