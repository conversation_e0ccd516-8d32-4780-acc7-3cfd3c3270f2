# -*- coding: utf-8 -*-
"""
لوحة التحكم المحسنة
Enhanced Dashboard Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QPushButton, QProgressBar)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class DashboardWidget(QWidget):
    """ويدجت لوحة التحكم المحسنة"""
    
    def __init__(self, database_manager=None, user_data=None):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)
        
        # رسالة ترحيب
        self.create_welcome_section(layout)
        
        # بطاقات الإحصائيات
        self.create_stats_cards(layout)
        
        # الرسوم البيانية والتقارير السريعة
        self.create_charts_section(layout)
        
        # الأنشطة الأخيرة
        self.create_recent_activities(layout)
        
        self.setLayout(layout)
        
    def create_welcome_section(self, layout):
        """إنشاء قسم الترحيب"""
        welcome_frame = QFrame()
        welcome_frame.setObjectName("welcomeFrame")
        welcome_frame.setStyleSheet("""
            QFrame#welcomeFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        welcome_layout = QVBoxLayout()
        
        # رسالة ترحيب
        welcome_label = QLabel("مرحباً بك في نظام إدارة مصنع الجرانيت")
        welcome_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
        welcome_label.setAlignment(Qt.AlignCenter)
        
        # معلومات سريعة
        info_label = QLabel("إدارة شاملة لجميع عمليات المصنع")
        info_label.setStyleSheet("color: white; font-size: 14px; margin-top: 10px;")
        info_label.setAlignment(Qt.AlignCenter)
        
        welcome_layout.addWidget(welcome_label)
        welcome_layout.addWidget(info_label)
        welcome_frame.setLayout(welcome_layout)
        
        layout.addWidget(welcome_frame)
        
    def create_stats_cards(self, layout):
        """إنشاء بطاقات الإحصائيات"""
        stats_layout = QGridLayout()
        
        # بيانات الإحصائيات
        stats_data = [
            ("🚛", "الجرارات", "25", "جرار نشط"),
            ("🧱", "البلوكات", "150", "بلوك متاح"),
            ("💰", "المبيعات", "50,000", "ريال هذا الشهر"),
            ("📋", "الطلبات", "12", "طلب قيد التنفيذ"),
            ("👥", "العملاء", "85", "عميل نشط"),
            ("📈", "الأرباح", "15,000", "ريال صافي")
        ]
        
        for i, (icon, title, value, subtitle) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, subtitle)
            row = i // 3
            col = i % 3
            stats_layout.addWidget(card, row, col)
            
        layout.addLayout(stats_layout)
        
    def create_stat_card(self, icon, title, value, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setStyleSheet("""
            QFrame#statCard {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                padding: 15px;
            }
            QFrame#statCard:hover {
                border-color: #3498db;
                box-shadow: 0 2px 10px rgba(52, 152, 219, 0.1);
            }
        """)
        
        layout = QVBoxLayout()
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #3498db; margin: 10px 0;")
        
        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        
        card.setLayout(layout)
        return card
        
    def create_charts_section(self, layout):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = QFrame()
        charts_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        charts_layout = QVBoxLayout()
        
        # عنوان القسم
        title = QLabel("📈 الرسوم البيانية والتقارير")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        charts_layout.addWidget(title)
        
        # محتوى مؤقت
        content = QLabel("الرسوم البيانية قيد التطوير...")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("font-size: 14px; color: #7f8c8d; padding: 50px;")
        charts_layout.addWidget(content)
        
        charts_frame.setLayout(charts_layout)
        layout.addWidget(charts_frame)
        
    def create_recent_activities(self, layout):
        """إنشاء قسم الأنشطة الأخيرة"""
        activities_frame = QFrame()
        activities_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        activities_layout = QVBoxLayout()
        
        # عنوان القسم
        title = QLabel("🕒 الأنشطة الأخيرة")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        activities_layout.addWidget(title)
        
        # قائمة الأنشطة
        activities = [
            "تم إضافة جرار جديد رقم TR-001",
            "تم إنشاء فاتورة مبيعات INV-2025-001",
            "تم تسجيل دفعة من العميل أحمد محمد",
            "تم بدء عملية نشر للبلوك BL-150"
        ]
        
        for activity in activities:
            activity_label = QLabel(f"• {activity}")
            activity_label.setStyleSheet("font-size: 13px; color: #2c3e50; padding: 5px 0;")
            activities_layout.addWidget(activity_label)
            
        activities_frame.setLayout(activities_layout)
        layout.addWidget(activities_frame)
        
    def load_data(self):
        """تحميل البيانات"""
        # سيتم تطوير هذه الدالة لتحميل البيانات الفعلية
        pass
