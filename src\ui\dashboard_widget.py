# -*- coding: utf-8 -*-
"""
ويدجت لوحة التحكم
Dashboard Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QFrame, QGridLayout, QPushButton, QScrollArea,
                            QProgressBar, QTextEdit)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap

import config
from datetime import datetime, timedelta

class DashboardWidget(QWidget):
    """ويدجت لوحة التحكم الرئيسية"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.setup_ui()
        self.load_data()
        
        # تحديث البيانات كل 5 دقائق
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_data)
        self.refresh_timer.start(300000)  # 5 دقائق
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان لوحة التحكم
        self.create_header(main_layout)
        
        # البطاقات الإحصائية
        self.create_stats_cards(main_layout)

        # بطاقات الأقسام الرئيسية
        self.create_sections_cards(main_layout)

        # الرسوم البيانية والتقارير السريعة
        self.create_charts_section(main_layout)
        
        # الأنشطة الأخيرة
        self.create_recent_activities(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس لوحة التحكم"""
        header_frame = QFrame()
        header_layout = QHBoxLayout()
        
        # العنوان والترحيب
        welcome_layout = QVBoxLayout()
        
        title_label = QLabel("لوحة التحكم")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 18, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        welcome_label = QLabel(f"مرحباً {self.user_data['full_name']}")
        welcome_label.setStyleSheet("color: #7F8C8D; font-size: 12px;")
        
        date_label = QLabel(datetime.now().strftime("%A, %d %B %Y"))
        date_label.setStyleSheet("color: #7F8C8D; font-size: 11px;")
        
        welcome_layout.addWidget(title_label)
        welcome_layout.addWidget(welcome_label)
        welcome_layout.addWidget(date_label)
        
        # زر التحديث
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.clicked.connect(self.load_data)
        refresh_button.setMaximumWidth(120)
        
        header_layout.addLayout(welcome_layout)
        header_layout.addStretch()
        header_layout.addWidget(refresh_button)
        
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
    def create_stats_cards(self, layout):
        """إنشاء البطاقات الإحصائية"""
        stats_frame = QFrame()
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        
        # بيانات البطاقات
        self.stats_data = [
            {"title": "إجمالي المبيعات", "value": "0", "unit": "جنيه", "color": config.COLORS['success']},
            {"title": "عدد الفواتير", "value": "0", "unit": "فاتورة", "color": config.COLORS['secondary']},
            {"title": "المخزون المتاح", "value": "0", "unit": "متر مربع", "color": config.COLORS['warning']},
            {"title": "الجرارات المستلمة", "value": "0", "unit": "جرار", "color": config.COLORS['primary']},
            {"title": "البلوكات المتاحة", "value": "0", "unit": "بلوك", "color": config.COLORS['dark']},
            {"title": "إجمالي المصروفات", "value": "0", "unit": "جنيه", "color": config.COLORS['danger']}
        ]
        
        self.stats_cards = []
        
        for i, stat in enumerate(self.stats_data):
            card = self.create_stat_card(stat)
            self.stats_cards.append(card)
            
            row = i // 3
            col = i % 3
            stats_layout.addWidget(card, row, col)
            
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)

    def create_sections_cards(self, layout):
        """إنشاء بطاقات الأقسام الرئيسية"""
        sections_frame = QFrame()
        sections_layout = QGridLayout()
        sections_layout.setSpacing(15)

        # بيانات الأقسام
        sections_data = [
            {"title": "إدارة الشاحنات", "desc": "متابعة الشاحنات والرحلات", "icon": "🚛", "section": "trucks"},
            {"title": "عمليات التقطيع", "desc": "إدارة عمليات تقطيع الكتل", "icon": "⚙️", "section": "cutting"},
            {"title": "إدارة المبيعات", "desc": "الفواتير والعملاء", "icon": "💰", "section": "sales"},
            {"title": "إدارة المصاريف", "desc": "متابعة المصاريف التشغيلية", "icon": "📊", "section": "expenses"},
            {"title": "إدارة الطلبات", "desc": "الطلبات والحجوزات", "icon": "📋", "section": "orders"},
            {"title": "التقارير", "desc": "التقارير والإحصائيات", "icon": "📈", "section": "reports"}
        ]

        for i, section in enumerate(sections_data):
            card = self.create_section_card(section)

            row = i // 3
            col = i % 3
            sections_layout.addWidget(card, row, col)

        sections_frame.setLayout(sections_layout)
        layout.addWidget(sections_frame)

    def create_section_card(self, section_data):
        """إنشاء بطاقة قسم واحد"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setMinimumHeight(100)
        card.setCursor(Qt.PointingHandCursor)

        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(section_data["icon"])
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px; margin-bottom: 5px;")

        # العنوان
        title_label = QLabel(section_data["title"])
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")

        # الوصف
        desc_label = QLabel(section_data["desc"])
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #7F8C8D; font-size: 10px;")
        desc_label.setWordWrap(True)

        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)

        card.setLayout(layout)

        # إضافة حدث النقر
        def on_click():
            self.parent().switch_section(section_data["section"])

        card.mousePressEvent = lambda event: on_click()

        # تأثير التمرير
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #E8E8E8;
                border-radius: 8px;
                padding: 10px;
            }}
            QFrame:hover {{
                border: 2px solid {config.COLORS['secondary']};
                background-color: #F8F9FA;
            }}
        """)

        return card
        
    def create_stat_card(self, stat_data):
        """إنشاء بطاقة إحصائية واحدة"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setMinimumHeight(120)
        
        card_layout = QVBoxLayout()
        card_layout.setSpacing(10)
        card_layout.setContentsMargins(15, 15, 15, 15)
        
        # العنوان
        title_label = QLabel(stat_data['title'])
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 10))
        title_label.setStyleSheet("color: #7F8C8D;")
        
        # القيمة
        value_label = QLabel(stat_data['value'])
        value_label.setFont(QFont(config.UI_CONFIG['font_family'], 20, QFont.Bold))
        value_label.setStyleSheet(f"color: {stat_data['color']};")
        value_label.setAlignment(Qt.AlignCenter)
        
        # الوحدة
        unit_label = QLabel(stat_data['unit'])
        unit_label.setFont(QFont(config.UI_CONFIG['font_family'], 9))
        unit_label.setStyleSheet("color: #95A5A6;")
        unit_label.setAlignment(Qt.AlignCenter)
        
        card_layout.addWidget(title_label)
        card_layout.addWidget(value_label)
        card_layout.addWidget(unit_label)
        
        card.setLayout(card_layout)
        
        # تخزين مراجع للتحديث
        card.value_label = value_label
        card.stat_key = stat_data['title']
        
        return card
        
    def create_charts_section(self, layout):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = QFrame()
        charts_frame.setFrameStyle(QFrame.StyledPanel)
        charts_layout = QHBoxLayout()
        
        # رسم بياني للمبيعات الشهرية
        sales_chart = self.create_sales_chart()
        charts_layout.addWidget(sales_chart)
        
        # رسم بياني للمخزون
        inventory_chart = self.create_inventory_chart()
        charts_layout.addWidget(inventory_chart)
        
        charts_frame.setLayout(charts_layout)
        layout.addWidget(charts_frame)
        
    def create_sales_chart(self):
        """إنشاء رسم بياني للمبيعات"""
        chart_frame = QFrame()
        chart_layout = QVBoxLayout()
        
        title_label = QLabel("المبيعات الشهرية")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        
        # رسم بياني مؤقت (نص)
        chart_placeholder = QLabel("رسم بياني للمبيعات\n(سيتم تطويره لاحقاً)")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setMinimumHeight(200)
        chart_placeholder.setStyleSheet("background-color: #ECF0F1; border: 1px dashed #BDC3C7;")
        
        chart_layout.addWidget(title_label)
        chart_layout.addWidget(chart_placeholder)
        
        chart_frame.setLayout(chart_layout)
        return chart_frame
        
    def create_inventory_chart(self):
        """إنشاء رسم بياني للمخزون"""
        chart_frame = QFrame()
        chart_layout = QVBoxLayout()
        
        title_label = QLabel("حالة المخزون")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        
        # رسم بياني مؤقت (نص)
        chart_placeholder = QLabel("رسم بياني للمخزون\n(سيتم تطويره لاحقاً)")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setMinimumHeight(200)
        chart_placeholder.setStyleSheet("background-color: #ECF0F1; border: 1px dashed #BDC3C7;")
        
        chart_layout.addWidget(title_label)
        chart_layout.addWidget(chart_placeholder)
        
        chart_frame.setLayout(chart_layout)
        return chart_frame
        
    def create_recent_activities(self, layout):
        """إنشاء قسم الأنشطة الأخيرة"""
        activities_frame = QFrame()
        activities_frame.setFrameStyle(QFrame.StyledPanel)
        activities_layout = QVBoxLayout()
        
        title_label = QLabel("الأنشطة الأخيرة")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        
        # قائمة الأنشطة
        self.activities_text = QTextEdit()
        self.activities_text.setMaximumHeight(150)
        self.activities_text.setReadOnly(True)
        
        activities_layout.addWidget(title_label)
        activities_layout.addWidget(self.activities_text)
        
        activities_frame.setLayout(activities_layout)
        layout.addWidget(activities_frame)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {config.COLORS['white']};
                border: 1px solid #BDC3C7;
                border-radius: 8px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QTextEdit {{
                border: 1px solid #BDC3C7;
                border-radius: 5px;
                background-color: #FAFAFA;
            }}
        """)
        
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحديث البطاقات الإحصائية
            self.update_sales_stats()
            self.update_inventory_stats()
            self.update_trucks_stats()
            self.update_expenses_stats()
            
            # تحديث الأنشطة الأخيرة
            self.update_recent_activities()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")
            
    def update_sales_stats(self):
        """تحديث إحصائيات المبيعات"""
        try:
            # إجمالي المبيعات هذا الشهر
            query = """
            SELECT COALESCE(SUM(total_amount), 0) as total_sales,
                   COUNT(*) as invoice_count
            FROM sales_invoices 
            WHERE MONTH(invoice_date) = MONTH(GETDATE()) 
            AND YEAR(invoice_date) = YEAR(GETDATE())
            """
            result = self.database_manager.execute_query(query, fetch='one')
            
            if result:
                total_sales, invoice_count = result
                self.update_stat_card("إجمالي المبيعات", f"{total_sales:,.0f}")
                self.update_stat_card("عدد الفواتير", str(invoice_count))
                
        except Exception as e:
            print(f"خطأ في تحديث إحصائيات المبيعات: {str(e)}")
            
    def update_inventory_stats(self):
        """تحديث إحصائيات المخزون"""
        try:
            # إجمالي المساحة المتاحة
            query = """
            SELECT COALESCE(SUM(area_sqm), 0) as total_area
            FROM slices 
            WHERE status = 'available'
            """
            result = self.database_manager.execute_query(query, fetch='one')
            
            if result:
                total_area = result[0]
                self.update_stat_card("المخزون المتاح", f"{total_area:,.1f}")
                
            # عدد البلوكات المتاحة
            query = """
            SELECT COUNT(*) as available_blocks
            FROM blocks 
            WHERE status = 'available'
            """
            result = self.database_manager.execute_query(query, fetch='one')
            
            if result:
                available_blocks = result[0]
                self.update_stat_card("البلوكات المتاحة", str(available_blocks))
                
        except Exception as e:
            print(f"خطأ في تحديث إحصائيات المخزون: {str(e)}")
            
    def update_trucks_stats(self):
        """تحديث إحصائيات الجرارات"""
        try:
            # عدد الجرارات المستلمة هذا الشهر
            query = """
            SELECT COUNT(*) as truck_count
            FROM trucks 
            WHERE MONTH(arrival_date) = MONTH(GETDATE()) 
            AND YEAR(arrival_date) = YEAR(GETDATE())
            """
            result = self.database_manager.execute_query(query, fetch='one')
            
            if result:
                truck_count = result[0]
                self.update_stat_card("الجرارات المستلمة", str(truck_count))
                
        except Exception as e:
            print(f"خطأ في تحديث إحصائيات الجرارات: {str(e)}")
            
    def update_expenses_stats(self):
        """تحديث إحصائيات المصروفات"""
        try:
            # إجمالي المصروفات هذا الشهر
            query = """
            SELECT COALESCE(SUM(amount), 0) as total_expenses
            FROM expenses 
            WHERE MONTH(expense_date) = MONTH(GETDATE()) 
            AND YEAR(expense_date) = YEAR(GETDATE())
            """
            result = self.database_manager.execute_query(query, fetch='one')
            
            if result:
                total_expenses = result[0]
                self.update_stat_card("إجمالي المصروفات", f"{total_expenses:,.0f}")
                
        except Exception as e:
            print(f"خطأ في تحديث إحصائيات المصروفات: {str(e)}")
            
    def update_stat_card(self, title, value):
        """تحديث قيمة بطاقة إحصائية"""
        for card in self.stats_cards:
            if hasattr(card, 'stat_key') and card.stat_key == title:
                card.value_label.setText(value)
                break
                
    def update_recent_activities(self):
        """تحديث الأنشطة الأخيرة"""
        try:
            query = """
            SELECT TOP 10 u.full_name, ul.action, ul.created_at
            FROM user_logs ul
            JOIN users u ON ul.user_id = u.id
            ORDER BY ul.created_at DESC
            """
            results = self.database_manager.execute_query(query, fetch='all')
            
            activities_text = ""
            if results:
                for full_name, action, created_at in results:
                    time_str = created_at.strftime("%H:%M")
                    activities_text += f"• {time_str} - {full_name}: {action}\n"
            else:
                activities_text = "لا توجد أنشطة حديثة"
                
            self.activities_text.setPlainText(activities_text)
            
        except Exception as e:
            print(f"خطأ في تحديث الأنشطة الأخيرة: {str(e)}")
            self.activities_text.setPlainText("خطأ في تحميل الأنشطة")
