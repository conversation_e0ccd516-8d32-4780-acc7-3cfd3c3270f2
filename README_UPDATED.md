# نظام إدارة مصنع الجرانيت - الإصدار المحدث 🏭

نظام إدارة شامل ومتطور لمصانع الجرانيت والرخام مع واجهات تفاعلية متقدمة ونظام إعدادات شامل.

## الميزات الجديدة والمحدثة ✨

### 🎨 نظام الإعدادات الشامل
- **تخصيص الخطوط**: 9 خطوط مختلفة (عربي وإنجليزي)
- **الثيمات**: 5 ثيمات جاهزة + تخصيص يدوي
- **إعدادات النوافذ**: أبعاد مخصصة وتكبير تلقائي
- **إعدادات الأمان**: مهل زمنية وكلمات مرور
- **تصدير/استيراد**: نسخ احتياطية للإعدادات

### 💰 نظام المبيعات المتطور
- **نوافذ إنشاء الفواتير**: واجهة تفاعلية كاملة
- **إدارة العملاء**: نوافذ إضافة وتعديل متقدمة
- **نظام الدفعات**: إضافة ومتابعة الدفعات
- **عرض الفواتير**: تفاصيل شاملة مع الإجماليات
- **حساب تلقائي**: للمساحات والأسعار

### 📋 إدارة الطلبات التفاعلية
- **نوافذ إدخال متطورة**: حساب تلقائي للتكاليف
- **اختيار العملاء**: قوائم منسدلة تفاعلية
- **حساب المساحة**: تلقائي بناءً على الأبعاد
- **تواريخ ذكية**: تواريخ افتراضية منطقية

### 🎨 إدارة أنواع الجرانيت
- **واجهة إدارة شاملة**: إضافة وتعديل وحذف
- **الأسعار الافتراضية**: لكل نوع جرانيت
- **إحصائيات متقدمة**: عدد الأنواع ومتوسط الأسعار
- **تحديث فوري**: للقوائم في جميع النوافذ

### 👥 إدارة المستخدمين المتقدمة
- **واجهة إدارة كاملة**: إضافة وتعديل المستخدمين
- **إعادة تعيين كلمات المرور**: بأمان
- **إحصائيات المستخدمين**: نشطين ومديرين
- **حماية متقدمة**: منع حذف المستخدم الحالي

### 🏠 لوحة التحكم المحدثة
- **بطاقات تفاعلية**: وصول سريع للأقسام
- **إحصائيات فورية**: تحديث تلقائي
- **تصميم عصري**: ألوان وأيقونات محدثة

## الميزات الأساسية 🔧

### 🚛 إدارة الشاحنات
- تسجيل الشاحنات والسائقين
- متابعة الرحلات والتسليمات
- حساب تكاليف النقل

### ⚙️ عمليات التقطيع
- إدارة عمليات نشر الجرانيت
- تتبع الكتل والشرائح
- حساب الفاقد والإنتاجية

### 📊 إدارة المصاريف
- تسجيل المصاريف التشغيلية
- تصنيف المصاريف
- تقارير مالية مفصلة

### 📈 التقارير
- تقارير مالية شاملة
- تقارير الإنتاج
- تحليل الأداء

## التثبيت والتشغيل 🚀

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل النظام
```bash
# التشغيل العادي
python main.py

# أو التشغيل الشامل
python run_complete_system.py
```

### 3. اختبار النظام
```bash
# اختبار الإعدادات
python test_settings.py

# اختبار شامل لجميع الواجهات
python test_complete_ui.py
```

### 4. تسجيل الدخول
- **المدير**: `admin` / `admin123`
- **المبيعات**: `sales` / `sales123`

## طرق الوصول للإعدادات ⚙️

1. **من القائمة الرئيسية**: ملف → الإعدادات
2. **من شريط الأدوات**: زر الإعدادات
3. **من لوحة التحكم**: بطاقة الإعدادات
4. **اختصار لوحة المفاتيح**: Ctrl+, (قيد التطوير)

## الواجهات المتاحة 🖥️

### النوافذ التفاعلية:
- ✅ **نافذة إضافة العملاء**: كاملة ومتطورة
- ✅ **نافذة إنشاء الفواتير**: مع حساب تلقائي
- ✅ **نافذة عرض الفواتير**: تفاصيل شاملة
- ✅ **نافذة إضافة الدفعات**: نظام دفع متقدم
- ✅ **نافذة إضافة الطلبات**: حساب تلقائي للتكاليف
- ✅ **نافذة إدارة أنواع الجرانيت**: إدارة كاملة
- ✅ **نافذة إدارة المستخدمين**: نظام متقدم
- ✅ **نافذة الإعدادات**: تخصيص شامل

### الواجهات الرئيسية:
- ✅ **لوحة التحكم**: محدثة ومطورة
- ✅ **واجهة الشاحنات**: كاملة
- ✅ **واجهة التقطيع**: مع إدارة أنواع الجرانيت
- ✅ **واجهة المبيعات**: متطورة بالكامل
- ✅ **واجهة المصاريف**: كاملة
- ✅ **واجهة الطلبات**: محدثة ومطورة
- ✅ **واجهة التقارير**: شاملة

## نتائج الاختبار الأخيرة 📊

```
🎯 النتيجة الإجمالية: 22/23 (95.7%)
🎉 ممتاز! النظام جاهز للاستخدام

🔹 النوافذ والحوارات: 8/8 (100.0%)
🔹 الواجهات الرئيسية: 7/7 (100.0%)
🔹 النماذج: 7/7 (100.0%)
🔹 قاعدة البيانات: يحتاج إعداد SQL Server
```

## هيكل المشروع المحدث 📁

```
AL-Hassan/
├── main.py                          # الملف الرئيسي
├── config.py                        # إعدادات التطبيق
├── requirements.txt                 # المكتبات المطلوبة
├── settings.json                    # ملف الإعدادات المحفوظة
├── SETTINGS_GUIDE.md               # دليل الإعدادات
├── src/
│   ├── ui/
│   │   ├── main_window.py          # النافذة الرئيسية
│   │   ├── dashboard_widget.py     # لوحة التحكم
│   │   ├── customer_dialog.py      # نافذة العملاء (جديد)
│   │   ├── invoice_dialog.py       # نافذة الفواتير (جديد)
│   │   ├── invoice_view_dialog.py  # عرض الفواتير (جديد)
│   │   ├── payment_dialog.py       # نافذة الدفعات (جديد)
│   │   ├── order_dialog.py         # نافذة الطلبات (جديد)
│   │   ├── granite_types_dialog.py # إدارة أنواع الجرانيت (جديد)
│   │   ├── users_management_dialog.py # إدارة المستخدمين (جديد)
│   │   ├── settings_dialog.py      # نافذة الإعدادات (جديد)
│   │   └── ...
│   ├── models/
│   │   ├── settings.py             # نموذج الإعدادات (جديد)
│   │   └── ...
│   └── ...
├── test_settings.py                # اختبار الإعدادات
├── test_complete_ui.py             # اختبار شامل للواجهات
└── run_complete_system.py         # تشغيل النظام الكامل
```

## المتطلبات 📋

- Windows 10 أو أحدث
- Python 3.8 أو أحدث
- PyQt5
- SQL Server Express (اختياري)
- 4 جيجابايت رام على الأقل
- 500 ميجابايت مساحة فارغة

## الدعم والمساعدة 🆘

- **دليل الإعدادات**: `SETTINGS_GUIDE.md`
- **اختبار النظام**: `python test_complete_ui.py`
- **اختبار الإعدادات**: `python test_settings.py`

---

**تم التطوير بواسطة**: فريق تطوير نظام الحسن لإدارة مصانع الجرانيت
**الإصدار**: 2.0 - محدث ومطور بالكامل
**التاريخ**: 2025
