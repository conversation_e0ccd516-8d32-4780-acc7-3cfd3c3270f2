#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الإعدادات
Settings System Test
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_settings_system():
    """اختبار نظام الإعدادات"""
    print("🧪 اختبار نظام الإعدادات")
    print("=" * 50)
    
    try:
        from src.models.settings import Settings
        
        # إنشاء كائن الإعدادات
        settings = Settings()
        print("✅ تم إنشاء كائن الإعدادات بنجاح")
        
        # اختبار الحصول على الإعدادات
        font_family = settings.get_setting('font_family')
        font_size = settings.get_setting('font_size')
        print(f"✅ نوع الخط الحالي: {font_family}")
        print(f"✅ حجم الخط الحالي: {font_size}")
        
        # اختبار تغيير الإعدادات
        settings.set_setting('font_size', 14)
        new_font_size = settings.get_setting('font_size')
        print(f"✅ تم تغيير حجم الخط إلى: {new_font_size}")
        
        # اختبار حفظ الإعدادات
        success, message = settings.save_settings()
        if success:
            print("✅ تم حفظ الإعدادات بنجاح")
        else:
            print(f"❌ فشل في حفظ الإعدادات: {message}")
            
        # اختبار إعدادات الخط
        font_settings = settings.get_font_settings()
        print(f"✅ إعدادات الخط: {font_settings}")
        
        # اختبار إعدادات الألوان
        color_settings = settings.get_color_settings()
        print(f"✅ إعدادات الألوان: {color_settings}")
        
        # اختبار إعدادات النافذة
        window_settings = settings.get_window_settings()
        print(f"✅ إعدادات النافذة: {window_settings}")
        
        # اختبار التحقق من صحة الإعدادات
        errors = settings.validate_settings()
        if not errors:
            print("✅ جميع الإعدادات صحيحة")
        else:
            print(f"⚠️ أخطاء في الإعدادات: {errors}")
            
        # اختبار الثيمات
        themes = settings.get_themes()
        print(f"✅ الثيمات المتاحة: {list(themes.keys())}")
        
        # اختبار تطبيق ثيم
        success = settings.apply_theme('dark')
        if success:
            print("✅ تم تطبيق الثيم الداكن")
        else:
            print("❌ فشل في تطبيق الثيم")
            
        # اختبار تصدير الإعدادات
        export_success, export_message = settings.export_settings('test_settings_export.json')
        if export_success:
            print("✅ تم تصدير الإعدادات بنجاح")
            
            # اختبار استيراد الإعدادات
            import_success, import_message = settings.import_settings('test_settings_export.json')
            if import_success:
                print("✅ تم استيراد الإعدادات بنجاح")
            else:
                print(f"❌ فشل في استيراد الإعدادات: {import_message}")
                
            # حذف ملف الاختبار
            try:
                os.remove('test_settings_export.json')
                print("✅ تم حذف ملف الاختبار")
            except:
                pass
        else:
            print(f"❌ فشل في تصدير الإعدادات: {export_message}")
            
        print("\n🎉 اكتمل اختبار نظام الإعدادات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {str(e)}")
        return False

def test_settings_ui():
    """اختبار واجهة الإعدادات"""
    print("\n🖥️ اختبار واجهة الإعدادات")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.settings_dialog import SettingsDialog
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء نافذة الإعدادات
        settings_dialog = SettingsDialog()
        print("✅ تم إنشاء نافذة الإعدادات بنجاح")
        
        # عرض النافذة (اختياري - سيتم إغلاقها تلقائياً)
        # settings_dialog.show()
        
        print("✅ واجهة الإعدادات جاهزة للاستخدام")
        
        # تنظيف
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الإعدادات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام الإعدادات الشامل")
    print("=" * 60)
    
    # اختبار نظام الإعدادات
    settings_test = test_settings_system()
    
    # اختبار واجهة الإعدادات
    ui_test = test_settings_ui()
    
    print("\n📊 نتائج الاختبار:")
    print("=" * 30)
    print(f"نظام الإعدادات: {'✅ نجح' if settings_test else '❌ فشل'}")
    print(f"واجهة الإعدادات: {'✅ نجح' if ui_test else '❌ فشل'}")
    
    if settings_test and ui_test:
        print("\n🎉 جميع الاختبارات نجحت! نظام الإعدادات جاهز للاستخدام.")
        return 0
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
