# -*- coding: utf-8 -*-
"""
واجهة إدارة الطلبات والحجوزات
Orders Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QSplitter,
                            QGroupBox, QGridLayout, QComboBox, QSpinBox,
                            QTabWidget)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

import config
from src.models.order import Order
from src.models.customer import Customer
from src.models.granite_type import GraniteType
from src.utils.logger import log_user_action
from src.ui.order_dialog import OrderDialog

class OrdersWidget(QWidget):
    """واجهة إدارة الطلبات والحجوزات"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.order_model = Order(database_manager)
        self.customer_model = Customer(database_manager)
        self.granite_type_model = GraniteType(database_manager)
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان والأزرار
        self.create_header(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس الواجهة"""
        header_layout = QHBoxLayout()
        
        # العنوان
        title_label = QLabel("إدارة الطلبات والحجوزات")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        # الأزرار
        self.new_order_btn = QPushButton("طلب جديد")
        self.new_order_btn.clicked.connect(self.create_new_order)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_data)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.new_order_btn)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
    def create_tabs(self, layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        
        # تبويب جميع الطلبات
        self.all_orders_tab = QWidget()
        self.create_all_orders_tab()
        self.tabs.addTab(self.all_orders_tab, "جميع الطلبات")
        
        # تبويب الطلبات المعلقة
        self.pending_orders_tab = QWidget()
        self.create_pending_orders_tab()
        self.tabs.addTab(self.pending_orders_tab, "الطلبات المعلقة")
        
        # تبويب الطلبات المتأخرة
        self.overdue_orders_tab = QWidget()
        self.create_overdue_orders_tab()
        self.tabs.addTab(self.overdue_orders_tab, "الطلبات المتأخرة")
        
        # تبويب الإحصائيات
        self.stats_tab = QWidget()
        self.create_stats_tab()
        self.tabs.addTab(self.stats_tab, "الإحصائيات")
        
        layout.addWidget(self.tabs)
        
    def create_all_orders_tab(self):
        """إنشاء تبويب جميع الطلبات"""
        layout = QVBoxLayout()
        
        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في الطلبات...")
        self.search_input.textChanged.connect(self.search_orders)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "معلق", "قيد التنفيذ", "مكتمل", "ملغي"])
        self.status_filter.currentTextChanged.connect(self.filter_by_status)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(QLabel("الحالة:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addStretch()
        
        layout.addLayout(search_layout)
        
        # جدول الطلبات
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(11)
        
        headers = ["المعرف", "رقم الطلب", "العميل", "نوع الجرانيت", "تاريخ الطلب",
                  "تاريخ التسليم", "الكمية", "المساحة (م²)", "القيمة", "الحالة", "تاريخ الإنشاء"]
        self.orders_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        # قائمة السياق
        self.orders_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.orders_table.customContextMenuRequested.connect(self.show_orders_context_menu)
        
        # النقر المزدوج للعرض
        self.orders_table.doubleClicked.connect(self.view_order)
        
        layout.addWidget(self.orders_table)
        
        self.all_orders_tab.setLayout(layout)
        
    def create_pending_orders_tab(self):
        """إنشاء تبويب الطلبات المعلقة"""
        layout = QVBoxLayout()
        
        # جدول الطلبات المعلقة
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        
        headers = ["رقم الطلب", "العميل", "نوع الجرانيت", "تاريخ الطلب",
                  "تاريخ التسليم", "الكمية", "القيمة", "الأيام المتبقية"]
        self.pending_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.pending_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.pending_table.setAlternatingRowColors(True)
        self.pending_table.setSortingEnabled(True)
        
        layout.addWidget(self.pending_table)
        
        self.pending_orders_tab.setLayout(layout)
        
    def create_overdue_orders_tab(self):
        """إنشاء تبويب الطلبات المتأخرة"""
        layout = QVBoxLayout()
        
        # تحذير
        warning_label = QLabel("⚠️ الطلبات التالية متأخرة عن موعد التسليم")
        warning_label.setStyleSheet(f"color: {config.COLORS['danger']}; font-weight: bold; padding: 10px;")
        layout.addWidget(warning_label)
        
        # جدول الطلبات المتأخرة
        self.overdue_table = QTableWidget()
        self.overdue_table.setColumnCount(8)
        
        headers = ["رقم الطلب", "العميل", "نوع الجرانيت", "تاريخ الطلب",
                  "تاريخ التسليم", "الكمية", "القيمة", "أيام التأخير"]
        self.overdue_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.overdue_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.overdue_table.setAlternatingRowColors(True)
        self.overdue_table.setSortingEnabled(True)
        
        layout.addWidget(self.overdue_table)
        
        self.overdue_orders_tab.setLayout(layout)
        
    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        layout = QVBoxLayout()
        
        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        
        # إحصائيات عامة
        general_group = QGroupBox("إحصائيات عامة")
        general_layout = QVBoxLayout()
        
        self.total_orders_label = QLabel("إجمالي الطلبات: 0")
        self.pending_orders_label = QLabel("الطلبات المعلقة: 0")
        self.in_progress_orders_label = QLabel("قيد التنفيذ: 0")
        self.completed_orders_label = QLabel("المكتملة: 0")
        self.overdue_orders_label = QLabel("المتأخرة: 0")
        self.total_value_label = QLabel("إجمالي القيمة: 0 جنيه")
        
        general_layout.addWidget(self.total_orders_label)
        general_layout.addWidget(self.pending_orders_label)
        general_layout.addWidget(self.in_progress_orders_label)
        general_layout.addWidget(self.completed_orders_label)
        general_layout.addWidget(self.overdue_orders_label)
        general_layout.addWidget(self.total_value_label)
        general_group.setLayout(general_layout)
        
        stats_layout.addWidget(general_group, 0, 0)
        
        layout.addLayout(stats_layout)
        layout.addStretch()
        
        self.stats_tab.setLayout(layout)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QLineEdit, QComboBox {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
            
            QTabWidget::pane {{
                border: 1px solid #BDC3C7;
                background-color: white;
            }}
            
            QTabBar::tab {{
                background-color: #ECF0F1;
                padding: 8px 15px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_all_orders()
        self.load_pending_orders()
        self.load_overdue_orders()
        self.load_statistics()
        
    def load_all_orders(self):
        """تحميل جميع الطلبات"""
        try:
            orders = self.order_model.get_all_orders()
            self.populate_orders_table(orders)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الطلبات:\n{str(e)}")
            
    def populate_orders_table(self, orders):
        """ملء جدول الطلبات"""
        self.orders_table.setRowCount(len(orders))
        
        for row, order in enumerate(orders):
            items = [
                str(order[0]),  # ID
                str(order[1]),  # order_number
                str(order[13]),  # customer_name
                str(order[14]),  # granite_type_name
                str(order[2])[:10] if order[2] else "",  # order_date
                str(order[3])[:10] if order[3] else "",  # delivery_date
                str(order[7]),  # quantity
                f"{order[16]:.2f}" if order[16] else "0.00",  # total_area
                f"{order[9]:,.2f}" if order[9] else "0.00",  # total_amount
                self.get_status_text(order[10]),  # status
                str(order[12])[:10] if order[12] else ""  # created_at
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 6, 7, 8]:  # أعمدة رقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.orders_table.setItem(row, col, item)
                
    def get_status_text(self, status):
        """تحويل حالة الطلب إلى نص عربي"""
        status_map = {
            'pending': 'معلق',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)
        
    def load_pending_orders(self):
        """تحميل الطلبات المعلقة"""
        try:
            orders = self.order_model.get_orders_by_status('pending')
            # سيتم ملء الجدول هنا
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الطلبات المعلقة:\n{str(e)}")
            
    def load_overdue_orders(self):
        """تحميل الطلبات المتأخرة"""
        try:
            orders = self.order_model.get_overdue_orders()
            # سيتم ملء الجدول هنا
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الطلبات المتأخرة:\n{str(e)}")
            
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            stats = self.order_model.get_order_statistics()
            
            self.total_orders_label.setText(f"إجمالي الطلبات: {stats.get('total_orders', 0)}")
            self.pending_orders_label.setText(f"الطلبات المعلقة: {stats.get('pending_orders', 0)}")
            self.in_progress_orders_label.setText(f"قيد التنفيذ: {stats.get('in_progress_orders', 0)}")
            self.completed_orders_label.setText(f"المكتملة: {stats.get('completed_orders', 0)}")
            self.overdue_orders_label.setText(f"المتأخرة: {stats.get('overdue_orders', 0)}")
            self.total_value_label.setText(f"إجمالي القيمة: {stats.get('total_value', 0):,.2f} جنيه")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الإحصائيات:\n{str(e)}")
            
    def search_orders(self):
        """البحث في الطلبات"""
        search_term = self.search_input.text().strip()
        if search_term:
            orders = self.order_model.search_orders(search_term)
            self.populate_orders_table(orders)
        else:
            self.load_all_orders()
            
    def filter_by_status(self):
        """فلترة حسب الحالة"""
        status_text = self.status_filter.currentText()
        if status_text != "جميع الحالات":
            # تحويل النص العربي إلى حالة إنجليزية
            status_map = {
                'معلق': 'pending',
                'قيد التنفيذ': 'in_progress',
                'مكتمل': 'completed',
                'ملغي': 'cancelled'
            }
            status = status_map.get(status_text)
            if status:
                orders = self.order_model.get_orders_by_status(status)
                self.populate_orders_table(orders)
        else:
            self.load_all_orders()
            
    def create_new_order(self):
        """إنشاء طلب جديد"""
        dialog = OrderDialog(self, self.database_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
        
    def view_order(self):
        """عرض تفاصيل الطلب"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة عرض الطلب قريباً")
        
    def show_orders_context_menu(self, position):
        """عرض قائمة السياق للطلبات"""
        if self.orders_table.itemAt(position):
            menu = QMenu()
            
            view_action = QAction("عرض التفاصيل", self)
            view_action.triggered.connect(self.view_order)
            menu.addAction(view_action)
            
            edit_action = QAction("تعديل", self)
            edit_action.triggered.connect(self.edit_order)
            menu.addAction(edit_action)
            
            status_menu = menu.addMenu("تغيير الحالة")
            
            pending_action = QAction("معلق", self)
            pending_action.triggered.connect(lambda: self.change_order_status('pending'))
            status_menu.addAction(pending_action)
            
            progress_action = QAction("قيد التنفيذ", self)
            progress_action.triggered.connect(lambda: self.change_order_status('in_progress'))
            status_menu.addAction(progress_action)
            
            completed_action = QAction("مكتمل", self)
            completed_action.triggered.connect(lambda: self.change_order_status('completed'))
            status_menu.addAction(completed_action)
            
            menu.exec_(self.orders_table.mapToGlobal(position))
            
    def edit_order(self):
        """تعديل الطلب"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_id = int(self.orders_table.item(current_row, 0).text())
            dialog = OrderDialog(self, self.database_manager, self.user_data, order_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()
        
    def change_order_status(self, new_status):
        """تغيير حالة الطلب"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_id = int(self.orders_table.item(current_row, 0).text())
            order_number = self.orders_table.item(current_row, 1).text()
            
            success, message = self.order_model.update_order_status(
                order_id, new_status, self.user_data['id']
            )
            
            if success:
                QMessageBox.information(self, "نجح", message)
                self.load_data()
            else:
                QMessageBox.warning(self, "خطأ", message)
