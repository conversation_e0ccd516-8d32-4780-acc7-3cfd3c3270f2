# دليل التثبيت والتشغيل - نظام إدارة مصنع الجرانيت

## متطلبات النظام

### المتطلبات الأساسية
- **نظام التشغيل**: Windows 10 أو أحدث
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 جيجابايت رام على الأقل
- **المساحة**: 1 جيجابايت مساحة فارغة
- **قاعدة البيانات**: SQL Server Express أو أحدث (اختياري)

## خطوات التثبيت

### 1. تثبيت Python
1. قم بتحميل Python من الموقع الرسمي: https://python.org
2. تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت
3. تحقق من التثبيت بفتح Command Prompt وكتابة:
   ```
   python --version
   ```

### 2. تثبيت المتطلبات
افتح Command Prompt في مجلد المشروع وقم بتشغيل:
```bash
pip install -r requirements.txt
```

أو يمكنك تثبيت المكتبات يدوياً:
```bash
pip install PyQt5 pyodbc reportlab openpyxl Pillow
```

### 3. إعداد قاعدة البيانات (اختياري)
النظام يستخدم قاعدة بيانات SQLite افتراضياً، ولكن يمكن استخدام SQL Server:

#### لاستخدام SQL Server:
1. قم بتثبيت SQL Server Express
2. قم بتعديل ملف `config.py` وتغيير إعدادات قاعدة البيانات

## تشغيل النظام

### الطريقة الأولى - التشغيل المباشر
```bash
python main.py
```

### الطريقة الثانية - التشغيل الكامل
```bash
python run_complete_system.py
```

### الطريقة الثالثة - استخدام ملف التشغيل الموجود
```bash
python start_app.py
```

## بيانات تسجيل الدخول الافتراضية

### المدير العام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### موظف مبيعات
- **اسم المستخدم**: `sales`
- **كلمة المرور**: `sales123`

⚠️ **تحذير أمني**: يُنصح بشدة بتغيير كلمات المرور الافتراضية فور تسجيل الدخول الأول.

## الميزات المتاحة

### 🏠 لوحة التحكم الرئيسية
- عرض الإحصائيات العامة
- الوصول السريع لجميع الأقسام
- متابعة الأنشطة الأخيرة

### 🚛 إدارة الشاحنات
- تسجيل الشاحنات الواردة والصادرة
- متابعة حالة الشاحنات
- إدارة بيانات السائقين
- تتبع الرحلات

### ⚙️ عمليات التقطيع
- تسجيل البلوكات الواردة
- إدارة عمليات التقطيع
- متابعة إنتاج الشرائح
- حساب المساحات تلقائياً

### 💰 إدارة المبيعات
- إنشاء الفواتير
- متابعة المدفوعات
- إدارة العملاء
- تتبع المستحقات

### 📊 إدارة المصاريف
- تسجيل المصاريف التشغيلية
- تصنيف المصاريف
- متابعة الميزانية
- تقارير المصاريف

### 📋 إدارة الطلبات
- إنشاء طلبات العملاء
- متابعة حالة الطلبات
- تنبيهات التسليم
- ربط الطلبات بالمخزون

### 📈 التقارير والإحصائيات
- تقارير المبيعات
- تقارير المصاريف
- تقارير المخزون
- إحصائيات الأداء

## حل المشاكل الشائعة

### مشكلة: "ModuleNotFoundError"
**الحل**: تأكد من تثبيت جميع المتطلبات:
```bash
pip install -r requirements.txt
```

### مشكلة: خطأ في قاعدة البيانات
**الحل**: 
1. تأكد من وجود مجلد `database` في مجلد المشروع
2. قم بحذف ملف قاعدة البيانات وإعادة تشغيل البرنامج لإنشائها من جديد

### مشكلة: الخطوط العربية لا تظهر بشكل صحيح
**الحل**: تأكد من وجود خطوط عربية مثبتة على النظام

### مشكلة: البرنامج لا يبدأ
**الحل**: 
1. تحقق من إصدار Python (يجب أن يكون 3.8 أو أحدث)
2. تأكد من تثبيت PyQt5 بشكل صحيح
3. قم بتشغيل البرنامج من Command Prompt لرؤية رسائل الخطأ

## الدعم الفني

في حالة مواجهة أي مشاكل:
1. تحقق من ملف السجلات في مجلد `logs`
2. تأكد من تثبيت جميع المتطلبات
3. راجع هذا الدليل للحلول الشائعة

## تحديثات النظام

للحصول على آخر التحديثات:
1. احتفظ بنسخة احتياطية من قاعدة البيانات
2. قم بتحميل الإصدار الجديد
3. انسخ ملف قاعدة البيانات إلى المجلد الجديد

---

**ملاحظة**: هذا النظام مطور خصيصاً لمصنع الحسن للأحجار ويمكن تخصيصه حسب احتياجات المصنع.
