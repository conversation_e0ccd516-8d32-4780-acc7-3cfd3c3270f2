# -*- coding: utf-8 -*-
"""
نظام السجلات للتطبيق
Logging System
"""

import logging
import os
from datetime import datetime
import config

def setup_logger(name='AlHassanStone', level=logging.INFO):
    """إعداد نظام السجلات"""
    
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    if not os.path.exists(config.LOGS_DIR):
        os.makedirs(config.LOGS_DIR)
    
    # إنشاء اسم ملف السجل بالتاريخ الحالي
    log_filename = f"app_{datetime.now().strftime('%Y%m%d')}.log"
    log_filepath = os.path.join(config.LOGS_DIR, log_filename)
    
    # إعداد المسجل
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # تجنب إضافة معالجات متعددة
    if logger.handlers:
        return logger
    
    # إعداد تنسيق الرسائل
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # معالج الملف
    file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # معالج وحدة التحكم
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

def log_user_action(user_id, action, details=""):
    """تسجيل عمليات المستخدم"""
    logger = logging.getLogger('UserActions')
    logger.info(f"المستخدم {user_id} - العملية: {action} - التفاصيل: {details}")

def log_database_operation(operation, table, details=""):
    """تسجيل عمليات قاعدة البيانات"""
    logger = logging.getLogger('DatabaseOps')
    logger.info(f"العملية: {operation} - الجدول: {table} - التفاصيل: {details}")

def log_error(error_message, exception=None):
    """تسجيل الأخطاء"""
    logger = logging.getLogger('Errors')
    if exception:
        logger.error(f"{error_message} - الاستثناء: {str(exception)}")
    else:
        logger.error(error_message)
