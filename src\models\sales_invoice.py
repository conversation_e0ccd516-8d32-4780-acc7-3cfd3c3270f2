# -*- coding: utf-8 -*-
"""
نموذج فواتير المبيعات
Sales Invoice Model
"""

from datetime import datetime, timedelta
from src.utils.logger import log_user_action, log_database_operation, log_error

class SalesInvoice:
    """نموذج فواتير المبيعات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def create_invoice(self, invoice_data, items_data, user_id):
        """إنشاء فاتورة جديدة مع العناصر"""
        try:
            # بدء معاملة
            if not self.db_manager.connection:
                self.db_manager.connect()
                
            # إنشاء رقم فاتورة تلقائي
            invoice_number = self.generate_invoice_number()
            
            # إدراج الفاتورة
            invoice_query = """
            INSERT INTO sales_invoices (invoice_number, customer_id, invoice_date, 
                                      due_date, subtotal, tax_amount, discount_amount,
                                      total_amount, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            invoice_params = (
                invoice_number,
                invoice_data['customer_id'],
                invoice_data['invoice_date'],
                invoice_data.get('due_date'),
                invoice_data['subtotal'],
                invoice_data.get('tax_amount', 0),
                invoice_data.get('discount_amount', 0),
                invoice_data['total_amount'],
                invoice_data.get('notes', ''),
                user_id
            )
            
            result = self.db_manager.execute_query(invoice_query, invoice_params)
            
            if result is None:
                return False, None, "فشل في إنشاء الفاتورة"
                
            invoice_id = self.db_manager.connection.lastrowid
            
            # إدراج عناصر الفاتورة
            for item in items_data:
                item_query = """
                INSERT INTO invoice_items (invoice_id, slice_id, granite_type_id,
                                         description, length_cm, width_cm, thickness_cm,
                                         quantity, unit_price, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                item_params = (
                    invoice_id,
                    item.get('slice_id'),
                    item['granite_type_id'],
                    item.get('description', ''),
                    item['length_cm'],
                    item['width_cm'],
                    item['thickness_cm'],
                    item['quantity'],
                    item['unit_price'],
                    item.get('notes', '')
                )
                
                item_result = self.db_manager.execute_query(item_query, item_params)
                
                if item_result is None:
                    self.db_manager.connection.rollback()
                    return False, None, "فشل في إضافة عناصر الفاتورة"
                    
                # تحديث حالة الشريحة إذا كانت محددة
                if item.get('slice_id'):
                    self.update_slice_status(item['slice_id'], 'sold')
                    
            # تأكيد المعاملة
            self.db_manager.connection.commit()
            
            log_user_action(user_id, "CREATE_INVOICE", f"إنشاء فاتورة رقم: {invoice_number}")
            log_database_operation("INSERT", "sales_invoices", f"فاتورة ID: {invoice_id}")
            
            return True, invoice_id, f"تم إنشاء الفاتورة رقم {invoice_number} بنجاح"
            
        except Exception as e:
            if self.db_manager.connection:
                self.db_manager.connection.rollback()
            log_error(f"خطأ في إنشاء الفاتورة: {str(e)}")
            return False, None, str(e)
            
    def generate_invoice_number(self):
        """توليد رقم فاتورة تلقائي"""
        try:
            # الحصول على آخر رقم فاتورة
            query = """
            SELECT invoice_number FROM sales_invoices 
            ORDER BY id DESC LIMIT 1
            """
            result = self.db_manager.execute_query(query, fetch='one')
            
            if result:
                last_number = result[0]
                # استخراج الرقم من آخر فاتورة
                if last_number.startswith('INV-'):
                    number_part = int(last_number.split('-')[1]) + 1
                else:
                    number_part = 1
            else:
                number_part = 1
                
            return f"INV-{number_part:06d}"
            
        except Exception as e:
            log_error(f"خطأ في توليد رقم الفاتورة: {str(e)}")
            return f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
    def get_all_invoices(self, limit=None):
        """الحصول على جميع الفواتير"""
        try:
            query = """
            SELECT si.id, si.invoice_number, si.invoice_date, si.due_date,
                   si.subtotal, si.tax_amount, si.discount_amount, si.total_amount,
                   si.paid_amount, si.status, si.notes, si.created_at,
                   c.name as customer_name, u.full_name as created_by_name,
                   (si.total_amount - si.paid_amount) as remaining_amount
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            LEFT JOIN users u ON si.created_by = u.id
            ORDER BY si.created_at DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
                
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الفواتير: {str(e)}")
            return []
            
    def get_invoice_by_id(self, invoice_id):
        """الحصول على فاتورة بالمعرف"""
        try:
            query = """
            SELECT si.*, c.name as customer_name, c.phone as customer_phone,
                   c.address as customer_address, u.full_name as created_by_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            LEFT JOIN users u ON si.created_by = u.id
            WHERE si.id = ?
            """
            
            return self.db_manager.execute_query(query, (invoice_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب الفاتورة: {str(e)}")
            return None
            
    def get_invoice_items(self, invoice_id):
        """الحصول على عناصر الفاتورة"""
        try:
            query = """
            SELECT ii.*, gt.name as granite_type_name,
                   (ii.length_cm * ii.width_cm * ii.quantity / 10000) as total_area,
                   (ii.length_cm * ii.width_cm * ii.quantity * ii.unit_price / 10000) as total_price
            FROM invoice_items ii
            LEFT JOIN granite_types gt ON ii.granite_type_id = gt.id
            WHERE ii.invoice_id = ?
            ORDER BY ii.id
            """
            
            return self.db_manager.execute_query(query, (invoice_id,), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب عناصر الفاتورة: {str(e)}")
            return []
            
    def add_payment(self, invoice_id, payment_amount, payment_method, reference_number, user_id):
        """إضافة دفعة للفاتورة"""
        try:
            # إدراج الدفعة
            payment_query = """
            INSERT INTO payments (invoice_id, payment_date, amount, payment_method,
                                reference_number, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            
            payment_params = (
                invoice_id,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                payment_amount,
                payment_method,
                reference_number,
                user_id
            )
            
            result = self.db_manager.execute_query(payment_query, payment_params)
            
            if result is None:
                return False, "فشل في إضافة الدفعة"
                
            # تحديث المبلغ المدفوع في الفاتورة
            update_query = """
            UPDATE sales_invoices 
            SET paid_amount = paid_amount + ?,
                status = CASE 
                    WHEN (paid_amount + ?) >= total_amount THEN 'paid'
                    ELSE 'partial'
                END
            WHERE id = ?
            """
            
            update_result = self.db_manager.execute_query(
                update_query, (payment_amount, payment_amount, invoice_id)
            )
            
            if update_result is None:
                self.db_manager.connection.rollback()
                return False, "فشل في تحديث الفاتورة"
                
            self.db_manager.connection.commit()
            
            log_user_action(user_id, "ADD_PAYMENT", f"إضافة دفعة للفاتورة ID: {invoice_id}")
            
            return True, "تم إضافة الدفعة بنجاح"
            
        except Exception as e:
            if self.db_manager.connection:
                self.db_manager.connection.rollback()
            log_error(f"خطأ في إضافة الدفعة: {str(e)}")
            return False, str(e)
            
    def update_slice_status(self, slice_id, status):
        """تحديث حالة الشريحة"""
        try:
            query = "UPDATE slices SET status = ? WHERE id = ?"
            self.db_manager.execute_query(query, (status, slice_id))
        except Exception as e:
            log_error(f"خطأ في تحديث حالة الشريحة: {str(e)}")
            
    def get_sales_statistics(self):
        """الحصول على إحصائيات المبيعات"""
        try:
            stats = {}
            
            # إجمالي المبيعات
            query = "SELECT SUM(total_amount) FROM sales_invoices"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_sales'] = result[0] if result and result[0] else 0
            
            # المبيعات هذا الشهر
            query = """
            SELECT SUM(total_amount) FROM sales_invoices 
            WHERE strftime('%Y-%m', invoice_date) = strftime('%Y-%m', 'now')
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['sales_this_month'] = result[0] if result and result[0] else 0
            
            # عدد الفواتير
            query = "SELECT COUNT(*) FROM sales_invoices"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_invoices'] = result[0] if result else 0
            
            # الفواتير المعلقة
            query = "SELECT COUNT(*) FROM sales_invoices WHERE status = 'pending'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['pending_invoices'] = result[0] if result else 0
            
            # إجمالي المديونية
            query = "SELECT SUM(total_amount - paid_amount) FROM sales_invoices WHERE status != 'paid'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_debt'] = result[0] if result and result[0] else 0
            
            return stats
            
        except Exception as e:
            log_error(f"خطأ في جلب إحصائيات المبيعات: {str(e)}")
            return {}
            
    def search_invoices(self, search_term):
        """البحث في الفواتير"""
        try:
            query = """
            SELECT si.id, si.invoice_number, si.invoice_date, si.total_amount,
                   si.paid_amount, si.status, c.name as customer_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE si.invoice_number LIKE ? OR c.name LIKE ? OR si.notes LIKE ?
            ORDER BY si.created_at DESC
            """
            
            search_pattern = f"%{search_term}%"
            params = (search_pattern, search_pattern, search_pattern)
            
            return self.db_manager.execute_query(query, params, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في البحث في الفواتير: {str(e)}")
            return []
            
    def validate_invoice_data(self, invoice_data, items_data):
        """التحقق من صحة بيانات الفاتورة"""
        errors = []
        
        # التحقق من العميل
        if not invoice_data.get('customer_id'):
            errors.append("العميل مطلوب")
            
        # التحقق من التاريخ
        if not invoice_data.get('invoice_date'):
            errors.append("تاريخ الفاتورة مطلوب")
            
        # التحقق من العناصر
        if not items_data or len(items_data) == 0:
            errors.append("يجب إضافة عنصر واحد على الأقل للفاتورة")
            
        # التحقق من صحة العناصر
        for i, item in enumerate(items_data):
            if not item.get('granite_type_id'):
                errors.append(f"نوع الجرانيت مطلوب للعنصر {i+1}")
                
            try:
                length = float(item.get('length_cm', 0))
                width = float(item.get('width_cm', 0))
                thickness = float(item.get('thickness_cm', 0))
                quantity = int(item.get('quantity', 0))
                unit_price = float(item.get('unit_price', 0))
                
                if length <= 0 or width <= 0 or thickness <= 0:
                    errors.append(f"الأبعاد يجب أن تكون أكبر من صفر للعنصر {i+1}")
                    
                if quantity <= 0:
                    errors.append(f"الكمية يجب أن تكون أكبر من صفر للعنصر {i+1}")
                    
                if unit_price <= 0:
                    errors.append(f"السعر يجب أن يكون أكبر من صفر للعنصر {i+1}")
                    
            except (ValueError, TypeError):
                errors.append(f"بيانات غير صحيحة في العنصر {i+1}")
                
        return errors
