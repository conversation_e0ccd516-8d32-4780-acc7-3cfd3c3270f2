# -*- coding: utf-8 -*-
"""
نموذج المصاريف
Expense Model
"""

from datetime import datetime
from src.utils.logger import log_user_action, log_database_operation, log_error

class Expense:
    """نموذج المصاريف التشغيلية"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def add_expense(self, expense_data, user_id):
        """إضافة مصروف جديد"""
        try:
            query = """
            INSERT INTO expenses (expense_date, expense_type, description, amount,
                                payment_method, reference_number, supplier, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                expense_data['expense_date'],
                expense_data['expense_type'],
                expense_data['description'],
                expense_data['amount'],
                expense_data.get('payment_method', ''),
                expense_data.get('reference_number', ''),
                expense_data.get('supplier', ''),
                expense_data.get('notes', ''),
                user_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                expense_id = self.db_manager.connection.lastrowid
                
                log_user_action(user_id, "ADD_EXPENSE", f"إضافة مصروف: {expense_data['description']}")
                log_database_operation("INSERT", "expenses", f"مصروف ID: {expense_id}")
                
                return True, expense_id, "تم إضافة المصروف بنجاح"
            else:
                return False, None, "فشل في إضافة المصروف"
                
        except Exception as e:
            log_error(f"خطأ في إضافة المصروف: {str(e)}")
            return False, None, str(e)
            
    def get_all_expenses(self, limit=None):
        """الحصول على جميع المصاريف"""
        try:
            query = """
            SELECT e.id, e.expense_date, e.expense_type, e.description, e.amount,
                   e.payment_method, e.reference_number, e.supplier, e.notes,
                   e.created_at, u.full_name as created_by_name
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            ORDER BY e.expense_date DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
                
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب المصاريف: {str(e)}")
            return []
            
    def get_expense_by_id(self, expense_id):
        """الحصول على مصروف بالمعرف"""
        try:
            query = """
            SELECT e.*, u.full_name as created_by_name
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            WHERE e.id = ?
            """
            
            return self.db_manager.execute_query(query, (expense_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب المصروف: {str(e)}")
            return None
            
    def update_expense(self, expense_id, expense_data, user_id):
        """تحديث المصروف"""
        try:
            query = """
            UPDATE expenses 
            SET expense_date = ?, expense_type = ?, description = ?, amount = ?,
                payment_method = ?, reference_number = ?, supplier = ?, notes = ?
            WHERE id = ?
            """
            
            params = (
                expense_data['expense_date'],
                expense_data['expense_type'],
                expense_data['description'],
                expense_data['amount'],
                expense_data.get('payment_method', ''),
                expense_data.get('reference_number', ''),
                expense_data.get('supplier', ''),
                expense_data.get('notes', ''),
                expense_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_EXPENSE", f"تحديث مصروف ID: {expense_id}")
                log_database_operation("UPDATE", "expenses", f"مصروف ID: {expense_id}")
                
                return True, "تم تحديث المصروف بنجاح"
            else:
                return False, "فشل في تحديث المصروف"
                
        except Exception as e:
            log_error(f"خطأ في تحديث المصروف: {str(e)}")
            return False, str(e)
            
    def delete_expense(self, expense_id, user_id):
        """حذف المصروف"""
        try:
            query = "DELETE FROM expenses WHERE id = ?"
            result = self.db_manager.execute_query(query, (expense_id,))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "DELETE_EXPENSE", f"حذف مصروف ID: {expense_id}")
                log_database_operation("DELETE", "expenses", f"مصروف ID: {expense_id}")
                
                return True, "تم حذف المصروف بنجاح"
            else:
                return False, "فشل في حذف المصروف"
                
        except Exception as e:
            log_error(f"خطأ في حذف المصروف: {str(e)}")
            return False, str(e)
            
    def get_expenses_by_type(self, expense_type):
        """الحصول على المصاريف حسب النوع"""
        try:
            query = """
            SELECT e.id, e.expense_date, e.description, e.amount, e.supplier,
                   e.created_at, u.full_name as created_by_name
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            WHERE e.expense_type = ?
            ORDER BY e.expense_date DESC
            """
            
            return self.db_manager.execute_query(query, (expense_type,), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب المصاريف حسب النوع: {str(e)}")
            return []
            
    def get_expenses_by_date_range(self, start_date, end_date):
        """الحصول على المصاريف في فترة زمنية"""
        try:
            query = """
            SELECT e.id, e.expense_date, e.expense_type, e.description, e.amount,
                   e.supplier, e.created_at, u.full_name as created_by_name
            FROM expenses e
            LEFT JOIN users u ON e.created_by = u.id
            WHERE DATE(e.expense_date) BETWEEN ? AND ?
            ORDER BY e.expense_date DESC
            """
            
            return self.db_manager.execute_query(query, (start_date, end_date), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب المصاريف بالتاريخ: {str(e)}")
            return []
            
    def search_expenses(self, search_term):
        """البحث في المصاريف"""
        try:
            query = """
            SELECT e.id, e.expense_date, e.expense_type, e.description, e.amount,
                   e.supplier, e.notes, e.created_at
            FROM expenses e
            WHERE e.description LIKE ? OR e.expense_type LIKE ? 
               OR e.supplier LIKE ? OR e.notes LIKE ?
            ORDER BY e.expense_date DESC
            """
            
            search_pattern = f"%{search_term}%"
            params = (search_pattern, search_pattern, search_pattern, search_pattern)
            
            return self.db_manager.execute_query(query, params, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في البحث في المصاريف: {str(e)}")
            return []
            
    def get_expense_statistics(self):
        """الحصول على إحصائيات المصاريف"""
        try:
            stats = {}
            
            # إجمالي المصاريف
            query = "SELECT SUM(amount) FROM expenses"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_expenses'] = result[0] if result and result[0] else 0
            
            # المصاريف هذا الشهر
            query = """
            SELECT SUM(amount) FROM expenses 
            WHERE strftime('%Y-%m', expense_date) = strftime('%Y-%m', 'now')
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['expenses_this_month'] = result[0] if result and result[0] else 0
            
            # عدد المصاريف
            query = "SELECT COUNT(*) FROM expenses"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_count'] = result[0] if result else 0
            
            # المصاريف حسب النوع
            query = """
            SELECT expense_type, SUM(amount) as total, COUNT(*) as count
            FROM expenses
            GROUP BY expense_type
            ORDER BY total DESC
            """
            result = self.db_manager.execute_query(query, fetch='all')
            stats['by_type'] = result if result else []
            
            # المصاريف الشهرية (آخر 6 أشهر)
            query = """
            SELECT strftime('%Y-%m', expense_date) as month, SUM(amount) as total
            FROM expenses
            WHERE expense_date >= date('now', '-6 months')
            GROUP BY strftime('%Y-%m', expense_date)
            ORDER BY month DESC
            """
            result = self.db_manager.execute_query(query, fetch='all')
            stats['monthly'] = result if result else []
            
            # أكبر المصاريف
            query = """
            SELECT description, amount, expense_date, expense_type
            FROM expenses
            ORDER BY amount DESC
            LIMIT 5
            """
            result = self.db_manager.execute_query(query, fetch='all')
            stats['top_expenses'] = result if result else []
            
            return stats
            
        except Exception as e:
            log_error(f"خطأ في جلب إحصائيات المصاريف: {str(e)}")
            return {}
            
    def get_expense_types_summary(self):
        """الحصول على ملخص أنواع المصاريف"""
        try:
            query = """
            SELECT expense_type, 
                   COUNT(*) as count,
                   SUM(amount) as total_amount,
                   AVG(amount) as avg_amount,
                   MIN(amount) as min_amount,
                   MAX(amount) as max_amount
            FROM expenses
            GROUP BY expense_type
            ORDER BY total_amount DESC
            """
            
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب ملخص أنواع المصاريف: {str(e)}")
            return []
            
    def validate_expense_data(self, expense_data):
        """التحقق من صحة بيانات المصروف"""
        errors = []
        
        # التحقق من التاريخ
        if not expense_data.get('expense_date'):
            errors.append("تاريخ المصروف مطلوب")
            
        # التحقق من نوع المصروف
        if not expense_data.get('expense_type', '').strip():
            errors.append("نوع المصروف مطلوب")
            
        # التحقق من الوصف
        if not expense_data.get('description', '').strip():
            errors.append("وصف المصروف مطلوب")
        elif len(expense_data['description'].strip()) < 3:
            errors.append("وصف المصروف يجب أن يكون 3 أحرف على الأقل")
            
        # التحقق من المبلغ
        try:
            amount = float(expense_data.get('amount', 0))
            if amount <= 0:
                errors.append("مبلغ المصروف يجب أن يكون أكبر من صفر")
        except (ValueError, TypeError):
            errors.append("مبلغ المصروف يجب أن يكون رقماً صحيحاً")
            
        return errors
