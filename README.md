# برنامج الحسن ستون - نظام إدارة مصنع الجرانيت

## وصف المشروع
برنامج سطح مكتب احترافي لإدارة مصنع الجرانيت بالكامل، مطور باستخدام Python وPyQt مع قاعدة بيانات SQL Server.

## المميزات الرئيسية
- إدارة المبيعات والفواتير
- إدارة المخزون (الشرائح والبلوكات)
- استلام الجرارات والمشتريات
- قسم النشر وإدارة العمليات
- المصاريف التشغيلية
- الطلبات والحجوزات
- لوحة تحكم إدارية
- نظام المستخدمين والصلاحيات
- تصدير التقارير إلى PDF وExcel

## متطلبات النظام
- Windows 10 أو أحدث
- Python 3.8 أو أحدث
- SQL Server Express أو أحدث
- 4 جيجابايت رام على الأقل
- 500 ميجابايت مساحة فارغة

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إعداد قاعدة البيانات
- تأكد من تشغيل SQL Server
- قم بتشغيل سكريبت إنشاء قاعدة البيانات

### 3. تشغيل البرنامج
```bash
python main.py
```

## هيكل المشروع
```
AL-Hassan/
├── main.py                 # الملف الرئيسي
├── config.py              # إعدادات التطبيق
├── requirements.txt       # المكتبات المطلوبة
├── src/                   # مجلد المصدر
│   ├── ui/               # واجهات المستخدم
│   ├── database/         # إدارة قاعدة البيانات
│   ├── models/           # نماذج البيانات
│   ├── utils/            # الأدوات المساعدة
│   └── reports/          # نظام التقارير
├── assets/               # الموارد
│   ├── icons/           # الأيقونات
│   └── fonts/           # الخطوط
├── logs/                # ملفات السجلات
├── reports/             # التقارير المُصدرة
└── backup/              # النسخ الاحتياطية
```

## الاستخدام
1. قم بتسجيل الدخول باستخدام بيانات المستخدم
2. استخدم القوائم للوصول إلى الوظائف المختلفة
3. يمكن تصدير التقارير من أي قسم

## الدعم الفني
للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

## الترخيص
جميع الحقوق محفوظة لشركة الحسن ستون © 2025
