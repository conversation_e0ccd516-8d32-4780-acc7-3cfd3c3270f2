#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء الواجهات المتبقية
Create Remaining Widgets
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_orders_widget():
    """إنشاء واجهة الطلبات الحديثة"""
    print("📋 إنشاء واجهة الطلبات الحديثة...")
    
    content = '''# -*- coding: utf-8 -*-
"""
واجهة إدارة الطلبات والحجوزات المحسنة
Modern Orders Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetI<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                            Q<PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ateE<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ox, <PERSON><PERSON><PERSON><PERSON>,
                            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QGridLayout, QMessageBox,
                            QAbstractItemView, QHeaderView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from src.utils.font_utils import apply_font_settings, get_responsive_style

class OrdersWidget(QWidget):
    """واجهة إدارة الطلبات والحجوزات المحسنة"""
    
    def __init__(self, database_manager=None, user_data=None):
        super().__init__()
        
        if not database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()
        else:
            self.database_manager = database_manager
            
        self.user_data = user_data or {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام'}
        
        self.setup_ui()
        self.apply_modern_styles()
        apply_font_settings(self)
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # العنوان الرئيسي
        self.create_header_section(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        self.setLayout(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # العنوان مع الأيقونة
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("📋")
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel("إدارة الطلبات والحجوزات")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 15px;
        """)
        
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # الأزرار السريعة
        actions_layout = QHBoxLayout()
        
        new_order_btn = QPushButton("➕ طلب جديد")
        new_order_btn.setObjectName("modernButton")
        new_order_btn.clicked.connect(self.add_order)
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setObjectName("modernButton")
        refresh_btn.clicked.connect(self.load_data)
        
        actions_layout.addWidget(new_order_btn)
        actions_layout.addWidget(refresh_btn)
        
        header_layout.addLayout(title_layout)
        header_layout.addLayout(actions_layout)
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # إحصائيات الطلبات
        self.create_orders_stats(content_layout)
        
        # جدول الطلبات
        self.create_orders_table(content_layout)
        
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)
        
    def create_orders_stats(self, layout):
        """إنشاء إحصائيات الطلبات"""
        stats_frame = QFrame()
        stats_frame.setObjectName("card")
        
        stats_layout = QGridLayout()
        
        # بيانات الإحصائيات
        stats_data = [
            ("📋", "إجمالي الطلبات", "28", "طلب"),
            ("⏳", "قيد التنفيذ", "12", "طلب"),
            ("✅", "مكتملة", "15", "طلب"),
            ("❌", "ملغية", "1", "طلب"),
        ]
        
        for i, (icon, title, value, subtitle) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, subtitle)
            stats_layout.addWidget(card, 0, i)
            
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)
        
    def create_stat_card(self, icon, title, value, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(100)
        
        layout = QVBoxLayout()
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #2c3e50;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #8e44ad;")
        
        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("font-size: 10px; color: #7f8c8d;")
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        
        card.setLayout(layout)
        return card
        
    def create_orders_table(self, layout):
        """إنشاء جدول الطلبات"""
        table_frame = QFrame()
        table_frame.setObjectName("card")
        table_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("📋 قائمة الطلبات")
        table_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.orders_table = QTableWidget()
        self.orders_table.setObjectName("modernTable")
        
        # إعداد أعمدة الجدول
        columns = ["رقم الطلب", "العميل", "تاريخ الطلب", "تاريخ التسليم", "الحالة", "المبلغ", "الإجراءات"]
        self.orders_table.setColumnCount(len(columns))
        self.orders_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.horizontalHeader().setStretchLastSection(True)
        
        table_layout.addWidget(self.orders_table)
        table_frame.setLayout(table_layout)
        layout.addWidget(table_frame)
        
    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QFrame#headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 3px solid #8e44ad;
            }
            
            QFrame#contentFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
            }
            
            QFrame#card {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
            
            QFrame#statCard {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
            
            QFrame#statCard:hover {
                border-color: #8e44ad;
                box-shadow: 0 2px 8px rgba(142, 68, 173, 0.1);
            }
            
            QPushButton#modernButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8e44ad, stop:1 #7d3c98);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }
            
            QPushButton#modernButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7d3c98, stop:1 #6c3483);
            }
            
            QTableWidget#modernTable {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                gridline-color: #f1f2f6;
            }
            
            QTableWidget#modernTable QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8e44ad, stop:1 #7d3c98);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        # بيانات تجريبية
        orders_data = [
            ("ORD-001", "أحمد محمد", "2025-01-15", "2025-01-25", "قيد التنفيذ", "15,000"),
            ("ORD-002", "سارة أحمد", "2025-01-14", "2025-01-24", "مكتمل", "8,500"),
            ("ORD-003", "محمد علي", "2025-01-13", "2025-01-23", "قيد التنفيذ", "22,000"),
            ("ORD-004", "فاطمة حسن", "2025-01-12", "2025-01-22", "مكتمل", "12,300"),
        ]
        
        self.orders_table.setRowCount(len(orders_data))
        
        for row, order in enumerate(orders_data):
            for col, value in enumerate(order):
                item = QTableWidgetItem(str(value))
                
                # تلوين الحالة
                if col == 4:  # عمود الحالة
                    if value == "مكتمل":
                        item.setBackground(Qt.green)
                        item.setForeground(Qt.white)
                    elif value == "قيد التنفيذ":
                        item.setBackground(Qt.yellow)
                    elif value == "ملغي":
                        item.setBackground(Qt.red)
                        item.setForeground(Qt.white)
                
                self.orders_table.setItem(row, col, item)
            
            # إضافة أزرار الإجراءات
            actions_widget = self.create_actions_widget(row)
            self.orders_table.setCellWidget(row, 6, actions_widget)
            
    def create_actions_widget(self, order_id):
        """إنشاء ويدجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        view_btn = QPushButton("👁️")
        view_btn.setToolTip("عرض")
        view_btn.setFixedSize(30, 30)
        view_btn.clicked.connect(lambda: self.view_order(order_id))
        
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_order(order_id))
        
        layout.addWidget(view_btn)
        layout.addWidget(edit_btn)
        
        widget.setLayout(layout)
        return widget
        
    def add_order(self):
        """إضافة طلب جديد"""
        try:
            from src.ui.order_dialog import OrderDialog
            dialog = OrderDialog(self, self.database_manager, self.user_data)
            if dialog.exec_() == dialog.Accepted:
                self.load_data()
                QMessageBox.information(self, "نجح", "تم إضافة الطلب بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إضافة الطلب: {str(e)}")
        
    def view_order(self, order_id):
        """عرض الطلب"""
        QMessageBox.information(self, "قيد التطوير", f"عرض الطلب رقم {order_id}")
        
    def edit_order(self, order_id):
        """تعديل الطلب"""
        QMessageBox.information(self, "قيد التطوير", f"تعديل الطلب رقم {order_id}")
'''
    
    try:
        with open("src/ui/orders_widget.py", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ تم إنشاء واجهة الطلبات الحديثة")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء واجهة الطلبات: {str(e)}")
        return False

def create_reports_widget():
    """إنشاء واجهة التقارير الحديثة"""
    print("📈 إنشاء واجهة التقارير الحديثة...")
    
    content = '''# -*- coding: utf-8 -*-
"""
واجهة التقارير المحسنة
Modern Reports Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QGridLayout, QComboBox,
                            QDateEdit, QMessageBox, QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from src.utils.font_utils import apply_font_settings, get_responsive_style

class ReportsWidget(QWidget):
    """واجهة التقارير المحسنة"""
    
    def __init__(self, database_manager=None, user_data=None):
        super().__init__()
        
        if not database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()
        else:
            self.database_manager = database_manager
            
        self.user_data = user_data or {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام'}
        
        self.setup_ui()
        self.apply_modern_styles()
        apply_font_settings(self)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # العنوان الرئيسي
        self.create_header_section(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        self.setLayout(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # العنوان مع الأيقونة
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("📈")
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel("التقارير والإحصائيات")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 15px;
        """)
        
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        header_layout.addLayout(title_layout)
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # أنواع التقارير
        self.create_reports_grid(content_layout)
        
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)
        
    def create_reports_grid(self, layout):
        """إنشاء شبكة التقارير"""
        reports_frame = QFrame()
        reports_frame.setObjectName("card")
        
        reports_layout = QGridLayout()
        reports_layout.setSpacing(15)
        
        # أنواع التقارير
        reports_data = [
            ("💰", "تقرير المبيعات", "تقرير شامل للمبيعات والفواتير", self.sales_report),
            ("💸", "تقرير المصاريف", "تقرير تفصيلي للمصاريف", self.expenses_report),
            ("📋", "تقرير الطلبات", "تقرير حالة الطلبات", self.orders_report),
            ("👥", "تقرير العملاء", "تقرير بيانات العملاء", self.customers_report),
            ("🚛", "تقرير الجرارات", "تقرير حالة الجرارات", self.trucks_report),
            ("📊", "تقرير شامل", "تقرير شامل للنشاط", self.comprehensive_report),
        ]
        
        for i, (icon, title, description, action) in enumerate(reports_data):
            card = self.create_report_card(icon, title, description, action)
            row = i // 2
            col = i % 2
            reports_layout.addWidget(card, row, col)
            
        reports_frame.setLayout(reports_layout)
        layout.addWidget(reports_frame)
        
    def create_report_card(self, icon, title, description, action):
        """إنشاء بطاقة تقرير"""
        card = QFrame()
        card.setObjectName("reportCard")
        card.setFixedHeight(150)
        
        layout = QVBoxLayout()
        layout.setSpacing(10)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        desc_label.setWordWrap(True)
        
        # زر الإنشاء
        generate_btn = QPushButton("إنشاء التقرير")
        generate_btn.setObjectName("modernButton")
        generate_btn.clicked.connect(action)
        
        layout.addLayout(header_layout)
        layout.addWidget(desc_label)
        layout.addStretch()
        layout.addWidget(generate_btn)
        
        card.setLayout(layout)
        return card
        
    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QFrame#headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 3px solid #f39c12;
            }
            
            QFrame#contentFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
            }
            
            QFrame#card {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
            
            QFrame#reportCard {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
            
            QFrame#reportCard:hover {
                border-color: #f39c12;
                box-shadow: 0 4px 15px rgba(243, 156, 18, 0.1);
            }
            
            QPushButton#modernButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 13px;
            }
            
            QPushButton#modernButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d68910);
            }
        """)
        
    def sales_report(self):
        """تقرير المبيعات"""
        QMessageBox.information(self, "تقرير المبيعات", "تقرير المبيعات قيد التطوير...")
        
    def expenses_report(self):
        """تقرير المصاريف"""
        QMessageBox.information(self, "تقرير المصاريف", "تقرير المصاريف قيد التطوير...")
        
    def orders_report(self):
        """تقرير الطلبات"""
        QMessageBox.information(self, "تقرير الطلبات", "تقرير الطلبات قيد التطوير...")
        
    def customers_report(self):
        """تقرير العملاء"""
        QMessageBox.information(self, "تقرير العملاء", "تقرير العملاء قيد التطوير...")
        
    def trucks_report(self):
        """تقرير الجرارات"""
        QMessageBox.information(self, "تقرير الجرارات", "تقرير الجرارات قيد التطوير...")
        
    def comprehensive_report(self):
        """التقرير الشامل"""
        QMessageBox.information(self, "التقرير الشامل", "التقرير الشامل قيد التطوير...")
'''
    
    try:
        with open("src/ui/reports_widget.py", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ تم إنشاء واجهة التقارير الحديثة")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء واجهة التقارير: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎨 إنشاء الواجهات المتبقية")
    print("=" * 40)
    
    orders_ok = create_orders_widget()
    reports_ok = create_reports_widget()
    
    # تقرير النتائج
    print("\n📊 تقرير الإنشاء:")
    print("=" * 25)
    print(f"واجهة الطلبات: {'✅ تم' if orders_ok else '❌ فشل'}")
    print(f"واجهة التقارير: {'✅ تم' if reports_ok else '❌ فشل'}")
    
    if orders_ok and reports_ok:
        print("\n🎉 تم إنشاء جميع الواجهات المتبقية!")
        return 0
    else:
        print("\n⚠️ بعض الواجهات واجهت مشاكل")
        return 1

if __name__ == "__main__":
    sys.exit(main())
