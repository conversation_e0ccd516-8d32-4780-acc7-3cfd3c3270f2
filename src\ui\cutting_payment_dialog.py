# -*- coding: utf-8 -*-
"""
نافذة دفعات عمليات النشر
Cutting Payment Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QDoubleSpinBox, QPushButton, QMessageBox,
                            QLabel, QGroupBox, QComboBox, QDateEdit, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

import config
from src.models.customer import Customer
from src.models.cutting_operation import CuttingOperation
from src.utils.font_utils import apply_font_settings, get_responsive_style

class CuttingPaymentDialog(QDialog):
    """نافذة دفعات عمليات النشر للعملاء"""
    
    def __init__(self, parent=None, database_manager=None, user_data=None, customer_id=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.user_data = user_data
        self.customer_id = customer_id

        # إنشاء مدير قاعدة البيانات إذا لم يتم تمريره
        if not self.database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()

        self.customer_model = Customer(self.database_manager)
        self.cutting_model = CuttingOperation(self.database_manager)

        self.setup_ui()
        apply_font_settings(self)
        self.load_customer_cutting_info()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("دفعات عمليات النشر للعميل")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # معلومات العميل
        customer_group = QGroupBox("معلومات العميل")
        customer_layout = QFormLayout()
        
        self.customer_name_label = QLabel()
        self.customer_name_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        customer_layout.addRow("اسم العميل:", self.customer_name_label)
        
        self.customer_phone_label = QLabel()
        customer_layout.addRow("الهاتف:", self.customer_phone_label)
        
        customer_group.setLayout(customer_layout)
        layout.addWidget(customer_group)
        
        # عمليات النشر
        operations_group = QGroupBox("عمليات النشر")
        operations_layout = QVBoxLayout()
        
        self.operations_table = QTableWidget()
        self.operations_table.setColumnCount(6)
        
        headers = ["رقم العملية", "تاريخ العملية", "نوع الجرانيت", "الكمية", "التكلفة", "الحالة"]
        self.operations_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.operations_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.operations_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        header = self.operations_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        
        operations_layout.addWidget(self.operations_table)
        operations_group.setLayout(operations_layout)
        layout.addWidget(operations_group)
        
        # الإجماليات
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QFormLayout()
        
        self.total_operations_label = QLabel("0")
        totals_layout.addRow("عدد العمليات:", self.total_operations_label)
        
        self.total_cost_label = QLabel()
        self.total_cost_label.setStyleSheet("font-weight: bold; color: #E74C3C;")
        totals_layout.addRow("إجمالي التكلفة:", self.total_cost_label)
        
        self.total_paid_label = QLabel()
        self.total_paid_label.setStyleSheet("font-weight: bold; color: #27AE60;")
        totals_layout.addRow("إجمالي المدفوع:", self.total_paid_label)
        
        self.remaining_amount_label = QLabel()
        self.remaining_amount_label.setStyleSheet("font-weight: bold; color: #F39C12;")
        totals_layout.addRow("المبلغ المتبقي:", self.remaining_amount_label)
        
        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)
        
        # معلومات الدفعة الجديدة
        payment_group = QGroupBox("إضافة دفعة جديدة")
        payment_layout = QFormLayout()
        
        # تاريخ الدفعة
        self.payment_date = QDateEdit()
        self.payment_date.setDate(QDate.currentDate())
        self.payment_date.setCalendarPopup(True)
        payment_layout.addRow("تاريخ الدفعة:", self.payment_date)
        
        # مبلغ الدفعة
        self.payment_amount = QDoubleSpinBox()
        self.payment_amount.setRange(0.01, 1000000)
        self.payment_amount.setDecimals(2)
        self.payment_amount.setSuffix(" جنيه")
        self.payment_amount.valueChanged.connect(self.validate_amount)
        payment_layout.addRow("مبلغ الدفعة:", self.payment_amount)
        
        # طريقة الدفع
        self.payment_method = QComboBox()
        self.payment_method.addItems([
            "نقدي",
            "شيك",
            "تحويل بنكي",
            "بطاقة ائتمان",
            "بطاقة خصم",
            "محفظة إلكترونية",
            "أخرى"
        ])
        payment_layout.addRow("طريقة الدفع:", self.payment_method)
        
        # رقم المرجع
        self.reference_number = QLineEdit()
        self.reference_number.setPlaceholderText("رقم الشيك أو التحويل أو المرجع")
        payment_layout.addRow("رقم المرجع:", self.reference_number)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(60)
        self.notes_input.setPlaceholderText("ملاحظات إضافية عن الدفعة...")
        payment_layout.addRow("ملاحظات:", self.notes_input)
        
        payment_group.setLayout(payment_layout)
        layout.addWidget(payment_group)
        
        # رسالة التحذير
        self.warning_label = QLabel()
        self.warning_label.setStyleSheet("color: #E74C3C; font-weight: bold;")
        self.warning_label.setVisible(False)
        layout.addWidget(self.warning_label)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الدفعة")
        self.save_btn.clicked.connect(self.save_payment)
        
        self.view_payments_btn = QPushButton("عرض الدفعات السابقة")
        self.view_payments_btn.clicked.connect(self.view_previous_payments)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.view_payments_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        self.apply_styles()

    def load_customer_cutting_info(self):
        """تحميل معلومات العميل وعمليات النشر"""
        if not self.customer_id:
            return
            
        # تحميل معلومات العميل
        customer = self.customer_model.get_customer_by_id(self.customer_id)
        if customer:
            self.customer_name_label.setText(customer[1])
            self.customer_phone_label.setText(customer[2] if customer[2] else "غير محدد")
            
        # تحميل عمليات النشر (مؤقتاً بيانات تجريبية)
        self.load_cutting_operations()
        
    def load_cutting_operations(self):
        """تحميل عمليات النشر للعميل"""
        # بيانات تجريبية - سيتم استبدالها بالبيانات الحقيقية من قاعدة البيانات
        operations = [
            (1, "2024-01-15", "جرانيت أحمر", "5 متر مربع", 2500.00, "مكتملة"),
            (2, "2024-01-20", "جرانيت أسود", "3 متر مربع", 1800.00, "مكتملة"),
            (3, "2024-01-25", "رخام أبيض", "4 متر مربع", 3200.00, "قيد التنفيذ"),
        ]
        
        self.operations_table.setRowCount(len(operations))
        
        total_cost = 0
        for row, operation in enumerate(operations):
            items = [
                str(operation[0]),  # رقم العملية
                operation[1],       # تاريخ العملية
                operation[2],       # نوع الجرانيت
                operation[3],       # الكمية
                f"{operation[4]:,.2f} جنيه",  # التكلفة
                operation[5]        # الحالة
            ]
            
            total_cost += operation[4]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 4]:  # الأعمدة الرقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.operations_table.setItem(row, col, item)
                
        # تحديث الإجماليات
        total_paid = 1500.00  # مؤقت
        remaining = total_cost - total_paid
        
        self.total_operations_label.setText(str(len(operations)))
        self.total_cost_label.setText(f"{total_cost:,.2f} جنيه")
        self.total_paid_label.setText(f"{total_paid:,.2f} جنيه")
        self.remaining_amount_label.setText(f"{remaining:,.2f} جنيه")
        
        # تعيين الحد الأقصى لمبلغ الدفعة
        self.payment_amount.setMaximum(remaining)
        self.payment_amount.setValue(min(remaining, 1000))
        
        self.remaining_amount = remaining
        
    def validate_amount(self):
        """التحقق من صحة المبلغ"""
        amount = self.payment_amount.value()
        
        if hasattr(self, 'remaining_amount'):
            if amount > self.remaining_amount:
                self.warning_label.setText("⚠️ مبلغ الدفعة أكبر من المبلغ المتبقي")
                self.warning_label.setVisible(True)
                self.save_btn.setEnabled(False)
            elif amount <= 0:
                self.warning_label.setText("⚠️ مبلغ الدفعة يجب أن يكون أكبر من صفر")
                self.warning_label.setVisible(True)
                self.save_btn.setEnabled(False)
            else:
                self.warning_label.setVisible(False)
                self.save_btn.setEnabled(True)
                
    def save_payment(self):
        """حفظ دفعة عمليات النشر"""
        # التحقق من صحة البيانات
        if self.payment_amount.value() <= 0:
            QMessageBox.warning(self, "خطأ", "مبلغ الدفعة يجب أن يكون أكبر من صفر")
            return
            
        if hasattr(self, 'remaining_amount') and self.payment_amount.value() > self.remaining_amount:
            QMessageBox.warning(self, "خطأ", "مبلغ الدفعة أكبر من المبلغ المتبقي")
            return
            
        # حفظ الدفعة (مؤقتاً)
        try:
            payment_data = {
                'customer_id': self.customer_id,
                'amount': self.payment_amount.value(),
                'payment_method': self.payment_method.currentText(),
                'reference_number': self.reference_number.text().strip(),
                'payment_date': self.payment_date.date().toString("yyyy-MM-dd"),
                'notes': self.notes_input.toPlainText().strip(),
                'payment_type': 'cutting',
                'created_by': self.user_data['id']
            }
            
            # هنا سيتم حفظ البيانات في قاعدة البيانات
            QMessageBox.information(self, "نجح", "تم حفظ دفعة عمليات النشر بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الدفعة:\n{str(e)}")
            
    def view_previous_payments(self):
        """عرض الدفعات السابقة"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة عرض الدفعات السابقة قريباً")
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(get_responsive_style(config.COLORS['secondary']))
