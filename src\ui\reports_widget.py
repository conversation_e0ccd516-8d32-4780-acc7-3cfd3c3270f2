# -*- coding: utf-8 -*-
"""
واجهة التقارير
Reports Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QSplitter,
                            QGroupBox, QGridLayout, QComboBox, QSpinBox,
                            QTabWidget, QFileDialog, QProgressBar)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QDesktopServices
from PyQt5.QtCore import QUrl

import config
import os
from src.reports.report_generator import ReportGenerator
from src.utils.logger import log_user_action

class ReportsWidget(QWidget):
    """واجهة التقارير"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.report_generator = ReportGenerator(database_manager)
        
        self.setup_ui()
        self.load_available_reports()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        self.create_header(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس الواجهة"""
        header_layout = QHBoxLayout()
        
        # العنوان
        title_label = QLabel("التقارير والإحصائيات")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        # زر التحديث
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_available_reports)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
    def create_tabs(self, layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        
        # تبويب إنشاء التقارير
        self.generate_tab = QWidget()
        self.create_generate_tab()
        self.tabs.addTab(self.generate_tab, "إنشاء التقارير")
        
        # تبويب التقارير المحفوظة
        self.saved_tab = QWidget()
        self.create_saved_tab()
        self.tabs.addTab(self.saved_tab, "التقارير المحفوظة")
        
        layout.addWidget(self.tabs)
        
    def create_generate_tab(self):
        """إنشاء تبويب إنشاء التقارير"""
        layout = QVBoxLayout()
        
        # تقرير المبيعات
        sales_group = QGroupBox("تقرير المبيعات")
        sales_layout = QGridLayout()
        
        sales_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.sales_start_date = QDateEdit()
        self.sales_start_date.setDate(QDate.currentDate().addDays(-30))
        self.sales_start_date.setCalendarPopup(True)
        sales_layout.addWidget(self.sales_start_date, 0, 1)
        
        sales_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.sales_end_date = QDateEdit()
        self.sales_end_date.setDate(QDate.currentDate())
        self.sales_end_date.setCalendarPopup(True)
        sales_layout.addWidget(self.sales_end_date, 0, 3)
        
        self.generate_sales_btn = QPushButton("إنشاء تقرير المبيعات")
        self.generate_sales_btn.clicked.connect(self.generate_sales_report)
        sales_layout.addWidget(self.generate_sales_btn, 1, 0, 1, 4)
        
        sales_group.setLayout(sales_layout)
        layout.addWidget(sales_group)
        
        # تقرير المصاريف
        expenses_group = QGroupBox("تقرير المصاريف")
        expenses_layout = QGridLayout()
        
        expenses_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.expenses_start_date = QDateEdit()
        self.expenses_start_date.setDate(QDate.currentDate().addDays(-30))
        self.expenses_start_date.setCalendarPopup(True)
        expenses_layout.addWidget(self.expenses_start_date, 0, 1)
        
        expenses_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.expenses_end_date = QDateEdit()
        self.expenses_end_date.setDate(QDate.currentDate())
        self.expenses_end_date.setCalendarPopup(True)
        expenses_layout.addWidget(self.expenses_end_date, 0, 3)
        
        self.generate_expenses_btn = QPushButton("إنشاء تقرير المصاريف")
        self.generate_expenses_btn.clicked.connect(self.generate_expenses_report)
        expenses_layout.addWidget(self.generate_expenses_btn, 1, 0, 1, 4)
        
        expenses_group.setLayout(expenses_layout)
        layout.addWidget(expenses_group)
        
        # تقرير المخزون
        inventory_group = QGroupBox("تقرير المخزون")
        inventory_layout = QVBoxLayout()
        
        inventory_info = QLabel("تقرير شامل لحالة المخزون الحالية من الشرائح المتاحة")
        inventory_info.setStyleSheet("color: #7F8C8D; font-size: 11px;")
        inventory_layout.addWidget(inventory_info)
        
        self.generate_inventory_btn = QPushButton("إنشاء تقرير المخزون")
        self.generate_inventory_btn.clicked.connect(self.generate_inventory_report)
        inventory_layout.addWidget(self.generate_inventory_btn)
        
        inventory_group.setLayout(inventory_layout)
        layout.addWidget(inventory_group)
        
        # تقرير شامل
        comprehensive_group = QGroupBox("التقرير الشامل")
        comprehensive_layout = QVBoxLayout()
        
        comprehensive_info = QLabel("تقرير شامل يتضمن المبيعات والمصاريف والمخزون")
        comprehensive_info.setStyleSheet("color: #7F8C8D; font-size: 11px;")
        comprehensive_layout.addWidget(comprehensive_info)
        
        self.generate_comprehensive_btn = QPushButton("إنشاء التقرير الشامل")
        self.generate_comprehensive_btn.clicked.connect(self.generate_comprehensive_report)
        comprehensive_layout.addWidget(self.generate_comprehensive_btn)
        
        comprehensive_group.setLayout(comprehensive_layout)
        layout.addWidget(comprehensive_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        self.generate_tab.setLayout(layout)
        
    def create_saved_tab(self):
        """إنشاء تبويب التقارير المحفوظة"""
        layout = QVBoxLayout()
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        self.open_folder_btn = QPushButton("فتح مجلد التقارير")
        self.open_folder_btn.clicked.connect(self.open_reports_folder)
        
        self.delete_report_btn = QPushButton("حذف التقرير المحدد")
        self.delete_report_btn.clicked.connect(self.delete_selected_report)
        self.delete_report_btn.setEnabled(False)
        
        buttons_layout.addWidget(self.open_folder_btn)
        buttons_layout.addWidget(self.delete_report_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول التقارير المحفوظة
        self.reports_table = QTableWidget()
        self.reports_table.setColumnCount(4)
        
        headers = ["اسم التقرير", "تاريخ الإنشاء", "الحجم", "المسار"]
        self.reports_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.reports_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.reports_table.setAlternatingRowColors(True)
        self.reports_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.reports_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        
        # الأحداث
        self.reports_table.selectionModel().selectionChanged.connect(self.on_report_selection_changed)
        self.reports_table.doubleClicked.connect(self.open_selected_report)
        
        # قائمة السياق
        self.reports_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.reports_table.customContextMenuRequested.connect(self.show_reports_context_menu)
        
        layout.addWidget(self.reports_table)
        
        self.saved_tab.setLayout(layout)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QPushButton:disabled {{
                background-color: #BDC3C7;
                color: #7F8C8D;
            }}
            
            QDateEdit {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
            
            QProgressBar {{
                border: 1px solid #BDC3C7;
                border-radius: 5px;
                text-align: center;
            }}
            
            QProgressBar::chunk {{
                background-color: {config.COLORS['success']};
                border-radius: 3px;
            }}
        """)
        
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        start_date = self.sales_start_date.date().toString("yyyy-MM-dd")
        end_date = self.sales_end_date.date().toString("yyyy-MM-dd")
        
        self.show_progress("جاري إنشاء تقرير المبيعات...")
        
        try:
            success, filepath, message = self.report_generator.generate_sales_report(
                start_date, end_date, self.user_data['id']
            )
            
            self.hide_progress()
            
            if success:
                QMessageBox.information(self, "نجح", f"{message}\n\nمسار الملف: {filepath}")
                self.load_available_reports()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            self.hide_progress()
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير:\n{str(e)}")
            
    def generate_expenses_report(self):
        """إنشاء تقرير المصاريف"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير تقرير المصاريف قريباً")
        
    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        self.show_progress("جاري إنشاء تقرير المخزون...")
        
        try:
            success, filepath, message = self.report_generator.generate_inventory_report(
                self.user_data['id']
            )
            
            self.hide_progress()
            
            if success:
                QMessageBox.information(self, "نجح", f"{message}\n\nمسار الملف: {filepath}")
                self.load_available_reports()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            self.hide_progress()
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير:\n{str(e)}")
            
    def generate_comprehensive_report(self):
        """إنشاء التقرير الشامل"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير التقرير الشامل قريباً")
        
    def show_progress(self, message):
        """عرض شريط التقدم"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        
        # تعطيل الأزرار
        self.generate_sales_btn.setEnabled(False)
        self.generate_expenses_btn.setEnabled(False)
        self.generate_inventory_btn.setEnabled(False)
        self.generate_comprehensive_btn.setEnabled(False)
        
    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)
        
        # تفعيل الأزرار
        self.generate_sales_btn.setEnabled(True)
        self.generate_expenses_btn.setEnabled(True)
        self.generate_inventory_btn.setEnabled(True)
        self.generate_comprehensive_btn.setEnabled(True)
        
    def load_available_reports(self):
        """تحميل التقارير المتاحة"""
        try:
            reports = self.report_generator.get_available_reports()
            self.populate_reports_table(reports)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل التقارير:\n{str(e)}")
            
    def populate_reports_table(self, reports):
        """ملء جدول التقارير"""
        self.reports_table.setRowCount(len(reports))
        
        for row, report in enumerate(reports):
            items = [
                report['filename'],
                report['created'].strftime('%Y-%m-%d %H:%M:%S'),
                f"{report['size'] / 1024:.1f} KB",
                report['filepath']
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(str(item_text))
                self.reports_table.setItem(row, col, item)
                
    def on_report_selection_changed(self):
        """عند تغيير التحديد في جدول التقارير"""
        selected_rows = self.reports_table.selectionModel().selectedRows()
        self.delete_report_btn.setEnabled(len(selected_rows) > 0)
        
    def open_selected_report(self):
        """فتح التقرير المحدد"""
        current_row = self.reports_table.currentRow()
        if current_row >= 0:
            filepath = self.reports_table.item(current_row, 3).text()
            if os.path.exists(filepath):
                QDesktopServices.openUrl(QUrl.fromLocalFile(filepath))
            else:
                QMessageBox.warning(self, "خطأ", "الملف غير موجود")
                
    def open_reports_folder(self):
        """فتح مجلد التقارير"""
        reports_path = os.path.abspath(self.report_generator.reports_dir)
        if os.path.exists(reports_path):
            QDesktopServices.openUrl(QUrl.fromLocalFile(reports_path))
        else:
            QMessageBox.warning(self, "خطأ", "مجلد التقارير غير موجود")
            
    def delete_selected_report(self):
        """حذف التقرير المحدد"""
        current_row = self.reports_table.currentRow()
        if current_row >= 0:
            filename = self.reports_table.item(current_row, 0).text()
            filepath = self.reports_table.item(current_row, 3).text()
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف التقرير:\n{filename}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    if os.path.exists(filepath):
                        os.remove(filepath)
                        QMessageBox.information(self, "نجح", "تم حذف التقرير بنجاح")
                        self.load_available_reports()
                    else:
                        QMessageBox.warning(self, "خطأ", "الملف غير موجود")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في حذف الملف:\n{str(e)}")
                    
    def show_reports_context_menu(self, position):
        """عرض قائمة السياق للتقارير"""
        if self.reports_table.itemAt(position):
            menu = QMenu()
            
            open_action = QAction("فتح", self)
            open_action.triggered.connect(self.open_selected_report)
            menu.addAction(open_action)
            
            delete_action = QAction("حذف", self)
            delete_action.triggered.connect(self.delete_selected_report)
            menu.addAction(delete_action)
            
            menu.exec_(self.reports_table.mapToGlobal(position))
