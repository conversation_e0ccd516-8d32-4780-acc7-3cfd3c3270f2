# -*- coding: utf-8 -*-
"""
نموذج المستخدم
User Model
"""

import hashlib
from datetime import datetime
from src.utils.logger import log_user_action, log_error
import config

class User:
    """نموذج المستخدم"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def authenticate(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            query = """
            SELECT id, username, password_hash, full_name, role, is_active
            FROM users 
            WHERE username = ? AND is_active = 1
            """
            result = self.db_manager.execute_query(query, (username,), fetch='one')
            
            if result:
                user_id, username, password_hash, full_name, role, is_active = result
                
                # التحقق من كلمة المرور
                if bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8')):
                    # تحديث آخر تسجيل دخول
                    self.update_last_login(user_id)
                    
                    user_data = {
                        'id': user_id,
                        'username': username,
                        'full_name': full_name,
                        'role': role,
                        'permissions': self.get_user_permissions(role)
                    }
                    
                    log_user_action(user_id, "LOGIN", f"تسجيل دخول ناجح للمستخدم {username}")
                    return user_data
                    
            return None
            
        except Exception as e:
            log_error(f"خطأ في التحقق من المستخدم: {str(e)}")
            return None
            
    def get_user_permissions(self, role):
        """الحصول على صلاحيات المستخدم حسب الدور"""
        permissions = {
            'admin': {
                'users_management': True,
                'trucks_management': True,
                'blocks_management': True,
                'cutting_management': True,
                'inventory_management': True,
                'sales_management': True,
                'expenses_management': True,
                'orders_management': True,
                'reports_access': True,
                'backup_restore': True,
                'system_settings': True
            },
            'sales': {
                'users_management': False,
                'trucks_management': False,
                'blocks_management': False,
                'cutting_management': False,
                'inventory_management': True,
                'sales_management': True,
                'expenses_management': False,
                'orders_management': True,
                'reports_access': True,
                'backup_restore': False,
                'system_settings': False
            },
            'production': {
                'users_management': False,
                'trucks_management': True,
                'blocks_management': True,
                'cutting_management': True,
                'inventory_management': True,
                'sales_management': False,
                'expenses_management': True,
                'orders_management': False,
                'reports_access': True,
                'backup_restore': False,
                'system_settings': False
            },
            'accountant': {
                'users_management': False,
                'trucks_management': False,
                'blocks_management': False,
                'cutting_management': False,
                'inventory_management': False,
                'sales_management': True,
                'expenses_management': True,
                'orders_management': False,
                'reports_access': True,
                'backup_restore': True,
                'system_settings': False
            },
            'warehouse': {
                'users_management': False,
                'trucks_management': False,
                'blocks_management': True,
                'cutting_management': False,
                'inventory_management': True,
                'sales_management': False,
                'expenses_management': False,
                'orders_management': False,
                'reports_access': True,
                'backup_restore': False,
                'system_settings': False
            }
        }
        
        return permissions.get(role, {})
        
    def update_last_login(self, user_id):
        """تحديث آخر تسجيل دخول"""
        try:
            query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
            self.db_manager.execute_query(query, (user_id,))
            if self.db_manager.connection:
                self.db_manager.connection.commit()
        except Exception as e:
            log_error(f"خطأ في تحديث آخر تسجيل دخول: {str(e)}")
            
    def create_user(self, username, password, full_name, role, created_by, notes=""):
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من عدم وجود المستخدم
            check_query = "SELECT COUNT(*) FROM users WHERE username = ?"
            result = self.db_manager.execute_query(check_query, (username,), fetch='one')
            
            if result and result[0] > 0:
                return False, "اسم المستخدم موجود بالفعل"
                
            # تشفير كلمة المرور
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # إدراج المستخدم الجديد
            insert_query = """
            INSERT INTO users (username, password_hash, full_name, role, created_by, notes)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            
            result = self.db_manager.execute_query(
                insert_query, 
                (username, password_hash, full_name, role, created_by, notes)
            )
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                log_user_action(created_by, "CREATE_USER", f"إنشاء مستخدم جديد: {username}")
                return True, "تم إنشاء المستخدم بنجاح"
            else:
                return False, "فشل في إنشاء المستخدم"
                
        except Exception as e:
            log_error(f"خطأ في إنشاء المستخدم: {str(e)}")
            return False, str(e)
            
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            query = """
            SELECT id, username, full_name, role, is_active, created_at, last_login
            FROM users
            ORDER BY created_at DESC
            """
            return self.db_manager.execute_query(query, fetch='all')
        except Exception as e:
            log_error(f"خطأ في جلب المستخدمين: {str(e)}")
            return []
            
    def update_user_status(self, user_id, is_active, updated_by):
        """تحديث حالة المستخدم (تفعيل/إلغاء تفعيل)"""
        try:
            query = "UPDATE users SET is_active = ? WHERE id = ?"
            result = self.db_manager.execute_query(query, (is_active, user_id))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                status = "تفعيل" if is_active else "إلغاء تفعيل"
                log_user_action(updated_by, "UPDATE_USER_STATUS", f"{status} المستخدم ID: {user_id}")
                return True
            return False
            
        except Exception as e:
            log_error(f"خطأ في تحديث حالة المستخدم: {str(e)}")
            return False
            
    def change_password(self, user_id, old_password, new_password):
        """تغيير كلمة المرور"""
        try:
            # التحقق من كلمة المرور القديمة
            query = "SELECT password_hash FROM users WHERE id = ?"
            result = self.db_manager.execute_query(query, (user_id,), fetch='one')
            
            if not result:
                return False, "المستخدم غير موجود"
                
            password_hash = result[0]
            
            if not bcrypt.checkpw(old_password.encode('utf-8'), password_hash.encode('utf-8')):
                return False, "كلمة المرور القديمة غير صحيحة"
                
            # تشفير كلمة المرور الجديدة
            new_password_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # تحديث كلمة المرور
            update_query = "UPDATE users SET password_hash = ? WHERE id = ?"
            result = self.db_manager.execute_query(update_query, (new_password_hash, user_id))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                log_user_action(user_id, "CHANGE_PASSWORD", "تغيير كلمة المرور")
                return True, "تم تغيير كلمة المرور بنجاح"
            else:
                return False, "فشل في تغيير كلمة المرور"
                
        except Exception as e:
            log_error(f"خطأ في تغيير كلمة المرور: {str(e)}")
            return False, str(e)
