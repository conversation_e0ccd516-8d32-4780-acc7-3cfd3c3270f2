# -*- coding: utf-8 -*-
"""
أدوات الخطوط والتنسيق
Font and Formatting Utilities
"""

from PyQt5.QtWidgets import QLineEdit, QTextEdit, QDoubleSpinBox, QSpinBox, QComboBox, QDateEdit, QLabel, QPushButton
from PyQt5.QtGui import QFont
from src.models.settings import Settings

class FontManager:
    """مدير الخطوط والتنسيق"""
    
    def __init__(self):
        self.settings = Settings()
        
    def get_current_font(self):
        """الحصول على الخط الحالي"""
        font_settings = self.settings.get_font_settings()
        font = QFont(font_settings['family'], font_settings['size'])
        if font_settings['bold']:
            font.setBold(True)
        return font
        
    def get_field_height(self):
        """حساب ارتفاع الحقول المناسب"""
        font_settings = self.settings.get_font_settings()
        font_size = font_settings['size']
        # حساب متجاوب: حد أدنى 25، مع زيادة تدريجية حسب حجم الخط
        if font_size <= 10:
            return 25
        elif font_size <= 14:
            return font_size + 15
        elif font_size <= 20:
            return font_size + 18
        else:
            return font_size + 22
        
    def apply_font_to_widget(self, widget):
        """تطبيق الخط على عنصر واحد"""
        font = self.get_current_font()
        widget.setFont(font)
        
        # تحديث ارتفاع الحقول
        field_height = self.get_field_height()
        
        if isinstance(widget, (QLineEdit, QDoubleSpinBox, QSpinBox, QComboBox, QDateEdit)):
            widget.setMinimumHeight(field_height)
        elif isinstance(widget, QTextEdit):
            widget.setMinimumHeight(field_height * 2)
        elif isinstance(widget, QPushButton):
            widget.setMinimumHeight(field_height)
            
    def apply_font_to_dialog(self, dialog):
        """تطبيق الخط على نافذة كاملة"""
        font = self.get_current_font()
        dialog.setFont(font)
        
        field_height = self.get_field_height()
        
        # تطبيق على جميع حقول الإدخال
        for widget in dialog.findChildren((QLineEdit, QDoubleSpinBox, QSpinBox, QComboBox, QDateEdit, QTextEdit, QPushButton)):
            if isinstance(widget, (QLineEdit, QDoubleSpinBox, QSpinBox, QComboBox, QDateEdit)):
                widget.setMinimumHeight(field_height)
            elif isinstance(widget, QTextEdit):
                widget.setMinimumHeight(field_height * 2)
            elif isinstance(widget, QPushButton):
                widget.setMinimumHeight(field_height)
                
    def get_responsive_stylesheet(self, base_color="#3498DB"):
        """الحصول على stylesheet متجاوب مع حجم الخط"""
        field_height = self.get_field_height()
        font_size = self.settings.get_font_settings()['size']
        
        return f"""
            QLineEdit, QTextEdit, QDoubleSpinBox, QSpinBox, QComboBox, QDateEdit {{
                padding: {max(5, font_size // 3)}px;
                border: 1px solid #BDC3C7;
                border-radius: {max(3, font_size // 4)}px;
                min-height: {field_height}px;
                font-size: {font_size}px;
            }}
            
            QPushButton {{
                background-color: {base_color};
                color: white;
                border: none;
                padding: {max(8, font_size // 2)}px {max(15, font_size)}px;
                border-radius: {max(5, font_size // 3)}px;
                font-weight: bold;
                min-height: {field_height}px;
                font-size: {font_size}px;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QPushButton:disabled {{
                background-color: #BDC3C7;
                color: #7F8C8D;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: 2px solid #ECF0F1;
                border-radius: {max(5, font_size // 3)}px;
                margin-top: {max(10, font_size)}px;
                padding-top: {max(10, font_size)}px;
                font-size: {font_size + 1}px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {max(10, font_size)}px;
                padding: 0 {max(5, font_size // 2)}px 0 {max(5, font_size // 2)}px;
            }}
            
            QLabel {{
                font-size: {font_size}px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
                font-size: {font_size}px;
            }}
            
            QTableWidget::item {{
                padding: {max(5, font_size // 3)}px;
                min-height: {field_height - 5}px;
            }}
            
            QTabWidget::pane {{
                border: 1px solid #BDC3C7;
                border-radius: {max(3, font_size // 4)}px;
            }}
            
            QTabBar::tab {{
                background-color: #ECF0F1;
                padding: {max(8, font_size // 2)}px {max(15, font_size)}px;
                margin-right: 2px;
                border-top-left-radius: {max(3, font_size // 4)}px;
                border-top-right-radius: {max(3, font_size // 4)}px;
                font-size: {font_size}px;
            }}
            
            QTabBar::tab:selected {{
                background-color: white;
                border-bottom: 2px solid {base_color};
            }}
        """

# إنشاء مثيل عام للاستخدام
font_manager = FontManager()

def apply_font_settings(widget):
    """دالة مساعدة لتطبيق إعدادات الخط"""
    font_manager.apply_font_to_dialog(widget)
    
def get_responsive_style(color="#3498DB"):
    """دالة مساعدة للحصول على الأنماط المتجاوبة"""
    return font_manager.get_responsive_stylesheet(color)
