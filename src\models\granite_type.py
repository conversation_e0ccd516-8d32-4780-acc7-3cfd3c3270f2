# -*- coding: utf-8 -*-
"""
نموذج أنواع الجرانيت
Granite Types Model
"""

from src.utils.logger import log_user_action, log_database_operation, log_error

class GraniteType:
    """نموذج أنواع الجرانيت"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def get_all_granite_types(self):
        """الحصول على جميع أنواع الجرانيت"""
        try:
            query = """
            SELECT id, name, description, price_per_sqm, is_active, created_at
            FROM granite_types
            WHERE is_active = 1
            ORDER BY name
            """
            
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب أنواع الجرانيت: {str(e)}")
            return []
            
    def get_granite_type_by_id(self, granite_type_id):
        """الحصول على نوع جرانيت بالمعرف"""
        try:
            query = """
            SELECT id, name, description, price_per_sqm, is_active, created_at
            FROM granite_types
            WHERE id = ?
            """
            
            return self.db_manager.execute_query(query, (granite_type_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب نوع الجرانيت: {str(e)}")
            return None
            
    def add_granite_type(self, granite_data):
        """إضافة نوع جرانيت جديد"""
        try:
            query = """
            INSERT INTO granite_types (name, description, price_per_sqm)
            VALUES (?, ?, ?)
            """
            
            params = (
                granite_data['name'],
                granite_data.get('description', ''),
                granite_data.get('price_per_sqm', 0)
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                granite_type_id = self.db_manager.connection.lastrowid
                log_database_operation("INSERT", "granite_types", f"نوع جرانيت ID: {granite_type_id}")
                
                return True, granite_type_id, "تم إضافة نوع الجرانيت بنجاح"
            else:
                return False, None, "فشل في إضافة نوع الجرانيت"
                
        except Exception as e:
            log_error(f"خطأ في إضافة نوع الجرانيت: {str(e)}")
            return False, None, str(e)
            
    def update_granite_type(self, granite_type_id, granite_data):
        """تحديث نوع الجرانيت"""
        try:
            query = """
            UPDATE granite_types 
            SET name = ?, description = ?, price_per_sqm = ?
            WHERE id = ?
            """
            
            params = (
                granite_data['name'],
                granite_data.get('description', ''),
                granite_data.get('price_per_sqm', 0),
                granite_type_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_database_operation("UPDATE", "granite_types", f"نوع جرانيت ID: {granite_type_id}")
                
                return True, "تم تحديث نوع الجرانيت بنجاح"
            else:
                return False, "فشل في تحديث نوع الجرانيت"
                
        except Exception as e:
            log_error(f"خطأ في تحديث نوع الجرانيت: {str(e)}")
            return False, str(e)
