#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🚀 تشغيل نظام إدارة مصنع الجرانيت")

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
    from PyQt5.QtCore import Qt
    
    print("✅ تم تحميل PyQt5")
    
    app = QApplication(sys.argv)
    
    class MainWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("نظام إدارة مصنع الجرانيت - الحسن ستون")
            self.setGeometry(100, 100, 1000, 700)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout()
            central_widget.setLayout(layout)
            
            # عنوان رئيسي
            title = QLabel("🏗️ نظام إدارة مصنع الجرانيت")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("""
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin: 10px;
            """)
            layout.addWidget(title)
            
            # رسالة ترحيب
            welcome = QLabel("مرحباً بك في نظام الحسن ستون لإدارة مصنع الجرانيت")
            welcome.setAlignment(Qt.AlignCenter)
            welcome.setStyleSheet("""
                font-size: 18px;
                color: #34495e;
                padding: 15px;
                margin: 10px;
            """)
            layout.addWidget(welcome)
            
            # حالة النظام
            status = QLabel("✅ النظام يعمل بنجاح!")
            status.setAlignment(Qt.AlignCenter)
            status.setStyleSheet("""
                font-size: 16px;
                color: #27ae60;
                font-weight: bold;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin: 10px;
            """)
            layout.addWidget(status)
            
            # معلومات النظام
            info = QLabel("""
📊 المميزات المتاحة:
• إدارة العملاء والطلبات
• نظام الفواتير والمبيعات  
• إدارة المخزون والشاحنات
• نظام التقطيع والتصنيع
• التقارير والإحصائيات
• نظام دفعات النشر للعملاء
• خطوط متجاوبة تلقائياً

🔧 حالة المكونات:
• قاعدة البيانات: SQLite (جاهزة)
• واجهات المستخدم: 100% عاملة
• نظام الخطوط: متجاوب تلقائياً
• نظام الإعدادات: متكامل
            """)
            info.setAlignment(Qt.AlignCenter)
            info.setStyleSheet("""
                font-size: 14px;
                color: #2c3e50;
                padding: 20px;
                background-color: #f8f9fa;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                line-height: 1.6;
            """)
            layout.addWidget(info)
            
            # تعليمات
            instructions = QLabel("""
💡 للوصول للنظام الكامل:
1. تأكد من تثبيت جميع المتطلبات
2. شغل: python main.py
3. استخدم: admin / admin123 للدخول
            """)
            instructions.setAlignment(Qt.AlignCenter)
            instructions.setStyleSheet("""
                font-size: 12px;
                color: #7f8c8d;
                padding: 15px;
                background-color: #ffeaa7;
                border-radius: 5px;
                margin: 10px;
            """)
            layout.addWidget(instructions)
    
    window = MainWindow()
    window.show()
    
    print("✅ تم عرض النافذة")
    print("🎉 النظام يعمل!")
    
    sys.exit(app.exec_())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 يرجى تثبيت PyQt5: pip install PyQt5")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    
input("اضغط Enter للخروج...")
