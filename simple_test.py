#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
Simple System Test
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    try:
        import config
        print("✅ config")
        
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager")
        
        from src.utils.font_utils import FontManager
        print("✅ FontManager")
        
        return True
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        from src.database.database_manager import DatabaseManager
        db = DatabaseManager()
        if db.connect():
            print("✅ قاعدة البيانات")
            db.disconnect()
            return True
        else:
            print("⚠️ قاعدة البيانات (SQLite)")
            return True
    except Exception as e:
        print(f"❌ قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 اختبار بسيط للنظام")
    print("=" * 30)
    
    imports_ok = test_basic_imports()
    db_ok = test_database()
    
    if imports_ok and db_ok:
        print("\n🎉 النظام جاهز للتشغيل!")
    else:
        print("\n⚠️ يوجد مشاكل تحتاج إصلاح")
