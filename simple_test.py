#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("اختبار بسيط لبرنامج الحسن ستون")
print("=" * 40)

try:
    print("1. اختبار PyQt5...")
    from PyQt5.QtWidgets import QApplication
    print("   ✅ PyQt5 يعمل")
except Exception as e:
    print(f"   ❌ PyQt5 لا يعمل: {e}")
    sys.exit(1)

try:
    print("2. اختبار قاعدة البيانات...")
    from src.database.sqlite_manager import SQLiteManager
    db = SQLiteManager()
    if db.test_connection() and db.setup_database():
        print("   ✅ قاعدة البيانات تعمل")
    else:
        print("   ❌ قاعدة البيانات لا تعمل")
except Exception as e:
    print(f"   ❌ خطأ في قاعدة البيانات: {e}")

try:
    print("3. اختبار تسجيل الدخول...")
    from src.models.user import User
    user_model = User(db)
    user_data = user_model.authenticate("admin", "admin123")
    if user_data:
        print(f"   ✅ تسجيل الدخول يعمل - {user_data['full_name']}")
    else:
        print("   ❌ تسجيل الدخول لا يعمل")
except Exception as e:
    print(f"   ❌ خطأ في تسجيل الدخول: {e}")

print("\n" + "=" * 40)
print("✅ الاختبار مكتمل!")
print("لتشغيل البرنامج: python start_app.py")
print("اسم المستخدم: admin")
print("كلمة المرور: admin123")
