#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج الحسن ستون - نظام إدارة مصنع الجرانيت
Al-Hassan Stone ERP System
الملف الرئيسي للتطبيق
"""

import sys
import os
import logging
from datetime import datetime

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

import config
from src.ui.login_window import LoginWindow
from src.ui.main_window import MainWindow
from src.database.sqlite_manager import SQLiteManager
from src.utils.logger import setup_logger

class AlHassanStoneApp:
    """الكلاس الرئيسي للتطبيق"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_application()
        self.setup_logging()
        self.database_manager = None
        self.main_window = None
        self.current_user = None
        
    def setup_application(self):
        """إعداد التطبيق الأساسي"""
        self.app.setApplicationName(config.APP_NAME)
        self.app.setApplicationVersion(config.APP_VERSION)
        self.app.setOrganizationName(config.COMPANY_NAME)
        
        # إعداد الخط العربي
        font = QFont(config.UI_CONFIG['font_family'], config.UI_CONFIG['font_size'])
        self.app.setFont(font)
        
        # إعداد اتجاه النص للعربية
        self.app.setLayoutDirection(Qt.RightToLeft)
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        self.logger = setup_logger()
        self.logger.info(f"تم بدء تشغيل {config.APP_NAME} الإصدار {config.APP_VERSION}")
        
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        splash.setMask(splash_pix.mask())
        splash.show()
        
        # رسالة الترحيب
        splash.showMessage(
            f"مرحباً بك في {config.APP_NAME}\n"
            f"الإصدار {config.APP_VERSION}\n"
            f"جاري تحميل النظام...",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )
        
        # تأخير لعرض الشاشة
        QTimer.singleShot(3000, splash.close)
        self.app.processEvents()
        
        return splash
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.database_manager = SQLiteManager()
            if self.database_manager.test_connection():
                self.logger.info("تم الاتصال بقاعدة البيانات بنجاح")
                # إعداد قاعدة البيانات إذا لم تكن موجودة
                if self.database_manager.setup_database():
                    self.logger.info("تم إعداد قاعدة البيانات بنجاح")
                    return True
                else:
                    self.show_error("خطأ في الإعداد", "فشل في إعداد قاعدة البيانات")
                    return False
            else:
                self.show_error("خطأ في الاتصال",
                              "لا يمكن الاتصال بقاعدة البيانات")
                return False
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
            self.show_error("خطأ في قاعدة البيانات", str(e))
            return False
            
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = LoginWindow(self.database_manager)
        login_window.login_successful.connect(self.on_login_successful)
        
        if login_window.exec_() == login_window.Accepted:
            return True
        return False
        
    def on_login_successful(self, user_data):
        """عند نجاح تسجيل الدخول"""
        self.current_user = user_data
        self.logger.info(f"تم تسجيل دخول المستخدم: {user_data['username']}")
        self.show_main_window()
        
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        self.main_window = MainWindow(self.database_manager, self.current_user)
        self.main_window.show()
        
    def show_error(self, title, message):
        """عرض رسالة خطأ"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setTextFormat(Qt.RichText)
        msg.exec_()
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            # عرض شاشة البداية
            splash = self.show_splash_screen()
            
            # تهيئة قاعدة البيانات
            if not self.initialize_database():
                return 1
                
            # إغلاق شاشة البداية
            splash.close()
            
            # عرض نافذة تسجيل الدخول
            if not self.show_login():
                return 0
                
            # تشغيل التطبيق
            return self.app.exec_()
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {str(e)}")
            self.show_error("خطأ في التطبيق", str(e))
            return 1

def main():
    """الدالة الرئيسية"""
    app = AlHassanStoneApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
