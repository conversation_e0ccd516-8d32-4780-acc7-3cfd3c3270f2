# -*- coding: utf-8 -*-
"""
واجهة إدارة الجرارات
Trucks Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QSplitter,
                            QGroupBox, QGridLayout, QComboBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QContextMenuEvent

import config
from src.models.truck import Truck
from src.models.block import Block
from src.models.granite_type import GraniteType
from src.utils.logger import log_user_action

class TrucksWidget(QWidget):
    """واجهة إدارة الجرارات"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.truck_model = Truck(database_manager)
        self.block_model = Block(database_manager)
        
        self.setup_ui()
        self.load_trucks()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان والأزرار
        self.create_header(main_layout)
        
        # منطقة البحث والفلترة
        self.create_search_section(main_layout)
        
        # جدول الجرارات
        self.create_trucks_table(main_layout)
        
        # شريط الحالة
        self.create_status_bar(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس الواجهة"""
        header_layout = QHBoxLayout()
        
        # العنوان
        title_label = QLabel("إدارة الجرارات")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        # الأزرار
        self.add_truck_btn = QPushButton("إضافة جرار جديد")
        self.add_truck_btn.clicked.connect(self.add_truck)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_trucks)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.add_truck_btn)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
    def create_search_section(self, layout):
        """إنشاء قسم البحث"""
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QHBoxLayout()
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في رقم الجرار، المورد، أو الملاحظات...")
        self.search_input.textChanged.connect(self.search_trucks)
        
        # فلترة بالتاريخ
        date_label = QLabel("من تاريخ:")
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        
        to_label = QLabel("إلى تاريخ:")
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        
        filter_btn = QPushButton("فلترة")
        filter_btn.clicked.connect(self.filter_by_date)
        
        clear_filter_btn = QPushButton("إلغاء الفلترة")
        clear_filter_btn.clicked.connect(self.clear_filter)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(date_label)
        search_layout.addWidget(self.start_date)
        search_layout.addWidget(to_label)
        search_layout.addWidget(self.end_date)
        search_layout.addWidget(filter_btn)
        search_layout.addWidget(clear_filter_btn)
        
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)
        
    def create_trucks_table(self, layout):
        """إنشاء جدول الجرارات"""
        self.trucks_table = QTableWidget()
        self.trucks_table.setColumnCount(9)
        
        headers = ["المعرف", "رقم الجرار", "الوزن (طن)", "سعر الطن", 
                  "التكلفة الإجمالية", "تاريخ الوصول", "المورد", "عدد البلوكات", "تاريخ الإدخال"]
        self.trucks_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.trucks_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.trucks_table.setAlternatingRowColors(True)
        self.trucks_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.trucks_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # رقم الجرار
        
        # قائمة السياق
        self.trucks_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.trucks_table.customContextMenuRequested.connect(self.show_context_menu)
        
        # النقر المزدوج للتعديل
        self.trucks_table.doubleClicked.connect(self.edit_truck)
        
        layout.addWidget(self.trucks_table)
        
    def create_status_bar(self, layout):
        """إنشاء شريط الحالة"""
        self.status_label = QLabel("جاري تحميل البيانات...")
        self.status_label.setStyleSheet("color: #7F8C8D; font-size: 11px;")
        layout.addWidget(self.status_label)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QLineEdit, QDateEdit {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
        
    def load_trucks(self):
        """تحميل الجرارات"""
        try:
            trucks = self.truck_model.get_all_trucks()
            self.populate_table(trucks)
            self.update_status(f"تم تحميل {len(trucks)} جرار")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الجرارات:\n{str(e)}")
            
    def populate_table(self, trucks):
        """ملء الجدول بالبيانات"""
        self.trucks_table.setRowCount(len(trucks))
        
        for row, truck in enumerate(trucks):
            # تحويل البيانات إلى نص
            items = [
                str(truck[0]),  # ID
                str(truck[1]),  # truck_number
                f"{truck[2]:.2f}",  # weight_tons
                f"{truck[3]:.2f}",  # price_per_ton
                f"{truck[4]:,.2f}",  # total_cost
                str(truck[5])[:10] if truck[5] else "",  # arrival_date
                str(truck[6]) if truck[6] else "",  # supplier
                str(truck[10]) if len(truck) > 10 else "0",  # blocks_count
                str(truck[8])[:10] if truck[8] else ""  # created_at
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 2, 3, 4, 7]:  # أعمدة رقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.trucks_table.setItem(row, col, item)
                
    def search_trucks(self):
        """البحث في الجرارات"""
        search_term = self.search_input.text().strip()
        if search_term:
            trucks = self.truck_model.search_trucks(search_term)
            self.populate_table(trucks)
            self.update_status(f"تم العثور على {len(trucks)} جرار")
        else:
            self.load_trucks()
            
    def filter_by_date(self):
        """فلترة حسب التاريخ"""
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        
        trucks = self.truck_model.get_trucks_by_date_range(start_date, end_date)
        self.populate_table(trucks)
        self.update_status(f"تم العثور على {len(trucks)} جرار في الفترة المحددة")
        
    def clear_filter(self):
        """إلغاء الفلترة"""
        self.search_input.clear()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.end_date.setDate(QDate.currentDate())
        self.load_trucks()
        
    def add_truck(self):
        """إضافة جرار جديد"""
        dialog = TruckDialog(self, self.database_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_trucks()
            
    def edit_truck(self):
        """تعديل جرار"""
        current_row = self.trucks_table.currentRow()
        if current_row >= 0:
            truck_id = int(self.trucks_table.item(current_row, 0).text())
            dialog = TruckDialog(self, self.database_manager, truck_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_trucks()
                
    def delete_truck(self):
        """حذف جرار"""
        current_row = self.trucks_table.currentRow()
        if current_row >= 0:
            truck_id = int(self.trucks_table.item(current_row, 0).text())
            truck_number = self.trucks_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الجرار رقم {truck_number}؟\n"
                "سيتم حذف جميع البلوكات المرتبطة به أيضاً.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success, message = self.truck_model.delete_truck(truck_id, self.user_data['id'])
                if success:
                    QMessageBox.information(self, "نجح", message)
                    self.load_trucks()
                else:
                    QMessageBox.warning(self, "خطأ", message)
                    
    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.trucks_table.itemAt(position):
            menu = QMenu()
            
            edit_action = QAction("تعديل", self)
            edit_action.triggered.connect(self.edit_truck)
            menu.addAction(edit_action)
            
            delete_action = QAction("حذف", self)
            delete_action.triggered.connect(self.delete_truck)
            menu.addAction(delete_action)
            
            menu.addSeparator()
            
            view_blocks_action = QAction("عرض البلوكات", self)
            view_blocks_action.triggered.connect(self.view_truck_blocks)
            menu.addAction(view_blocks_action)
            
            menu.exec_(self.trucks_table.mapToGlobal(position))
            
    def view_truck_blocks(self):
        """عرض بلوكات الجرار"""
        current_row = self.trucks_table.currentRow()
        if current_row >= 0:
            truck_id = int(self.trucks_table.item(current_row, 0).text())
            truck_number = self.trucks_table.item(current_row, 1).text()
            
            # سيتم تطوير نافذة عرض البلوكات لاحقاً
            QMessageBox.information(
                self, "عرض البلوكات", 
                f"سيتم عرض بلوكات الجرار رقم {truck_number}\n(قيد التطوير)"
            )
            
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.setText(message)


class TruckDialog(QDialog):
    """نافذة إضافة/تعديل الجرار"""
    
    def __init__(self, parent, database_manager, truck_id=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.truck_id = truck_id
        self.truck_model = Truck(database_manager)
        
        self.setup_ui()
        if truck_id:
            self.load_truck_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة جرار جديد" if not self.truck_id else "تعديل الجرار")
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout()
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        self.truck_number_input = QLineEdit()
        self.weight_input = QDoubleSpinBox()
        self.weight_input.setRange(0.1, 1000.0)
        self.weight_input.setDecimals(2)
        self.weight_input.setSuffix(" طن")
        
        self.price_input = QDoubleSpinBox()
        self.price_input.setRange(0.01, 100000.0)
        self.price_input.setDecimals(2)
        self.price_input.setSuffix(" جنيه")
        
        self.arrival_date = QDateEdit()
        self.arrival_date.setDate(QDate.currentDate())
        self.arrival_date.setCalendarPopup(True)
        
        self.supplier_input = QLineEdit()
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        
        # حساب التكلفة الإجمالية تلقائياً
        self.total_cost_label = QLabel("0.00 جنيه")
        self.weight_input.valueChanged.connect(self.calculate_total)
        self.price_input.valueChanged.connect(self.calculate_total)
        
        form_layout.addRow("رقم الجرار:", self.truck_number_input)
        form_layout.addRow("الوزن:", self.weight_input)
        form_layout.addRow("سعر الطن:", self.price_input)
        form_layout.addRow("التكلفة الإجمالية:", self.total_cost_label)
        form_layout.addRow("تاريخ الوصول:", self.arrival_date)
        form_layout.addRow("المورد:", self.supplier_input)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_truck)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def calculate_total(self):
        """حساب التكلفة الإجمالية"""
        weight = self.weight_input.value()
        price = self.price_input.value()
        total = weight * price
        self.total_cost_label.setText(f"{total:,.2f} جنيه")
        
    def load_truck_data(self):
        """تحميل بيانات الجرار للتعديل"""
        truck = self.truck_model.get_truck_by_id(self.truck_id)
        if truck:
            self.truck_number_input.setText(truck[1])
            self.weight_input.setValue(truck[2])
            self.price_input.setValue(truck[3])
            
            # تحويل التاريخ
            if truck[5]:
                date = QDate.fromString(str(truck[5])[:10], "yyyy-MM-dd")
                self.arrival_date.setDate(date)
                
            self.supplier_input.setText(truck[6] if truck[6] else "")
            self.notes_input.setPlainText(truck[7] if truck[7] else "")
            
    def save_truck(self):
        """حفظ الجرار"""
        # جمع البيانات
        truck_data = {
            'truck_number': self.truck_number_input.text().strip(),
            'weight_tons': self.weight_input.value(),
            'price_per_ton': self.price_input.value(),
            'total_cost': self.weight_input.value() * self.price_input.value(),
            'arrival_date': self.arrival_date.date().toString("yyyy-MM-dd"),
            'supplier': self.supplier_input.text().strip(),
            'notes': self.notes_input.toPlainText().strip()
        }
        
        # التحقق من صحة البيانات
        errors = self.truck_model.validate_truck_data(truck_data)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
            
        # حفظ البيانات
        try:
            if self.truck_id:
                # تحديث
                success, message = self.truck_model.update_truck(
                    self.truck_id, truck_data, self.parent().user_data['id']
                )
            else:
                # إضافة جديد
                success, truck_id, message = self.truck_model.add_truck(
                    truck_data, self.parent().user_data['id']
                )
                
            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات:\n{str(e)}")
