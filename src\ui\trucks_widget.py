# -*- coding: utf-8 -*-
"""
واجهة إدارة الجرارات المحسنة
Modern Trucks Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QComboBox, QFrame,
                            QGridLayout, QMessageBox, QAbstractItemView,
                            QHeaderView, QTextEdit, QSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from src.utils.font_utils import apply_font_settings, get_responsive_style

class TrucksWidget(QWidget):
    """واجهة إدارة الجرارات المحسنة"""
    
    def __init__(self, database_manager=None, user_data=None):
        super().__init__()
        
        if not database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()
        else:
            self.database_manager = database_manager
            
        self.user_data = user_data or {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام'}
        
        self.setup_ui()
        self.apply_modern_styles()
        apply_font_settings(self)
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # العنوان الرئيسي
        self.create_header_section(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        self.setLayout(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # العنوان مع الأيقونة
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("🚛")
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel("إدارة استلام الجرارات")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 15px;
        """)
        
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # الأزرار السريعة
        actions_layout = QHBoxLayout()
        
        new_truck_btn = QPushButton("➕ جرار جديد")
        new_truck_btn.setObjectName("modernButton")
        new_truck_btn.clicked.connect(self.add_truck)
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setObjectName("modernButton")
        refresh_btn.clicked.connect(self.load_data)
        
        actions_layout.addWidget(new_truck_btn)
        actions_layout.addWidget(refresh_btn)
        
        header_layout.addLayout(title_layout)
        header_layout.addLayout(actions_layout)
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # إحصائيات الجرارات
        self.create_trucks_stats(content_layout)
        
        # جدول الجرارات
        self.create_trucks_table(content_layout)
        
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)
        
    def create_trucks_stats(self, layout):
        """إنشاء إحصائيات الجرارات"""
        stats_frame = QFrame()
        stats_frame.setObjectName("card")
        
        stats_layout = QGridLayout()
        
        # بيانات الإحصائيات
        stats_data = [
            ("🚛", "إجمالي الجرارات", "25", "جرار"),
            ("✅", "نشطة", "18", "جرار"),
            ("🔧", "قيد الصيانة", "4", "جرار"),
            ("❌", "خارج الخدمة", "3", "جرار"),
        ]
        
        for i, (icon, title, value, subtitle) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, subtitle)
            stats_layout.addWidget(card, 0, i)
            
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)
        
    def create_stat_card(self, icon, title, value, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(100)
        
        layout = QVBoxLayout()
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #2c3e50;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #2980b9;")
        
        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("font-size: 10px; color: #7f8c8d;")
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        
        card.setLayout(layout)
        return card
        
    def create_trucks_table(self, layout):
        """إنشاء جدول الجرارات"""
        table_frame = QFrame()
        table_frame.setObjectName("card")
        table_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("🚛 قائمة الجرارات")
        table_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.trucks_table = QTableWidget()
        self.trucks_table.setObjectName("modernTable")
        
        # إعداد أعمدة الجدول
        columns = ["رقم الجرار", "السائق", "تاريخ الوصول", "الحمولة", "الحالة", "ملاحظات", "الإجراءات"]
        self.trucks_table.setColumnCount(len(columns))
        self.trucks_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.trucks_table.setAlternatingRowColors(True)
        self.trucks_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.trucks_table.horizontalHeader().setStretchLastSection(True)
        
        table_layout.addWidget(self.trucks_table)
        table_frame.setLayout(table_layout)
        layout.addWidget(table_frame)
        
    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QFrame#headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 3px solid #2980b9;
            }
            
            QFrame#contentFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
            }
            
            QFrame#card {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
            
            QFrame#statCard {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
            
            QFrame#statCard:hover {
                border-color: #2980b9;
                box-shadow: 0 2px 8px rgba(41, 128, 185, 0.1);
            }
            
            QPushButton#modernButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }
            
            QPushButton#modernButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #21618c, stop:1 #1b4f72);
            }
            
            QTableWidget#modernTable {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                gridline-color: #f1f2f6;
            }
            
            QTableWidget#modernTable QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        # بيانات تجريبية
        trucks_data = [
            ("TR-001", "أحمد محمد", "2025-01-15", "20 طن", "نشط", "حمولة جرانيت أحمر"),
            ("TR-002", "محمد علي", "2025-01-15", "18 طن", "قيد التفريغ", "حمولة جرانيت أسود"),
            ("TR-003", "سعد أحمد", "2025-01-14", "22 طن", "مكتمل", "تم التفريغ"),
            ("TR-004", "علي حسن", "2025-01-14", "19 طن", "قيد الصيانة", "مشكلة في المحرك"),
        ]
        
        self.trucks_table.setRowCount(len(trucks_data))
        
        for row, truck in enumerate(trucks_data):
            for col, value in enumerate(truck):
                item = QTableWidgetItem(str(value))
                
                # تلوين الحالة
                if col == 4:  # عمود الحالة
                    if value == "نشط":
                        item.setBackground(Qt.green)
                        item.setForeground(Qt.white)
                    elif value == "قيد التفريغ":
                        item.setBackground(Qt.yellow)
                    elif value == "مكتمل":
                        item.setBackground(Qt.blue)
                        item.setForeground(Qt.white)
                    elif value == "قيد الصيانة":
                        item.setBackground(Qt.red)
                        item.setForeground(Qt.white)
                
                self.trucks_table.setItem(row, col, item)
            
            # إضافة أزرار الإجراءات
            actions_widget = self.create_actions_widget(row)
            self.trucks_table.setCellWidget(row, 6, actions_widget)
            
    def create_actions_widget(self, truck_id):
        """إنشاء ويدجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_truck(truck_id))
        
        complete_btn = QPushButton("✅")
        complete_btn.setToolTip("إكمال")
        complete_btn.setFixedSize(30, 30)
        complete_btn.clicked.connect(lambda: self.complete_truck(truck_id))
        
        layout.addWidget(edit_btn)
        layout.addWidget(complete_btn)
        
        widget.setLayout(layout)
        return widget
        
    def add_truck(self):
        """إضافة جرار جديد"""
        QMessageBox.information(self, "قيد التطوير", "نافذة إضافة جرار قيد التطوير")
        
    def edit_truck(self, truck_id):
        """تعديل الجرار"""
        QMessageBox.information(self, "قيد التطوير", f"تعديل الجرار رقم {truck_id}")
        
    def complete_truck(self, truck_id):
        """إكمال الجرار"""
        reply = QMessageBox.question(self, "تأكيد الإكمال", "هل تريد تسجيل إكمال هذا الجرار؟")
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم تسجيل إكمال الجرار")
            self.load_data()
