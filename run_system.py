#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام مع إصلاح الأخطاء التلقائي
Run System with Automatic Error Fixing
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_init_files():
    """إنشاء ملفات __init__.py المفقودة"""
    init_files = [
        "src/__init__.py",
        "src/models/__init__.py", 
        "src/ui/__init__.py",
        "src/database/__init__.py",
        "src/utils/__init__.py"
    ]
    
    for init_file in init_files:
        if not os.path.exists(init_file):
            os.makedirs(os.path.dirname(init_file), exist_ok=True)
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('# -*- coding: utf-8 -*-\n')

def run_system():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام إدارة مصنع الجرانيت")
    print("=" * 50)
    
    # إنشاء ملفات __init__.py
    create_init_files()
    
    try:
        # استيراد المكونات الأساسية
        import config
        print("✅ تم تحميل الإعدادات")
        
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setApplicationName(config.APP_NAME)
        app.setApplicationVersion(config.APP_VERSION)
        
        print("✅ تم إنشاء تطبيق Qt")
        
        # محاولة تحميل النافذة الرئيسية
        try:
            from src.ui.main_window import MainWindow
            from src.ui.login_dialog import LoginDialog
            
            # نافذة تسجيل الدخول
            login_dialog = LoginDialog()
            if login_dialog.exec_() == login_dialog.Accepted:
                user_data = login_dialog.get_user_data()
                
                # النافذة الرئيسية
                main_window = MainWindow(user_data)
                main_window.show()
                
                print("✅ تم تشغيل النظام بنجاح")
                print(f"👤 مرحباً {user_data.get('full_name', 'المستخدم')}")
                
                return app.exec_()
            else:
                print("❌ تم إلغاء تسجيل الدخول")
                return 1
                
        except Exception as ui_error:
            print(f"❌ خطأ في واجهة المستخدم: {str(ui_error)}")
            
            # عرض رسالة خطأ
            QMessageBox.critical(None, "خطأ", 
                               f"خطأ في تشغيل النظام:\n{str(ui_error)}\n\n"
                               "يرجى التحقق من:\n"
                               "1. تثبيت PyQt5\n"
                               "2. إعدادات قاعدة البيانات\n"
                               "3. ملفات النظام")
            return 1
            
    except ImportError as import_error:
        print(f"❌ خطأ في الاستيراد: {str(import_error)}")
        print("\n💡 حلول مقترحة:")
        print("1. تثبيت المتطلبات: pip install -r requirements.txt")
        print("2. التحقق من Python: python --version")
        print("3. التحقق من PyQt5: pip install PyQt5")
        return 1
        
    except Exception as general_error:
        print(f"❌ خطأ عام: {str(general_error)}")
        return 1

if __name__ == "__main__":
    sys.exit(run_system())
