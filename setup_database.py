#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد قاعدة البيانات
Database Setup Script
"""

import sys
import os

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.create_tables import create_database

def main():
    """الدالة الرئيسية لإعداد قاعدة البيانات"""
    print("=" * 50)
    print("برنامج الحسن ستون - إعداد قاعدة البيانات")
    print("=" * 50)
    
    try:
        print("جاري إنشاء قاعدة البيانات والجداول...")
        
        if create_database():
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
            print("✅ تم إنشاء جميع الجداول المطلوبة")
            print("✅ تم إدراج البيانات الأساسية")
            print("\nبيانات تسجيل الدخول الافتراضية:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")
            print("\n⚠️  يُنصح بتغيير كلمة المرور بعد أول تسجيل دخول")
            return 0
        else:
            print("❌ فشل في إنشاء قاعدة البيانات!")
            print("تأكد من:")
            print("- تشغيل SQL Server")
            print("- صحة إعدادات الاتصال في config.py")
            print("- وجود صلاحيات إنشاء قواعد البيانات")
            return 1
            
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
