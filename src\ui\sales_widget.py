# -*- coding: utf-8 -*-
"""
واجهة إدارة المبيعات والفواتير
Sales Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QSplitter,
                            QGroupBox, QGridLayout, QComboBox, QSpinBox,
                            QTabWidget, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

import config
from src.models.sales_invoice import SalesInvoice
from src.models.customer import Customer
from src.models.granite_type import GraniteType
from src.models.slice import Slice
from src.utils.logger import log_user_action
from src.ui.customer_dialog import CustomerDialog
from src.ui.invoice_dialog import InvoiceDialog
from src.ui.invoice_view_dialog import InvoiceViewDialog
from src.ui.payment_dialog import PaymentDialog

class SalesWidget(QWidget):
    """واجهة إدارة المبيعات والفواتير"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.invoice_model = SalesInvoice(database_manager)
        self.customer_model = Customer(database_manager)
        self.granite_type_model = GraniteType(database_manager)
        self.slice_model = Slice(database_manager)
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان والأزرار
        self.create_header(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس الواجهة"""
        header_layout = QHBoxLayout()
        
        # العنوان
        title_label = QLabel("إدارة المبيعات والفواتير")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        # الأزرار
        self.new_invoice_btn = QPushButton("فاتورة جديدة")
        self.new_invoice_btn.clicked.connect(self.create_new_invoice)
        
        self.new_customer_btn = QPushButton("عميل جديد")
        self.new_customer_btn.clicked.connect(self.create_new_customer)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_data)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.new_invoice_btn)
        header_layout.addWidget(self.new_customer_btn)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
    def create_tabs(self, layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        
        # تبويب الفواتير
        self.invoices_tab = QWidget()
        self.create_invoices_tab()
        self.tabs.addTab(self.invoices_tab, "الفواتير")
        
        # تبويب العملاء
        self.customers_tab = QWidget()
        self.create_customers_tab()
        self.tabs.addTab(self.customers_tab, "العملاء")
        
        # تبويب الإحصائيات
        self.stats_tab = QWidget()
        self.create_sales_stats_tab()
        self.tabs.addTab(self.stats_tab, "إحصائيات المبيعات")
        
        layout.addWidget(self.tabs)
        
    def create_invoices_tab(self):
        """إنشاء تبويب الفواتير"""
        layout = QVBoxLayout()
        
        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()
        
        self.invoices_search = QLineEdit()
        self.invoices_search.setPlaceholderText("البحث في الفواتير...")
        self.invoices_search.textChanged.connect(self.search_invoices)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "معلقة", "مدفوعة جزئياً", "مدفوعة كاملة"])
        self.status_filter.currentTextChanged.connect(self.filter_invoices)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.invoices_search)
        search_layout.addWidget(QLabel("الحالة:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addStretch()
        
        layout.addLayout(search_layout)
        
        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(10)
        
        headers = ["المعرف", "رقم الفاتورة", "العميل", "تاريخ الفاتورة", 
                  "الإجمالي", "المدفوع", "المتبقي", "الحالة", "تاريخ الاستحقاق", "تاريخ الإنشاء"]
        self.invoices_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.invoices_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        # قائمة السياق
        self.invoices_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.invoices_table.customContextMenuRequested.connect(self.show_invoices_context_menu)
        
        # النقر المزدوج للعرض
        self.invoices_table.doubleClicked.connect(self.view_invoice)
        
        layout.addWidget(self.invoices_table)
        
        self.invoices_tab.setLayout(layout)
        
    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        layout = QVBoxLayout()
        
        # منطقة البحث
        search_layout = QHBoxLayout()
        
        self.customers_search = QLineEdit()
        self.customers_search.setPlaceholderText("البحث في العملاء...")
        self.customers_search.textChanged.connect(self.search_customers)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.customers_search)
        search_layout.addStretch()
        
        layout.addLayout(search_layout)
        
        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(8)
        
        headers = ["المعرف", "الاسم", "الهاتف", "البريد الإلكتروني", 
                  "الرصيد الحالي", "إجمالي المشتريات", "عدد الفواتير", "تاريخ التسجيل"]
        self.customers_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.customers_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        # قائمة السياق
        self.customers_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customers_table.customContextMenuRequested.connect(self.show_customers_context_menu)
        
        # النقر المزدوج للتعديل
        self.customers_table.doubleClicked.connect(self.edit_customer)
        
        layout.addWidget(self.customers_table)
        
        self.customers_tab.setLayout(layout)
        
    def create_sales_stats_tab(self):
        """إنشاء تبويب إحصائيات المبيعات"""
        layout = QVBoxLayout()
        
        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        
        # إحصائيات المبيعات
        sales_group = QGroupBox("إحصائيات المبيعات")
        sales_layout = QVBoxLayout()
        
        self.total_sales_label = QLabel("إجمالي المبيعات: 0 جنيه")
        self.sales_this_month_label = QLabel("مبيعات هذا الشهر: 0 جنيه")
        self.total_invoices_label = QLabel("إجمالي الفواتير: 0")
        self.pending_invoices_label = QLabel("الفواتير المعلقة: 0")
        self.total_debt_label = QLabel("إجمالي المديونية: 0 جنيه")
        
        sales_layout.addWidget(self.total_sales_label)
        sales_layout.addWidget(self.sales_this_month_label)
        sales_layout.addWidget(self.total_invoices_label)
        sales_layout.addWidget(self.pending_invoices_label)
        sales_layout.addWidget(self.total_debt_label)
        sales_group.setLayout(sales_layout)
        
        # إحصائيات العملاء
        customers_group = QGroupBox("إحصائيات العملاء")
        customers_layout = QVBoxLayout()
        
        self.total_customers_label = QLabel("إجمالي العملاء: 0")
        self.new_customers_label = QLabel("عملاء جدد هذا الشهر: 0")
        self.customer_debt_label = QLabel("مديونية العملاء: 0 جنيه")
        
        customers_layout.addWidget(self.total_customers_label)
        customers_layout.addWidget(self.new_customers_label)
        customers_layout.addWidget(self.customer_debt_label)
        customers_group.setLayout(customers_layout)
        
        stats_layout.addWidget(sales_group, 0, 0)
        stats_layout.addWidget(customers_group, 0, 1)
        
        layout.addLayout(stats_layout)
        layout.addStretch()
        
        self.stats_tab.setLayout(layout)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QLineEdit, QComboBox {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
            
            QTabWidget::pane {{
                border: 1px solid #BDC3C7;
                background-color: white;
            }}
            
            QTabBar::tab {{
                background-color: #ECF0F1;
                padding: 8px 15px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_invoices()
        self.load_customers()
        self.load_statistics()
        
    def load_invoices(self):
        """تحميل الفواتير"""
        try:
            invoices = self.invoice_model.get_all_invoices()
            self.populate_invoices_table(invoices)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الفواتير:\n{str(e)}")
            
    def populate_invoices_table(self, invoices):
        """ملء جدول الفواتير"""
        self.invoices_table.setRowCount(len(invoices))
        
        for row, invoice in enumerate(invoices):
            items = [
                str(invoice[0]),  # ID
                str(invoice[1]),  # invoice_number
                str(invoice[12]),  # customer_name
                str(invoice[2])[:10] if invoice[2] else "",  # invoice_date
                f"{invoice[7]:,.2f}",  # total_amount
                f"{invoice[8]:,.2f}",  # paid_amount
                f"{invoice[14]:,.2f}",  # remaining_amount
                self.get_status_text(invoice[9]),  # status
                str(invoice[3])[:10] if invoice[3] else "",  # due_date
                str(invoice[11])[:10] if invoice[11] else ""  # created_at
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 4, 5, 6]:  # أعمدة رقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.invoices_table.setItem(row, col, item)
                
    def get_status_text(self, status):
        """تحويل حالة الفاتورة إلى نص عربي"""
        status_map = {
            'pending': 'معلقة',
            'partial': 'مدفوعة جزئياً',
            'paid': 'مدفوعة كاملة'
        }
        return status_map.get(status, status)
        
    def load_customers(self):
        """تحميل العملاء"""
        try:
            customers = self.customer_model.get_all_customers()
            self.populate_customers_table(customers)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل العملاء:\n{str(e)}")
            
    def populate_customers_table(self, customers):
        """ملء جدول العملاء"""
        self.customers_table.setRowCount(len(customers))
        
        for row, customer in enumerate(customers):
            items = [
                str(customer[0]),  # ID
                str(customer[1]),  # name
                str(customer[2]) if customer[2] else "",  # phone
                str(customer[4]) if customer[4] else "",  # email
                f"{customer[7]:,.2f}",  # current_balance
                f"{customer[13]:,.2f}",  # total_purchases
                str(customer[12]),  # total_invoices
                str(customer[10])[:10] if customer[10] else ""  # created_at
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 4, 5, 6]:  # أعمدة رقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.customers_table.setItem(row, col, item)
                
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # إحصائيات المبيعات
            sales_stats = self.invoice_model.get_sales_statistics()
            self.total_sales_label.setText(f"إجمالي المبيعات: {sales_stats.get('total_sales', 0):,.2f} جنيه")
            self.sales_this_month_label.setText(f"مبيعات هذا الشهر: {sales_stats.get('sales_this_month', 0):,.2f} جنيه")
            self.total_invoices_label.setText(f"إجمالي الفواتير: {sales_stats.get('total_invoices', 0)}")
            self.pending_invoices_label.setText(f"الفواتير المعلقة: {sales_stats.get('pending_invoices', 0)}")
            self.total_debt_label.setText(f"إجمالي المديونية: {sales_stats.get('total_debt', 0):,.2f} جنيه")
            
            # إحصائيات العملاء
            customer_stats = self.customer_model.get_customer_statistics()
            self.total_customers_label.setText(f"إجمالي العملاء: {customer_stats.get('total_customers', 0)}")
            self.new_customers_label.setText(f"عملاء جدد هذا الشهر: {customer_stats.get('new_customers_this_month', 0)}")
            self.customer_debt_label.setText(f"مديونية العملاء: {customer_stats.get('total_debt', 0):,.2f} جنيه")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الإحصائيات:\n{str(e)}")
            
    def create_new_invoice(self):
        """إنشاء فاتورة جديدة"""
        dialog = InvoiceDialog(self, self.database_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()

    def create_new_customer(self):
        """إنشاء عميل جديد"""
        dialog = CustomerDialog(self, self.database_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
        
    def view_invoice(self):
        """عرض تفاصيل الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            invoice_id = int(self.invoices_table.item(current_row, 0).text())
            dialog = InvoiceViewDialog(self, self.database_manager, invoice_id)
            dialog.exec_()
        
    def edit_customer(self):
        """تعديل العميل"""
        current_row = self.customers_table.currentRow()
        if current_row >= 0:
            customer_id = int(self.customers_table.item(current_row, 0).text())
            dialog = CustomerDialog(self, self.database_manager, self.user_data, customer_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()
        
    def search_invoices(self):
        """البحث في الفواتير"""
        search_term = self.invoices_search.text().strip()
        if search_term:
            invoices = self.invoice_model.search_invoices(search_term)
            self.populate_invoices_table(invoices)
        else:
            self.load_invoices()
            
    def search_customers(self):
        """البحث في العملاء"""
        search_term = self.customers_search.text().strip()
        if search_term:
            customers = self.customer_model.search_customers(search_term)
            self.populate_customers_table(customers)
        else:
            self.load_customers()
            
    def filter_invoices(self):
        """فلترة الفواتير حسب الحالة"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        pass
        
    def show_invoices_context_menu(self, position):
        """عرض قائمة السياق للفواتير"""
        if self.invoices_table.itemAt(position):
            menu = QMenu()
            
            view_action = QAction("عرض التفاصيل", self)
            view_action.triggered.connect(self.view_invoice)
            menu.addAction(view_action)
            
            print_action = QAction("طباعة", self)
            print_action.triggered.connect(self.print_invoice)
            menu.addAction(print_action)
            
            payment_action = QAction("إضافة دفعة", self)
            payment_action.triggered.connect(self.add_payment)
            menu.addAction(payment_action)
            
            menu.exec_(self.invoices_table.mapToGlobal(position))
            
    def show_customers_context_menu(self, position):
        """عرض قائمة السياق للعملاء"""
        if self.customers_table.itemAt(position):
            menu = QMenu()
            
            edit_action = QAction("تعديل", self)
            edit_action.triggered.connect(self.edit_customer)
            menu.addAction(edit_action)
            
            invoices_action = QAction("عرض الفواتير", self)
            invoices_action.triggered.connect(self.view_customer_invoices)
            menu.addAction(invoices_action)
            
            menu.exec_(self.customers_table.mapToGlobal(position))
            
    def print_invoice(self):
        """طباعة الفاتورة"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير وظيفة الطباعة قريباً")
        
    def add_payment(self):
        """إضافة دفعة للفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            invoice_id = int(self.invoices_table.item(current_row, 0).text())
            dialog = PaymentDialog(self, self.database_manager, self.user_data, invoice_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()
        
    def view_customer_invoices(self):
        """عرض فواتير العميل"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة فواتير العميل قريباً")
