# -*- coding: utf-8 -*-
"""
واجهة عمليات النشر والتقطيع المحسنة
Modern Cutting Operations Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QFrame, QGridLayout, QMessageBox, QTabWidget,
                            QAbstractItemView, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from src.utils.font_utils import apply_font_settings, get_responsive_style

class CuttingWidget(QWidget):
    """واجهة عمليات النشر والتقطيع المحسنة"""
    
    def __init__(self, database_manager=None, user_data=None):
        super().__init__()
        
        if not database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()
        else:
            self.database_manager = database_manager
            
        self.user_data = user_data or {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام'}
        
        self.setup_ui()
        self.apply_modern_styles()
        apply_font_settings(self)
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # العنوان الرئيسي
        self.create_header_section(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        self.setLayout(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # العنوان مع الأيقونة
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("⚡")
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel("عمليات النشر والتقطيع")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 15px;
        """)
        
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # الأزرار السريعة
        actions_layout = QHBoxLayout()
        
        new_cutting_btn = QPushButton("➕ عملية نشر")
        new_cutting_btn.setObjectName("modernButton")
        new_cutting_btn.clicked.connect(self.add_cutting_operation)
        
        payments_btn = QPushButton("💰 دفعات العملاء")
        payments_btn.setObjectName("successButton")
        payments_btn.clicked.connect(self.show_customer_payments)
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setObjectName("modernButton")
        refresh_btn.clicked.connect(self.load_data)
        
        actions_layout.addWidget(new_cutting_btn)
        actions_layout.addWidget(payments_btn)
        actions_layout.addWidget(refresh_btn)
        
        header_layout.addLayout(title_layout)
        header_layout.addLayout(actions_layout)
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # التبويبات
        self.create_tabs_section(content_layout)
        
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)
        
    def create_tabs_section(self, layout):
        """إنشاء قسم التبويبات"""
        self.tabs = QTabWidget()
        self.tabs.setObjectName("modernTabs")
        
        # تبويب عمليات النشر
        self.create_cutting_operations_tab()
        
        # تبويب دفعات العملاء
        self.create_customer_payments_tab()
        
        # تبويب الإحصائيات
        self.create_cutting_stats_tab()
        
        layout.addWidget(self.tabs)
        
    def create_cutting_operations_tab(self):
        """إنشاء تبويب عمليات النشر"""
        operations_widget = QWidget()
        operations_layout = QVBoxLayout()
        operations_layout.setContentsMargins(15, 15, 15, 15)
        
        # جدول العمليات
        self.create_operations_table(operations_layout)
        
        operations_widget.setLayout(operations_layout)
        self.tabs.addTab(operations_widget, "⚡ عمليات النشر")
        
    def create_operations_table(self, layout):
        """إنشاء جدول عمليات النشر"""
        table_frame = QFrame()
        table_frame.setObjectName("card")
        table_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("⚡ قائمة عمليات النشر")
        table_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.operations_table = QTableWidget()
        self.operations_table.setObjectName("modernTable")
        
        # إعداد أعمدة الجدول
        columns = ["رقم العملية", "العميل", "نوع الجرانيت", "التاريخ", "الكمية", "التكلفة", "الحالة", "الإجراءات"]
        self.operations_table.setColumnCount(len(columns))
        self.operations_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.operations_table.setAlternatingRowColors(True)
        self.operations_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.operations_table.horizontalHeader().setStretchLastSection(True)
        
        table_layout.addWidget(self.operations_table)
        table_frame.setLayout(table_layout)
        layout.addWidget(table_frame)
        
    def create_customer_payments_tab(self):
        """إنشاء تبويب دفعات العملاء"""
        payments_widget = QWidget()
        payments_layout = QVBoxLayout()
        payments_layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان القسم
        title = QLabel("💰 دفعات النشر للعملاء")
        title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        payments_layout.addWidget(title)
        
        # أزرار إدارة الدفعات
        buttons_layout = QHBoxLayout()
        
        view_payments_btn = QPushButton("👁️ عرض الدفعات")
        view_payments_btn.setObjectName("modernButton")
        view_payments_btn.clicked.connect(self.view_all_payments)
        
        add_payment_btn = QPushButton("➕ إضافة دفعة")
        add_payment_btn.setObjectName("successButton")
        add_payment_btn.clicked.connect(self.add_customer_payment)
        
        buttons_layout.addWidget(view_payments_btn)
        buttons_layout.addWidget(add_payment_btn)
        buttons_layout.addStretch()
        
        payments_layout.addLayout(buttons_layout)
        
        # محتوى مؤقت
        content = QLabel("استخدم الأزرار أعلاه لإدارة دفعات النشر للعملاء")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("font-size: 14px; color: #7f8c8d; padding: 50px;")
        payments_layout.addWidget(content)
        
        payments_widget.setLayout(payments_layout)
        self.tabs.addTab(payments_widget, "💰 دفعات العملاء")
        
    def create_cutting_stats_tab(self):
        """إنشاء تبويب إحصائيات النشر"""
        stats_widget = QWidget()
        stats_layout = QVBoxLayout()
        stats_layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان القسم
        title = QLabel("📊 إحصائيات النشر")
        title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        stats_layout.addWidget(title)
        
        # بطاقات الإحصائيات
        self.create_cutting_stats_cards(stats_layout)
        
        stats_widget.setLayout(stats_layout)
        self.tabs.addTab(stats_widget, "📊 الإحصائيات")
        
    def create_cutting_stats_cards(self, layout):
        """إنشاء بطاقات إحصائيات النشر"""
        cards_layout = QGridLayout()
        
        # بيانات الإحصائيات
        stats_data = [
            ("⚡", "عمليات النشر", "35", "عملية هذا الشهر"),
            ("💰", "إجمالي الإيرادات", "85,000", "ريال"),
            ("👥", "عملاء النشر", "12", "عميل نشط"),
            ("📈", "متوسط التكلفة", "2,400", "ريال للعملية"),
        ]
        
        for i, (icon, title, value, subtitle) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, subtitle)
            row = i // 2
            col = i % 2
            cards_layout.addWidget(card, row, col)
            
        layout.addLayout(cards_layout)
        
    def create_stat_card(self, icon, title, value, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(120)
        
        layout = QVBoxLayout()
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #16a085; margin: 10px 0;")
        
        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        
        card.setLayout(layout)
        return card
        
    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QFrame#headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 3px solid #16a085;
            }
            
            QFrame#contentFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
            }
            
            QFrame#card {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
            
            QFrame#statCard {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
            
            QFrame#statCard:hover {
                border-color: #16a085;
                box-shadow: 0 4px 15px rgba(22, 160, 133, 0.1);
            }
            
            QPushButton#modernButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #16a085, stop:1 #138d75);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }
            
            QPushButton#modernButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138d75, stop:1 #117a65);
            }
            
            QPushButton#successButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
            }
            
            QPushButton#successButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
            
            QTabWidget#modernTabs::pane {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                margin-top: 5px;
            }
            
            QTabWidget#modernTabs QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 500;
            }
            
            QTabWidget#modernTabs QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #16a085, stop:1 #138d75);
                color: white;
            }
            
            QTableWidget#modernTable {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                gridline-color: #f1f2f6;
            }
            
            QTableWidget#modernTable QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #16a085, stop:1 #138d75);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        # بيانات تجريبية لعمليات النشر
        operations_data = [
            ("CUT-001", "أحمد محمد", "جرانيت أحمر", "2025-01-15", "5 متر", "2,500", "مكتمل"),
            ("CUT-002", "سارة أحمد", "جرانيت أسود", "2025-01-14", "3 متر", "1,800", "قيد التنفيذ"),
            ("CUT-003", "محمد علي", "جرانيت رمادي", "2025-01-13", "7 متر", "3,200", "مكتمل"),
            ("CUT-004", "فاطمة حسن", "جرانيت أبيض", "2025-01-12", "4 متر", "2,000", "قيد التنفيذ"),
        ]
        
        self.operations_table.setRowCount(len(operations_data))
        
        for row, operation in enumerate(operations_data):
            for col, value in enumerate(operation):
                item = QTableWidgetItem(str(value))
                
                # تلوين الحالة
                if col == 6:  # عمود الحالة
                    if value == "مكتمل":
                        item.setBackground(Qt.green)
                        item.setForeground(Qt.white)
                    elif value == "قيد التنفيذ":
                        item.setBackground(Qt.yellow)
                
                self.operations_table.setItem(row, col, item)
            
            # إضافة أزرار الإجراءات
            actions_widget = self.create_actions_widget(row)
            self.operations_table.setCellWidget(row, 7, actions_widget)
            
    def create_actions_widget(self, operation_id):
        """إنشاء ويدجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_operation(operation_id))
        
        complete_btn = QPushButton("✅")
        complete_btn.setToolTip("إكمال")
        complete_btn.setFixedSize(30, 30)
        complete_btn.clicked.connect(lambda: self.complete_operation(operation_id))
        
        layout.addWidget(edit_btn)
        layout.addWidget(complete_btn)
        
        widget.setLayout(layout)
        return widget
        
    def add_cutting_operation(self):
        """إضافة عملية نشر جديدة"""
        QMessageBox.information(self, "قيد التطوير", "نافذة إضافة عملية نشر قيد التطوير")
        
    def show_customer_payments(self):
        """عرض دفعات العملاء"""
        try:
            from src.ui.cutting_payment_dialog import CuttingPaymentDialog
            dialog = CuttingPaymentDialog(self, self.database_manager, self.user_data, customer_id=1)
            dialog.exec_()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في عرض دفعات العملاء: {str(e)}")
        
    def view_all_payments(self):
        """عرض جميع الدفعات"""
        QMessageBox.information(self, "قيد التطوير", "نافذة عرض جميع الدفعات قيد التطوير")
        
    def add_customer_payment(self):
        """إضافة دفعة عميل"""
        QMessageBox.information(self, "قيد التطوير", "نافذة إضافة دفعة عميل قيد التطوير")
        
    def edit_operation(self, operation_id):
        """تعديل العملية"""
        QMessageBox.information(self, "قيد التطوير", f"تعديل العملية رقم {operation_id}")
        
    def complete_operation(self, operation_id):
        """إكمال العملية"""
        reply = QMessageBox.question(self, "تأكيد الإكمال", "هل تريد تسجيل إكمال هذه العملية؟")
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم تسجيل إكمال العملية")
            self.load_data()
