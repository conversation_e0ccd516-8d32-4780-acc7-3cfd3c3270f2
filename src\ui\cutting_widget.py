# -*- coding: utf-8 -*-
"""
واجهة قسم النشر
Cutting Operations Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QSplitter,
                            QGroupBox, QGridLayout, QComboBox, QSpinBox,
                            QTabWidget)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

import config
from src.models.cutting_operation import CuttingOperation
from src.models.slice import Slice
from src.models.block import Block
from src.models.granite_type import GraniteType
from src.utils.logger import log_user_action
from src.ui.granite_types_dialog import GraniteTypesDialog
from src.ui.cutting_payment_dialog import CuttingPaymentDialog

class CuttingWidget(QWidget):
    """واجهة قسم النشر"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.cutting_model = CuttingOperation(database_manager)
        self.slice_model = Slice(database_manager)
        self.block_model = Block(database_manager)
        self.granite_type_model = GraniteType(database_manager)
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان والأزرار
        self.create_header(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس الواجهة"""
        header_layout = QHBoxLayout()
        
        # العنوان
        title_label = QLabel("قسم النشر وإدارة العمليات")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        # الأزرار
        self.add_operation_btn = QPushButton("إضافة عملية نشر")
        self.add_operation_btn.clicked.connect(self.add_cutting_operation)
        
        self.add_slice_btn = QPushButton("إضافة شريحة")
        self.add_slice_btn.clicked.connect(self.add_slice)

        self.manage_types_btn = QPushButton("إدارة أنواع الجرانيت")
        self.manage_types_btn.clicked.connect(self.manage_granite_types)

        self.customer_payments_btn = QPushButton("دفعات العملاء")
        self.customer_payments_btn.clicked.connect(self.manage_customer_payments)

        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_data)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.add_operation_btn)
        header_layout.addWidget(self.add_slice_btn)
        header_layout.addWidget(self.manage_types_btn)
        header_layout.addWidget(self.customer_payments_btn)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
    def create_tabs(self, layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        
        # تبويب عمليات النشر
        self.operations_tab = QWidget()
        self.create_operations_tab()
        self.tabs.addTab(self.operations_tab, "عمليات النشر")
        
        # تبويب الشرائح
        self.slices_tab = QWidget()
        self.create_slices_tab()
        self.tabs.addTab(self.slices_tab, "الشرائح")
        
        # تبويب الإحصائيات
        self.stats_tab = QWidget()
        self.create_stats_tab()
        self.tabs.addTab(self.stats_tab, "الإحصائيات")
        
        layout.addWidget(self.tabs)
        
    def create_operations_tab(self):
        """إنشاء تبويب عمليات النشر"""
        layout = QVBoxLayout()
        
        # منطقة البحث
        search_layout = QHBoxLayout()
        self.operations_search = QLineEdit()
        self.operations_search.setPlaceholderText("البحث في عمليات النشر...")
        self.operations_search.textChanged.connect(self.search_operations)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.operations_search)
        search_layout.addStretch()
        
        layout.addLayout(search_layout)
        
        # جدول عمليات النشر
        self.operations_table = QTableWidget()
        self.operations_table.setColumnCount(8)
        
        headers = ["المعرف", "رقم البلوك", "نوع الجرانيت", "تاريخ العملية", 
                  "الشرائح المنتجة", "نسبة الفاقد %", "المشغل", "تاريخ الإدخال"]
        self.operations_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.operations_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.operations_table.setAlternatingRowColors(True)
        self.operations_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.operations_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        # قائمة السياق
        self.operations_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.operations_table.customContextMenuRequested.connect(self.show_operations_context_menu)
        
        # النقر المزدوج للتعديل
        self.operations_table.doubleClicked.connect(self.edit_cutting_operation)
        
        layout.addWidget(self.operations_table)
        
        self.operations_tab.setLayout(layout)
        
    def create_slices_tab(self):
        """إنشاء تبويب الشرائح"""
        layout = QVBoxLayout()
        
        # منطقة البحث والفلترة
        filter_layout = QHBoxLayout()
        
        self.slices_search = QLineEdit()
        self.slices_search.setPlaceholderText("البحث في الشرائح...")
        self.slices_search.textChanged.connect(self.search_slices)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "متاح", "محجوز", "مباع"])
        self.status_filter.currentTextChanged.connect(self.filter_slices)
        
        filter_layout.addWidget(QLabel("البحث:"))
        filter_layout.addWidget(self.slices_search)
        filter_layout.addWidget(QLabel("الحالة:"))
        filter_layout.addWidget(self.status_filter)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # جدول الشرائح
        self.slices_table = QTableWidget()
        self.slices_table.setColumnCount(10)
        
        headers = ["المعرف", "رقم الشريحة", "رقم البلوك", "نوع الجرانيت", 
                  "الطول (سم)", "العرض (سم)", "السمك (سم)", "المساحة (م²)", 
                  "الحالة", "السعر/م²"]
        self.slices_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.slices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.slices_table.setAlternatingRowColors(True)
        self.slices_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.slices_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        # قائمة السياق
        self.slices_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.slices_table.customContextMenuRequested.connect(self.show_slices_context_menu)
        
        # النقر المزدوج للتعديل
        self.slices_table.doubleClicked.connect(self.edit_slice)
        
        layout.addWidget(self.slices_table)
        
        self.slices_tab.setLayout(layout)
        
    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        layout = QVBoxLayout()
        
        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        
        # إحصائيات عمليات النشر
        operations_group = QGroupBox("إحصائيات عمليات النشر")
        operations_layout = QVBoxLayout()
        
        self.total_operations_label = QLabel("إجمالي العمليات: 0")
        self.total_slices_produced_label = QLabel("إجمالي الشرائح المنتجة: 0")
        self.avg_waste_label = QLabel("متوسط نسبة الفاقد: 0%")
        self.operations_this_month_label = QLabel("عمليات هذا الشهر: 0")
        
        operations_layout.addWidget(self.total_operations_label)
        operations_layout.addWidget(self.total_slices_produced_label)
        operations_layout.addWidget(self.avg_waste_label)
        operations_layout.addWidget(self.operations_this_month_label)
        operations_group.setLayout(operations_layout)
        
        # إحصائيات الشرائح
        slices_group = QGroupBox("إحصائيات الشرائح")
        slices_layout = QVBoxLayout()
        
        self.total_slices_label = QLabel("إجمالي الشرائح: 0")
        self.available_slices_label = QLabel("الشرائح المتاحة: 0")
        self.sold_slices_label = QLabel("الشرائح المباعة: 0")
        self.total_area_label = QLabel("إجمالي المساحة المتاحة: 0 م²")
        
        slices_layout.addWidget(self.total_slices_label)
        slices_layout.addWidget(self.available_slices_label)
        slices_layout.addWidget(self.sold_slices_label)
        slices_layout.addWidget(self.total_area_label)
        slices_group.setLayout(slices_layout)
        
        stats_layout.addWidget(operations_group, 0, 0)
        stats_layout.addWidget(slices_group, 0, 1)
        
        layout.addLayout(stats_layout)
        layout.addStretch()
        
        self.stats_tab.setLayout(layout)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QLineEdit, QComboBox {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
            
            QTabWidget::pane {{
                border: 1px solid #BDC3C7;
                background-color: white;
            }}
            
            QTabBar::tab {{
                background-color: #ECF0F1;
                padding: 8px 15px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_operations()
        self.load_slices()
        self.load_statistics()
        
    def load_operations(self):
        """تحميل عمليات النشر"""
        try:
            operations = self.cutting_model.get_all_cutting_operations()
            self.populate_operations_table(operations)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل عمليات النشر:\n{str(e)}")
            
    def populate_operations_table(self, operations):
        """ملء جدول عمليات النشر"""
        self.operations_table.setRowCount(len(operations))
        
        for row, operation in enumerate(operations):
            items = [
                str(operation[0]),  # ID
                str(operation[8]),  # block_number
                str(operation[9]),  # granite_type_name
                str(operation[1])[:10] if operation[1] else "",  # operation_date
                str(operation[2]),  # slices_produced
                f"{operation[3]:.1f}" if operation[3] else "0.0",  # waste_percentage
                str(operation[4]) if operation[4] else "",  # operator_name
                str(operation[7])[:10] if operation[7] else ""  # created_at
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 4, 5]:  # أعمدة رقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.operations_table.setItem(row, col, item)
                
    def load_slices(self):
        """تحميل الشرائح"""
        try:
            slices = self.slice_model.get_all_slices()
            self.populate_slices_table(slices)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الشرائح:\n{str(e)}")
            
    def populate_slices_table(self, slices):
        """ملء جدول الشرائح"""
        self.slices_table.setRowCount(len(slices))
        
        for row, slice_data in enumerate(slices):
            items = [
                str(slice_data[0]),  # ID
                str(slice_data[1]),  # slice_number
                str(slice_data[11]),  # block_number
                str(slice_data[12]),  # granite_type_name
                f"{slice_data[2]:.1f}",  # length_cm
                f"{slice_data[3]:.1f}",  # width_cm
                f"{slice_data[4]:.1f}",  # thickness_cm
                f"{slice_data[14]:.2f}" if slice_data[14] else "0.00",  # area_sqm
                str(slice_data[6]),  # status
                f"{slice_data[8]:.2f}" if slice_data[8] else ""  # price_per_sqm
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 4, 5, 6, 7, 9]:  # أعمدة رقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.slices_table.setItem(row, col, item)
                
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # إحصائيات عمليات النشر
            cutting_stats = self.cutting_model.get_cutting_statistics()
            self.total_operations_label.setText(f"إجمالي العمليات: {cutting_stats.get('total_operations', 0)}")
            self.total_slices_produced_label.setText(f"إجمالي الشرائح المنتجة: {cutting_stats.get('total_slices', 0)}")
            self.avg_waste_label.setText(f"متوسط نسبة الفاقد: {cutting_stats.get('avg_waste_percentage', 0):.1f}%")
            self.operations_this_month_label.setText(f"عمليات هذا الشهر: {cutting_stats.get('operations_this_month', 0)}")
            
            # إحصائيات الشرائح
            slice_stats = self.slice_model.get_slice_statistics()
            self.total_slices_label.setText(f"إجمالي الشرائح: {slice_stats.get('total_slices', 0)}")
            self.available_slices_label.setText(f"الشرائح المتاحة: {slice_stats.get('available_slices', 0)}")
            self.sold_slices_label.setText(f"الشرائح المباعة: {slice_stats.get('sold_slices', 0)}")
            self.total_area_label.setText(f"إجمالي المساحة المتاحة: {slice_stats.get('total_available_area', 0):.2f} م²")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الإحصائيات:\n{str(e)}")
            
    def search_operations(self):
        """البحث في عمليات النشر"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        pass
        
    def search_slices(self):
        """البحث في الشرائح"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        pass
        
    def filter_slices(self):
        """فلترة الشرائح حسب الحالة"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        pass
        
    def add_cutting_operation(self):
        """إضافة عملية نشر جديدة"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة إضافة عملية النشر قريباً")
        
    def add_slice(self):
        """إضافة شريحة جديدة"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة إضافة الشريحة قريباً")

    def manage_granite_types(self):
        """إدارة أنواع الجرانيت"""
        dialog = GraniteTypesDialog(self, self.database_manager, self.user_data)
        dialog.exec_()

    def manage_customer_payments(self):
        """إدارة دفعات العملاء لعمليات النشر"""
        # يمكن إضافة نافذة اختيار العميل أولاً
        QMessageBox.information(self, "دفعات العملاء",
                              "لإدارة دفعات عميل معين:\n"
                              "1. اذهب إلى قسم المبيعات\n"
                              "2. اختر العميل\n"
                              "3. انقر على 'دفعات النشر'\n\n"
                              "أو سيتم تطوير نافذة اختيار العميل هنا قريباً")
        
    def edit_cutting_operation(self):
        """تعديل عملية النشر"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة تعديل عملية النشر قريباً")
        
    def edit_slice(self):
        """تعديل الشريحة"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة تعديل الشريحة قريباً")
        
    def show_operations_context_menu(self, position):
        """عرض قائمة السياق لعمليات النشر"""
        if self.operations_table.itemAt(position):
            menu = QMenu()
            
            edit_action = QAction("تعديل", self)
            edit_action.triggered.connect(self.edit_cutting_operation)
            menu.addAction(edit_action)
            
            delete_action = QAction("حذف", self)
            delete_action.triggered.connect(self.delete_cutting_operation)
            menu.addAction(delete_action)
            
            menu.exec_(self.operations_table.mapToGlobal(position))
            
    def show_slices_context_menu(self, position):
        """عرض قائمة السياق للشرائح"""
        if self.slices_table.itemAt(position):
            menu = QMenu()
            
            edit_action = QAction("تعديل", self)
            edit_action.triggered.connect(self.edit_slice)
            menu.addAction(edit_action)
            
            status_menu = menu.addMenu("تغيير الحالة")
            
            available_action = QAction("متاح", self)
            available_action.triggered.connect(lambda: self.change_slice_status('available'))
            status_menu.addAction(available_action)
            
            reserved_action = QAction("محجوز", self)
            reserved_action.triggered.connect(lambda: self.change_slice_status('reserved'))
            status_menu.addAction(reserved_action)
            
            sold_action = QAction("مباع", self)
            sold_action.triggered.connect(lambda: self.change_slice_status('sold'))
            status_menu.addAction(sold_action)
            
            menu.exec_(self.slices_table.mapToGlobal(position))
            
    def delete_cutting_operation(self):
        """حذف عملية النشر"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير وظيفة حذف عملية النشر قريباً")
        
    def change_slice_status(self, new_status):
        """تغيير حالة الشريحة"""
        current_row = self.slices_table.currentRow()
        if current_row >= 0:
            slice_id = int(self.slices_table.item(current_row, 0).text())
            slice_number = self.slices_table.item(current_row, 1).text()
            
            success, message = self.slice_model.update_slice_status(
                slice_id, new_status, self.user_data['id']
            )
            
            if success:
                QMessageBox.information(self, "نجح", message)
                self.load_slices()
            else:
                QMessageBox.warning(self, "خطأ", message)
