#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لبرنامج الحسن ستون
Quick Test for Al-Hassan Stone ERP
"""

import sys
import os

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔄 اختبار استيراد المكتبات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ PyQt5 - فشل الاستيراد: {e}")
        return False
        
    try:
        import sqlite3
        print("✅ SQLite3 - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ SQLite3 - فشل الاستيراد: {e}")
        return False
        
    try:
        import config
        print("✅ Config - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ Config - فشل الاستيراد: {e}")
        return False
        
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🔄 اختبار قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager()
        
        if db_manager.test_connection():
            print("✅ الاتصال بقاعدة البيانات - نجح")
        else:
            print("❌ الاتصال بقاعدة البيانات - فشل")
            return False
            
        if db_manager.setup_database():
            print("✅ إعداد قاعدة البيانات - نجح")
        else:
            print("❌ إعداد قاعدة البيانات - فشل")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    print("\n🔄 اختبار النماذج...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        from src.models.user import User
        from src.models.truck import Truck
        from src.models.block import Block
        from src.models.granite_type import GraniteType
        
        db_manager = SQLiteManager()
        
        # اختبار نموذج المستخدم
        user_model = User(db_manager)
        user_data = user_model.authenticate("admin", "admin123")
        
        if user_data:
            print("✅ نموذج المستخدم - نجح")
            print(f"   المستخدم: {user_data['full_name']}")
        else:
            print("❌ نموذج المستخدم - فشل")
            return False
            
        # اختبار نموذج أنواع الجرانيت
        granite_model = GraniteType(db_manager)
        granite_types = granite_model.get_all_granite_types()
        
        if granite_types:
            print(f"✅ نموذج أنواع الجرانيت - نجح ({len(granite_types)} نوع)")
        else:
            print("❌ نموذج أنواع الجرانيت - فشل")
            
        print("✅ جميع النماذج - تم اختبارها بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النماذج: {e}")
        return False

def test_ui():
    """اختبار واجهة المستخدم"""
    print("\n🔄 اختبار واجهة المستخدم...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        from src.ui.dashboard_widget import DashboardWidget
        from src.ui.trucks_widget import TrucksWidget
        from src.ui.cutting_widget import CuttingWidget
        
        print("✅ جميع واجهات المستخدم - تم استيرادها بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار سريع لبرنامج الحسن ستون")
    print("   Quick Test for Al-Hassan Stone ERP")
    print("=" * 60)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return 1
        
    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ فشل في اختبار قاعدة البيانات")
        return 1
        
    # اختبار النماذج
    if not test_models():
        print("\n❌ فشل في اختبار النماذج")
        return 1
        
    # اختبار واجهة المستخدم
    if not test_ui():
        print("\n❌ فشل في اختبار واجهة المستخدم")
        return 1
        
    print("\n" + "=" * 60)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ البرنامج جاهز للتشغيل")
    print("\nلتشغيل البرنامج:")
    print("python start_app.py")
    print("\nبيانات تسجيل الدخول:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n❌ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ خطأ غير متوقع: {str(e)}")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
