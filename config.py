# -*- coding: utf-8 -*-
"""
إعدادات برنامج الحسن ستون
Al-Hassan Stone ERP Configuration
"""

import os
from configparser import ConfigParser

# معلومات البرنامج الأساسية
APP_NAME = "الحسن ستون"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "نظام إدارة مصنع الجرانيت"
COMPANY_NAME = "شركة الحسن ستون"

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'server': 'SQLEXPRESS04',
    'database': 'GraniteERP',
    'driver': '{ODBC Driver 17 for SQL Server}',
    'trusted_connection': 'yes'
}

# مسارات الملفات
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
ASSETS_DIR = os.path.join(BASE_DIR, 'assets')
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
BACKUP_DIR = os.path.join(BASE_DIR, 'backup')

# إعدادات الواجهة (سيتم تحميلها من ملف الإعدادات)
def get_ui_config():
    """الحصول على إعدادات واجهة المستخدم"""
    try:
        from src.models.settings import Settings
        settings = Settings()
        return {
            'theme': settings.get_setting('theme', 'modern'),
            'language': settings.get_setting('language', 'ar'),
            'font_family': settings.get_setting('font_family', 'Arial'),
            'font_size': settings.get_setting('font_size', 12),
            'window_width': settings.get_setting('window_width', 1200),
            'window_height': settings.get_setting('window_height', 800)
        }
    except:
        # إعدادات افتراضية في حالة عدم توفر نظام الإعدادات
        return {
            'theme': 'modern',
            'language': 'ar',
            'font_family': 'Arial',
            'font_size': 12,
            'window_width': 1200,
            'window_height': 800
        }

# تحميل الإعدادات
UI_CONFIG = get_ui_config()

# ألوان التطبيق
COLORS = {
    'primary': '#2C3E50',
    'secondary': '#3498DB',
    'success': '#27AE60',
    'warning': '#F39C12',
    'danger': '#E74C3C',
    'light': '#ECF0F1',
    'dark': '#34495E',
    'white': '#FFFFFF'
}

# أنواع الجرانيت المتاحة
GRANITE_TYPES = [
    'جرانيت أحمر أسوان',
    'جرانيت رمادي',
    'جرانيت أسود',
    'جرانيت وردي',
    'جرانيت أبيض',
    'جرانيت بني',
    'جرانيت أخضر'
]

# أسماك الشرائح المتاحة
SLICE_THICKNESS = [
    '2 سم',
    '3 سم',
    '4 سم',
    '5 سم',
    '6 سم'
]

# أنواع المصاريف
EXPENSE_TYPES = [
    'سولار',
    'صيانة الماكينات',
    'رواتب',
    'نقل',
    'كهرباء',
    'مياه',
    'أدوات',
    'أخرى'
]

# صلاحيات المستخدمين
USER_ROLES = {
    'admin': 'مدير النظام',
    'sales': 'موظف مبيعات',
    'production': 'مشرف إنتاج',
    'accountant': 'محاسب',
    'warehouse': 'أمين مخزن'
}

# إعدادات التقارير
REPORT_CONFIG = {
    'pdf_font': 'assets/fonts/NotoSansArabic-Regular.ttf',
    'excel_font': 'Arial',
    'page_size': 'A4',
    'margin': 20
}

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [ASSETS_DIR, REPORTS_DIR, LOGS_DIR, BACKUP_DIR]
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

def get_connection_string():
    """الحصول على نص الاتصال بقاعدة البيانات"""
    return (
        f"DRIVER={DATABASE_CONFIG['driver']};"
        f"SERVER={DATABASE_CONFIG['server']};"
        f"DATABASE={DATABASE_CONFIG['database']};"
        f"Trusted_Connection={DATABASE_CONFIG['trusted_connection']};"
    )

# إنشاء المجلدات عند استيراد الملف
create_directories()
