# -*- coding: utf-8 -*-
"""
مولد التقارير
Report Generator
"""

import os
from datetime import datetime, timedelta
from src.utils.logger import log_user_action, log_error

class ReportGenerator:
    """مولد التقارير"""
    
    def __init__(self, database_manager):
        self.database_manager = database_manager
        self.reports_dir = "reports"
        self.ensure_reports_directory()
        
    def ensure_reports_directory(self):
        """التأكد من وجود مجلد التقارير"""
        if not os.path.exists(self.reports_dir):
            os.makedirs(self.reports_dir)
            
    def generate_sales_report(self, start_date, end_date, user_id):
        """إنشاء تقرير المبيعات"""
        try:
            # جلب بيانات المبيعات
            query = """
            SELECT si.invoice_number, si.invoice_date, c.name as customer_name,
                   si.subtotal, si.tax_amount, si.discount_amount, si.total_amount,
                   si.paid_amount, si.status
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE DATE(si.invoice_date) BETWEEN ? AND ?
            ORDER BY si.invoice_date DESC
            """
            
            invoices = self.database_manager.execute_query(query, (start_date, end_date), fetch='all')
            
            # إنشاء التقرير
            report_content = self.create_sales_report_content(invoices, start_date, end_date)
            
            # حفظ التقرير
            filename = f"sales_report_{start_date}_to_{end_date}.txt"
            filepath = os.path.join(self.reports_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)
                
            log_user_action(user_id, "GENERATE_SALES_REPORT", f"إنشاء تقرير مبيعات من {start_date} إلى {end_date}")
            
            return True, filepath, "تم إنشاء تقرير المبيعات بنجاح"
            
        except Exception as e:
            log_error(f"خطأ في إنشاء تقرير المبيعات: {str(e)}")
            return False, None, str(e)
            
    def create_sales_report_content(self, invoices, start_date, end_date):
        """إنشاء محتوى تقرير المبيعات"""
        content = []
        content.append("=" * 80)
        content.append("تقرير المبيعات")
        content.append("=" * 80)
        content.append(f"الفترة: من {start_date} إلى {end_date}")
        content.append(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("=" * 80)
        content.append("")
        
        if not invoices:
            content.append("لا توجد فواتير في هذه الفترة")
            return "\n".join(content)
            
        # إحصائيات عامة
        total_invoices = len(invoices)
        total_amount = sum(invoice[6] for invoice in invoices if invoice[6])
        total_paid = sum(invoice[7] for invoice in invoices if invoice[7])
        total_remaining = total_amount - total_paid
        
        content.append("الإحصائيات العامة:")
        content.append("-" * 40)
        content.append(f"عدد الفواتير: {total_invoices}")
        content.append(f"إجمالي المبيعات: {total_amount:,.2f} جنيه")
        content.append(f"إجمالي المدفوع: {total_paid:,.2f} جنيه")
        content.append(f"إجمالي المتبقي: {total_remaining:,.2f} جنيه")
        content.append("")
        
        # تفاصيل الفواتير
        content.append("تفاصيل الفواتير:")
        content.append("-" * 40)
        content.append(f"{'رقم الفاتورة':<15} {'التاريخ':<12} {'العميل':<20} {'الإجمالي':<12} {'الحالة':<10}")
        content.append("-" * 80)
        
        for invoice in invoices:
            invoice_number = str(invoice[0])[:14]
            invoice_date = str(invoice[1])[:10]
            customer_name = str(invoice[2])[:19] if invoice[2] else "غير محدد"
            total_amount = f"{invoice[6]:,.0f}" if invoice[6] else "0"
            status = self.get_status_text(invoice[8])
            
            content.append(f"{invoice_number:<15} {invoice_date:<12} {customer_name:<20} {total_amount:<12} {status:<10}")
            
        content.append("")
        content.append("=" * 80)
        content.append("انتهى التقرير")
        
        return "\n".join(content)
        
    def generate_expenses_report(self, start_date, end_date, user_id):
        """إنشاء تقرير المصاريف"""
        try:
            # جلب بيانات المصاريف
            query = """
            SELECT expense_date, expense_type, description, amount, supplier
            FROM expenses
            WHERE DATE(expense_date) BETWEEN ? AND ?
            ORDER BY expense_date DESC
            """
            
            expenses = self.database_manager.execute_query(query, (start_date, end_date), fetch='all')
            
            # إنشاء التقرير
            report_content = self.create_expenses_report_content(expenses, start_date, end_date)
            
            # حفظ التقرير
            filename = f"expenses_report_{start_date}_to_{end_date}.txt"
            filepath = os.path.join(self.reports_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)
                
            log_user_action(user_id, "GENERATE_EXPENSES_REPORT", f"إنشاء تقرير مصاريف من {start_date} إلى {end_date}")
            
            return True, filepath, "تم إنشاء تقرير المصاريف بنجاح"
            
        except Exception as e:
            log_error(f"خطأ في إنشاء تقرير المصاريف: {str(e)}")
            return False, None, str(e)
            
    def create_expenses_report_content(self, expenses, start_date, end_date):
        """إنشاء محتوى تقرير المصاريف"""
        content = []
        content.append("=" * 80)
        content.append("تقرير المصاريف")
        content.append("=" * 80)
        content.append(f"الفترة: من {start_date} إلى {end_date}")
        content.append(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("=" * 80)
        content.append("")
        
        if not expenses:
            content.append("لا توجد مصاريف في هذه الفترة")
            return "\n".join(content)
            
        # إحصائيات عامة
        total_expenses = len(expenses)
        total_amount = sum(expense[3] for expense in expenses if expense[3])
        
        # تجميع حسب النوع
        by_type = {}
        for expense in expenses:
            expense_type = expense[1]
            amount = expense[3] if expense[3] else 0
            if expense_type in by_type:
                by_type[expense_type] += amount
            else:
                by_type[expense_type] = amount
                
        content.append("الإحصائيات العامة:")
        content.append("-" * 40)
        content.append(f"عدد المصاريف: {total_expenses}")
        content.append(f"إجمالي المصاريف: {total_amount:,.2f} جنيه")
        content.append("")
        
        # المصاريف حسب النوع
        content.append("المصاريف حسب النوع:")
        content.append("-" * 40)
        for expense_type, amount in sorted(by_type.items(), key=lambda x: x[1], reverse=True):
            percentage = (amount / total_amount * 100) if total_amount > 0 else 0
            content.append(f"{expense_type}: {amount:,.2f} جنيه ({percentage:.1f}%)")
        content.append("")
        
        # تفاصيل المصاريف
        content.append("تفاصيل المصاريف:")
        content.append("-" * 40)
        content.append(f"{'التاريخ':<12} {'النوع':<15} {'الوصف':<25} {'المبلغ':<12} {'المورد':<15}")
        content.append("-" * 80)
        
        for expense in expenses:
            expense_date = str(expense[0])[:10]
            expense_type = str(expense[1])[:14]
            description = str(expense[2])[:24] if expense[2] else ""
            amount = f"{expense[3]:,.0f}" if expense[3] else "0"
            supplier = str(expense[4])[:14] if expense[4] else ""
            
            content.append(f"{expense_date:<12} {expense_type:<15} {description:<25} {amount:<12} {supplier:<15}")
            
        content.append("")
        content.append("=" * 80)
        content.append("انتهى التقرير")
        
        return "\n".join(content)
        
    def generate_inventory_report(self, user_id):
        """إنشاء تقرير المخزون"""
        try:
            # جلب بيانات المخزون
            query = """
            SELECT gt.name as granite_type, s.thickness_cm,
                   COUNT(s.id) as slice_count,
                   SUM(s.length_cm * s.width_cm / 10000) as total_area,
                   AVG(s.price_per_sqm) as avg_price
            FROM slices s
            LEFT JOIN granite_types gt ON s.granite_type_id = gt.id
            WHERE s.status = 'available'
            GROUP BY gt.name, s.thickness_cm
            ORDER BY gt.name, s.thickness_cm
            """
            
            inventory = self.database_manager.execute_query(query, fetch='all')
            
            # إنشاء التقرير
            report_content = self.create_inventory_report_content(inventory)
            
            # حفظ التقرير
            filename = f"inventory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            filepath = os.path.join(self.reports_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)
                
            log_user_action(user_id, "GENERATE_INVENTORY_REPORT", "إنشاء تقرير المخزون")
            
            return True, filepath, "تم إنشاء تقرير المخزون بنجاح"
            
        except Exception as e:
            log_error(f"خطأ في إنشاء تقرير المخزون: {str(e)}")
            return False, None, str(e)
            
    def create_inventory_report_content(self, inventory):
        """إنشاء محتوى تقرير المخزون"""
        content = []
        content.append("=" * 80)
        content.append("تقرير المخزون")
        content.append("=" * 80)
        content.append(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("=" * 80)
        content.append("")
        
        if not inventory:
            content.append("لا توجد شرائح متاحة في المخزون")
            return "\n".join(content)
            
        # إحصائيات عامة
        total_slices = sum(item[2] for item in inventory if item[2])
        total_area = sum(item[3] for item in inventory if item[3])
        
        content.append("الإحصائيات العامة:")
        content.append("-" * 40)
        content.append(f"إجمالي الشرائح المتاحة: {total_slices}")
        content.append(f"إجمالي المساحة المتاحة: {total_area:,.2f} متر مربع")
        content.append("")
        
        # تفاصيل المخزون
        content.append("تفاصيل المخزون:")
        content.append("-" * 40)
        content.append(f"{'نوع الجرانيت':<20} {'السمك (سم)':<12} {'عدد الشرائح':<15} {'المساحة (م²)':<15} {'متوسط السعر':<15}")
        content.append("-" * 80)
        
        for item in inventory:
            granite_type = str(item[0])[:19] if item[0] else "غير محدد"
            thickness = f"{item[1]:.1f}" if item[1] else "0.0"
            slice_count = str(item[2]) if item[2] else "0"
            area = f"{item[3]:,.2f}" if item[3] else "0.00"
            avg_price = f"{item[4]:,.0f}" if item[4] else "0"
            
            content.append(f"{granite_type:<20} {thickness:<12} {slice_count:<15} {area:<15} {avg_price:<15}")
            
        content.append("")
        content.append("=" * 80)
        content.append("انتهى التقرير")
        
        return "\n".join(content)
        
    def get_status_text(self, status):
        """تحويل حالة الفاتورة إلى نص عربي"""
        status_map = {
            'pending': 'معلقة',
            'partial': 'جزئية',
            'paid': 'مدفوعة'
        }
        return status_map.get(status, status)
        
    def get_available_reports(self):
        """الحصول على قائمة التقارير المتاحة"""
        try:
            reports = []
            if os.path.exists(self.reports_dir):
                for filename in os.listdir(self.reports_dir):
                    if filename.endswith('.txt'):
                        filepath = os.path.join(self.reports_dir, filename)
                        stat = os.stat(filepath)
                        reports.append({
                            'filename': filename,
                            'filepath': filepath,
                            'size': stat.st_size,
                            'created': datetime.fromtimestamp(stat.st_ctime)
                        })
                        
            return sorted(reports, key=lambda x: x['created'], reverse=True)
            
        except Exception as e:
            log_error(f"خطأ في جلب التقارير المتاحة: {str(e)}")
            return []
