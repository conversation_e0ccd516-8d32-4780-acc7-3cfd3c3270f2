# -*- coding: utf-8 -*-
"""
نموذج الإعدادات
Settings Model
"""

import json
import os
from src.utils.logger import log_user_action, log_error

class Settings:
    """نموذج إدارة إعدادات النظام"""
    
    def __init__(self):
        self.settings_file = "settings.json"
        self.default_settings = {
            # إعدادات الخط
            "font_family": "Segoe UI",
            "font_size": 10,
            "font_bold": False,
            
            # إعدادات الألوان
            "theme": "default",
            "primary_color": "#2C3E50",
            "secondary_color": "#3498DB",
            "success_color": "#27AE60",
            "warning_color": "#F39C12",
            "danger_color": "#E74C3C",
            "dark_color": "#34495E",
            "light_color": "#ECF0F1",
            
            # إعدادات الواجهة
            "window_width": 1200,
            "window_height": 800,
            "window_maximized": False,
            "show_splash": True,
            "auto_save": True,
            "auto_backup": True,
            
            # إعدادات التقارير
            "default_report_format": "txt",
            "reports_per_page": 50,
            "auto_open_reports": True,
            
            # إعدادات قاعدة البيانات
            "backup_interval": 24,  # ساعات
            "max_backup_files": 10,
            "compress_backups": True,
            
            # إعدادات اللغة والمنطقة
            "language": "ar",
            "date_format": "yyyy-MM-dd",
            "currency_symbol": "جنيه",
            "decimal_places": 2,
            
            # إعدادات الأمان
            "session_timeout": 60,  # دقائق
            "password_expiry": 90,  # أيام
            "max_login_attempts": 3,
            
            # إعدادات الإشعارات
            "show_notifications": True,
            "notification_sound": True,
            "notification_duration": 5,  # ثواني
            
            # إعدادات الطباعة
            "default_printer": "",
            "print_margins": 20,  # مم
            "print_orientation": "portrait",
            
            # إعدادات متقدمة
            "debug_mode": False,
            "log_level": "INFO",
            "cache_size": 100,  # MB
            "max_recent_files": 10
        }
        
        self.current_settings = self.load_settings()
        
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    
                # دمج الإعدادات المحفوظة مع الافتراضية
                settings = self.default_settings.copy()
                settings.update(saved_settings)
                return settings
            else:
                return self.default_settings.copy()
                
        except Exception as e:
            log_error(f"خطأ في تحميل الإعدادات: {str(e)}")
            return self.default_settings.copy()
            
    def save_settings(self, user_id=None):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, ensure_ascii=False, indent=4)
                
            if user_id:
                log_user_action(user_id, "UPDATE_SETTINGS", "تحديث إعدادات النظام")
                
            return True, "تم حفظ الإعدادات بنجاح"
            
        except Exception as e:
            log_error(f"خطأ في حفظ الإعدادات: {str(e)}")
            return False, str(e)
            
    def get_setting(self, key, default=None):
        """الحصول على قيمة إعداد معين"""
        return self.current_settings.get(key, default)
        
    def set_setting(self, key, value):
        """تعيين قيمة إعداد معين"""
        self.current_settings[key] = value
        
    def update_settings(self, new_settings):
        """تحديث عدة إعدادات مرة واحدة"""
        self.current_settings.update(new_settings)
        
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        self.current_settings = self.default_settings.copy()
        
    def get_font_settings(self):
        """الحصول على إعدادات الخط"""
        return {
            'family': self.get_setting('font_family'),
            'size': self.get_setting('font_size'),
            'bold': self.get_setting('font_bold')
        }
        
    def get_color_settings(self):
        """الحصول على إعدادات الألوان"""
        return {
            'primary': self.get_setting('primary_color'),
            'secondary': self.get_setting('secondary_color'),
            'success': self.get_setting('success_color'),
            'warning': self.get_setting('warning_color'),
            'danger': self.get_setting('danger_color'),
            'dark': self.get_setting('dark_color'),
            'light': self.get_setting('light_color')
        }
        
    def get_window_settings(self):
        """الحصول على إعدادات النافذة"""
        return {
            'width': self.get_setting('window_width'),
            'height': self.get_setting('window_height'),
            'maximized': self.get_setting('window_maximized')
        }
        
    def export_settings(self, filepath):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, ensure_ascii=False, indent=4)
            return True, "تم تصدير الإعدادات بنجاح"
        except Exception as e:
            return False, str(e)
            
    def import_settings(self, filepath):
        """استيراد الإعدادات من ملف"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
                
            # التحقق من صحة الإعدادات المستوردة
            valid_settings = {}
            for key, value in imported_settings.items():
                if key in self.default_settings:
                    valid_settings[key] = value
                    
            self.current_settings.update(valid_settings)
            return True, "تم استيراد الإعدادات بنجاح"
            
        except Exception as e:
            return False, str(e)
            
    def validate_settings(self):
        """التحقق من صحة الإعدادات"""
        errors = []
        
        # التحقق من حجم الخط
        font_size = self.get_setting('font_size')
        if not isinstance(font_size, int) or font_size < 8 or font_size > 72:
            errors.append("حجم الخط يجب أن يكون بين 8 و 72")
            
        # التحقق من أبعاد النافذة
        width = self.get_setting('window_width')
        height = self.get_setting('window_height')
        if not isinstance(width, int) or width < 800:
            errors.append("عرض النافذة يجب أن يكون 800 بكسل على الأقل")
        if not isinstance(height, int) or height < 600:
            errors.append("ارتفاع النافذة يجب أن يكون 600 بكسل على الأقل")
            
        # التحقق من فترة انتهاء الجلسة
        timeout = self.get_setting('session_timeout')
        if not isinstance(timeout, int) or timeout < 5 or timeout > 480:
            errors.append("مهلة الجلسة يجب أن تكون بين 5 و 480 دقيقة")
            
        return errors
        
    def get_themes(self):
        """الحصول على قائمة الثيمات المتاحة"""
        return {
            'default': 'الافتراضي',
            'dark': 'داكن',
            'light': 'فاتح',
            'blue': 'أزرق',
            'green': 'أخضر'
        }
        
    def apply_theme(self, theme_name):
        """تطبيق ثيم معين"""
        themes = {
            'default': {
                'primary_color': '#2C3E50',
                'secondary_color': '#3498DB',
                'success_color': '#27AE60',
                'warning_color': '#F39C12',
                'danger_color': '#E74C3C'
            },
            'dark': {
                'primary_color': '#1A1A1A',
                'secondary_color': '#4A90E2',
                'success_color': '#5CB85C',
                'warning_color': '#F0AD4E',
                'danger_color': '#D9534F'
            },
            'light': {
                'primary_color': '#F8F9FA',
                'secondary_color': '#007BFF',
                'success_color': '#28A745',
                'warning_color': '#FFC107',
                'danger_color': '#DC3545'
            },
            'blue': {
                'primary_color': '#1E3A8A',
                'secondary_color': '#3B82F6',
                'success_color': '#10B981',
                'warning_color': '#F59E0B',
                'danger_color': '#EF4444'
            },
            'green': {
                'primary_color': '#064E3B',
                'secondary_color': '#059669',
                'success_color': '#10B981',
                'warning_color': '#D97706',
                'danger_color': '#DC2626'
            }
        }
        
        if theme_name in themes:
            self.current_settings.update(themes[theme_name])
            self.set_setting('theme', theme_name)
            return True
        return False
