# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QCheckBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

import config
from src.models.user import User
from src.utils.logger import log_error

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal(dict)
    
    def __init__(self, database_manager):
        super().__init__()
        self.database_manager = database_manager
        self.user_model = User(database_manager)
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"تسجيل الدخول - {config.APP_NAME}")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # شعار الشركة
        self.create_header(main_layout)
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_layout)
        
        # أزرار التحكم
        self.create_buttons(main_layout)
        
        # معلومات إضافية
        self.create_footer(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس النافذة"""
        header_frame = QFrame()
        header_layout = QVBoxLayout()
        
        # عنوان التطبيق
        title_label = QLabel(config.APP_NAME)
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont(config.UI_CONFIG['font_family'], 18, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2C3E50; margin-bottom: 10px;")
        
        # وصف التطبيق
        desc_label = QLabel(config.APP_DESCRIPTION)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #7F8C8D; font-size: 12px;")
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(desc_label)
        header_frame.setLayout(header_layout)
        
        layout.addWidget(header_frame)
        
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النموذج
        form_title = QLabel("تسجيل الدخول")
        form_title.setAlignment(Qt.AlignCenter)
        form_title.setFont(QFont(config.UI_CONFIG['font_family'], 14, QFont.Bold))
        form_title.setStyleSheet("color: #34495E; margin-bottom: 10px;")
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")  # للاختبار
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setText("admin123")  # للاختبار
        
        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكرني")
        
        form_layout.addWidget(form_title)
        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_input)
        form_layout.addWidget(self.remember_checkbox)
        
        form_frame.setLayout(form_layout)
        layout.addWidget(form_frame)
        
    def create_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        buttons_layout = QHBoxLayout()
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setDefault(True)
        self.login_button.setMinimumHeight(40)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setMinimumHeight(40)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        
        layout.addLayout(buttons_layout)
        
    def create_footer(self, layout):
        """إنشاء تذييل النافذة"""
        # مساحة فارغة
        spacer = QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)
        layout.addItem(spacer)
        
        # معلومات الشركة
        footer_label = QLabel(f"© 2025 {config.COMPANY_NAME}")
        footer_label.setAlignment(Qt.AlignCenter)
        footer_label.setStyleSheet("color: #BDC3C7; font-size: 10px;")
        
        layout.addWidget(footer_label)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {config.COLORS['light']};
            }}
            
            QFrame {{
                background-color: {config.COLORS['white']};
                border: 1px solid #BDC3C7;
                border-radius: 8px;
            }}
            
            QLineEdit {{
                padding: 10px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 12px;
                background-color: {config.COLORS['white']};
            }}
            
            QLineEdit:focus {{
                border-color: {config.COLORS['secondary']};
            }}
            
            QPushButton {{
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }}
            
            QPushButton#login_button {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
            
            QPushButton#login_button:hover {{
                background-color: #2980B9;
            }}
            
            QPushButton#cancel_button {{
                background-color: {config.COLORS['danger']};
                color: white;
            }}
            
            QPushButton#cancel_button:hover {{
                background-color: #C0392B;
            }}
            
            QCheckBox {{
                font-size: 11px;
                color: #34495E;
            }}
            
            QLabel {{
                color: #2C3E50;
                font-size: 11px;
            }}
        """)
        
        # تعيين أسماء الكائنات للأنماط
        self.login_button.setObjectName("login_button")
        self.cancel_button.setObjectName("cancel_button")
        
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من صحة البيانات
        if not username:
            self.show_error("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
            
        if not password:
            self.show_error("خطأ", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
            
        # تعطيل الأزرار أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري التحقق...")
        
        try:
            # التحقق من صحة البيانات
            user_data = self.user_model.authenticate(username, password)
            
            if user_data:
                self.login_successful.emit(user_data)
                self.accept()
            else:
                self.show_error("خطأ في تسجيل الدخول", 
                              "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_input.clear()
                self.password_input.setFocus()
                
        except Exception as e:
            log_error(f"خطأ في تسجيل الدخول: {str(e)}")
            self.show_error("خطأ في النظام", "حدث خطأ أثناء تسجيل الدخول")
            
        finally:
            # إعادة تفعيل الأزرار
            self.login_button.setEnabled(True)
            self.login_button.setText("تسجيل الدخول")
            
    def show_error(self, title, message):
        """عرض رسالة خطأ"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()
        
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
