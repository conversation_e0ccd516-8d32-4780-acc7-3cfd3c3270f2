#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة مصنع الجرانيت
Launch Granite Factory Management System
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    init_files = [
        "src/__init__.py",
        "src/models/__init__.py", 
        "src/ui/__init__.py",
        "src/database/__init__.py",
        "src/utils/__init__.py"
    ]
    
    for init_file in init_files:
        if not os.path.exists(init_file):
            os.makedirs(os.path.dirname(init_file), exist_ok=True)
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('# -*- coding: utf-8 -*-\n')

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    print("🚀 تشغيل نظام إدارة مصنع الجرانيت")
    print("=" * 50)
    
    # إنشاء الملفات المفقودة
    create_missing_files()
    
    try:
        # استيراد المكونات الأساسية
        from PyQt5.QtWidgets import QApplication, QMessageBox
        import config
        
        print("✅ تم تحميل PyQt5")
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setApplicationName("نظام إدارة مصنع الجرانيت")
        
        print("✅ تم إنشاء التطبيق")
        
        # محاولة تحميل النافذة الرئيسية مباشرة
        try:
            from src.ui.main_window import MainWindow
            
            # بيانات مستخدم افتراضية
            user_data = {
                'id': 1,
                'username': 'admin',
                'full_name': 'مدير النظام',
                'role': 'admin'
            }
            
            # إنشاء وعرض النافذة الرئيسية
            main_window = MainWindow(user_data)
            main_window.show()
            
            print("✅ تم عرض النافذة الرئيسية")
            print("🎉 النظام يعمل بنجاح!")
            
            return app.exec_()
            
        except Exception as main_error:
            print(f"❌ خطأ في النافذة الرئيسية: {str(main_error)}")
            
            # محاولة عرض نافذة بسيطة
            try:
                from PyQt5.QtWidgets import QMainWindow, QLabel, QVBoxLayout, QWidget
                
                class SimpleWindow(QMainWindow):
                    def __init__(self):
                        super().__init__()
                        self.setWindowTitle("نظام إدارة مصنع الجرانيت")
                        self.setGeometry(100, 100, 800, 600)
                        
                        central_widget = QWidget()
                        self.setCentralWidget(central_widget)
                        
                        layout = QVBoxLayout()
                        central_widget.setLayout(layout)
                        
                        label = QLabel("🎉 مرحباً بك في نظام إدارة مصنع الجرانيت")
                        label.setStyleSheet("font-size: 24px; padding: 50px; text-align: center;")
                        layout.addWidget(label)
                        
                        status_label = QLabel("النظام يعمل بنجاح!")
                        status_label.setStyleSheet("font-size: 16px; color: green; text-align: center;")
                        layout.addWidget(status_label)
                
                simple_window = SimpleWindow()
                simple_window.show()
                
                print("✅ تم عرض نافذة بسيطة")
                return app.exec_()
                
            except Exception as simple_error:
                print(f"❌ خطأ في النافذة البسيطة: {str(simple_error)}")
                
                # عرض رسالة خطأ
                QMessageBox.critical(None, "خطأ", 
                                   f"فشل في تشغيل النظام:\n\n"
                                   f"خطأ النافذة الرئيسية: {str(main_error)}\n"
                                   f"خطأ النافذة البسيطة: {str(simple_error)}\n\n"
                                   f"يرجى التحقق من تثبيت PyQt5")
                return 1
                
    except ImportError as import_error:
        print(f"❌ خطأ في الاستيراد: {str(import_error)}")
        print("\n💡 حلول:")
        print("1. pip install PyQt5")
        print("2. pip install -r requirements.txt")
        return 1
        
    except Exception as general_error:
        print(f"❌ خطأ عام: {str(general_error)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
