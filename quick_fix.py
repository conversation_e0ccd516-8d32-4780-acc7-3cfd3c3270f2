#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع للأخطاء الأساسية
Quick Fix for Basic Errors
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_missing_init_files():
    """إنشاء ملفات __init__.py المفقودة"""
    print("📁 إنشاء ملفات __init__.py المفقودة...")
    
    init_files = [
        "src/__init__.py",
        "src/models/__init__.py",
        "src/ui/__init__.py",
        "src/database/__init__.py",
        "src/utils/__init__.py"
    ]
    
    for init_file in init_files:
        try:
            if not os.path.exists(init_file):
                os.makedirs(os.path.dirname(init_file), exist_ok=True)
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write('# -*- coding: utf-8 -*-\n')
                print(f"✅ تم إنشاء {init_file}")
            else:
                print(f"✅ {init_file} موجود")
        except Exception as e:
            print(f"❌ خطأ في إنشاء {init_file}: {str(e)}")

def fix_import_paths():
    """إصلاح مسارات الاستيراد"""
    print("\n🔧 إصلاح مسارات الاستيراد...")
    
    try:
        # اختبار استيراد config
        import config
        print("✅ config تم استيراده بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد config: {str(e)}")
        
    try:
        # اختبار استيراد قاعدة البيانات
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager تم استيراده بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد DatabaseManager: {str(e)}")
        
    try:
        # اختبار استيراد SQLite
        from src.database.sqlite_manager import SQLiteManager
        print("✅ SQLiteManager تم استيراده بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد SQLiteManager: {str(e)}")

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n💾 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from src.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.connect():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            db_manager.disconnect()
            return True
        else:
            print("⚠️ فشل الاتصال بقاعدة البيانات - سيتم استخدام SQLite")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def test_ui_imports():
    """اختبار استيراد واجهات المستخدم"""
    print("\n🖥️ اختبار استيراد واجهات المستخدم...")
    
    ui_modules = [
        ("نافذة العملاء", "src.ui.customer_dialog"),
        ("نافذة الطلبات", "src.ui.order_dialog"),
        ("نافذة الدفعات", "src.ui.payment_dialog"),
        ("أدوات الخط", "src.utils.font_utils"),
    ]
    
    success_count = 0
    
    for ui_name, module_path in ui_modules:
        try:
            __import__(module_path)
            print(f"✅ {ui_name}: تم الاستيراد بنجاح")
            success_count += 1
        except Exception as e:
            print(f"❌ {ui_name}: خطأ - {str(e)}")
            
    return success_count

def create_simple_test():
    """إنشاء اختبار بسيط"""
    print("\n🧪 إنشاء اختبار بسيط...")
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
Simple System Test
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    try:
        import config
        print("✅ config")
        
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager")
        
        from src.utils.font_utils import FontManager
        print("✅ FontManager")
        
        return True
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        from src.database.database_manager import DatabaseManager
        db = DatabaseManager()
        if db.connect():
            print("✅ قاعدة البيانات")
            db.disconnect()
            return True
        else:
            print("⚠️ قاعدة البيانات (SQLite)")
            return True
    except Exception as e:
        print(f"❌ قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 اختبار بسيط للنظام")
    print("=" * 30)
    
    imports_ok = test_basic_imports()
    db_ok = test_database()
    
    if imports_ok and db_ok:
        print("\\n🎉 النظام جاهز للتشغيل!")
    else:
        print("\\n⚠️ يوجد مشاكل تحتاج إصلاح")
'''
    
    try:
        with open("simple_test.py", "w", encoding="utf-8") as f:
            f.write(test_content)
        print("✅ تم إنشاء simple_test.py")
    except Exception as e:
        print(f"❌ فشل في إنشاء الاختبار: {str(e)}")

def main():
    """الدالة الرئيسية للإصلاح السريع"""
    print("🚀 بدء الإصلاح السريع للأخطاء")
    print("=" * 50)
    
    # إنشاء ملفات __init__.py
    create_missing_init_files()
    
    # إصلاح مسارات الاستيراد
    fix_import_paths()
    
    # اختبار قاعدة البيانات
    db_ok = test_database_connection()
    
    # اختبار واجهات المستخدم
    ui_count = test_ui_imports()
    
    # إنشاء اختبار بسيط
    create_simple_test()
    
    # تقرير النتائج
    print("\n📊 تقرير الإصلاح السريع:")
    print("=" * 30)
    print(f"قاعدة البيانات: {'✅ تعمل' if db_ok else '⚠️ SQLite'}")
    print(f"واجهات المستخدم: {ui_count}/4 تعمل")
    
    if ui_count >= 3:
        print("\n🎉 معظم المكونات تعمل بشكل صحيح!")
        print("💡 جرب تشغيل: python simple_test.py")
        return 0
    else:
        print("\n⚠️ يحتاج المزيد من الإصلاحات")
        return 1

if __name__ == "__main__":
    sys.exit(main())
