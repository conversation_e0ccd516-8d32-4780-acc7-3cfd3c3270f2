#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع واجهات النظام
Complete UI Testing
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_dialogs():
    """اختبار جميع النوافذ والحوارات"""
    print("🧪 اختبار جميع واجهات النظام")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # قائمة النوافذ للاختبار
        dialogs_to_test = [
            ("نافذة إضافة العملاء", "src.ui.customer_dialog", "CustomerDialog"),
            ("نافذة إنشاء الفاتورة", "src.ui.invoice_dialog", "InvoiceDialog"),
            ("نافذة عرض الفاتورة", "src.ui.invoice_view_dialog", "InvoiceViewDialog"),
            ("نافذة إضافة الدفعات", "src.ui.payment_dialog", "PaymentDialog"),
            ("نافذة إضافة الطلبات", "src.ui.order_dialog", "OrderDialog"),
            ("نافذة إدارة أنواع الجرانيت", "src.ui.granite_types_dialog", "GraniteTypesDialog"),
            ("نافذة إدارة المستخدمين", "src.ui.users_management_dialog", "UsersManagementDialog"),
            ("نافذة الإعدادات", "src.ui.settings_dialog", "SettingsDialog"),
        ]
        
        results = {}
        
        for dialog_name, module_path, class_name in dialogs_to_test:
            try:
                # استيراد الوحدة
                module = __import__(module_path, fromlist=[class_name])
                dialog_class = getattr(module, class_name)
                
                # إنشاء النافذة (بدون عرضها)
                dialog = dialog_class()
                
                print(f"✅ {dialog_name}: نجح")
                results[dialog_name] = True
                
                # تنظيف
                dialog.close()
                
            except Exception as e:
                print(f"❌ {dialog_name}: فشل - {str(e)}")
                results[dialog_name] = False
                
        # تنظيف التطبيق
        app.quit()
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ عام في اختبار الواجهات: {str(e)}")
        return {}

def test_main_widgets():
    """اختبار الواجهات الرئيسية"""
    print("\n🖥️ اختبار الواجهات الرئيسية")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # قائمة الواجهات الرئيسية
        widgets_to_test = [
            ("لوحة التحكم", "src.ui.dashboard_widget", "DashboardWidget"),
            ("واجهة الشاحنات", "src.ui.trucks_widget", "TrucksWidget"),
            ("واجهة التقطيع", "src.ui.cutting_widget", "CuttingWidget"),
            ("واجهة المبيعات", "src.ui.sales_widget", "SalesWidget"),
            ("واجهة المصاريف", "src.ui.expenses_widget", "ExpensesWidget"),
            ("واجهة الطلبات", "src.ui.orders_widget", "OrdersWidget"),
            ("واجهة التقارير", "src.ui.reports_widget", "ReportsWidget"),
        ]
        
        results = {}
        
        for widget_name, module_path, class_name in widgets_to_test:
            try:
                # استيراد الوحدة
                module = __import__(module_path, fromlist=[class_name])
                widget_class = getattr(module, class_name)
                
                print(f"✅ {widget_name}: تم الاستيراد بنجاح")
                results[widget_name] = True
                
            except Exception as e:
                print(f"❌ {widget_name}: فشل - {str(e)}")
                results[widget_name] = False
                
        # تنظيف التطبيق
        app.quit()
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ عام في اختبار الواجهات الرئيسية: {str(e)}")
        return {}

def test_models():
    """اختبار النماذج"""
    print("\n📊 اختبار النماذج")
    print("=" * 60)
    
    models_to_test = [
        ("نموذج العملاء", "src.models.customer", "Customer"),
        ("نموذج فواتير المبيعات", "src.models.sales_invoice", "SalesInvoice"),
        ("نموذج المصاريف", "src.models.expense", "Expense"),
        ("نموذج الطلبات", "src.models.order", "Order"),
        ("نموذج الإعدادات", "src.models.settings", "Settings"),
        ("نموذج المستخدمين", "src.models.user", "User"),
        ("نموذج أنواع الجرانيت", "src.models.granite_type", "GraniteType"),
    ]
    
    results = {}
    
    for model_name, module_path, class_name in models_to_test:
        try:
            # استيراد الوحدة
            module = __import__(module_path, fromlist=[class_name])
            model_class = getattr(module, class_name)
            
            print(f"✅ {model_name}: تم الاستيراد بنجاح")
            results[model_name] = True
            
        except Exception as e:
            print(f"❌ {model_name}: فشل - {str(e)}")
            results[model_name] = False
            
    return results

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n💾 اختبار قاعدة البيانات")
    print("=" * 60)
    
    try:
        from src.database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # اختبار الاتصال
        if db_manager.connect():
            print("✅ الاتصال بقاعدة البيانات: نجح")
            
            # اختبار استعلام بسيط
            result = db_manager.execute_query("SELECT COUNT(*) FROM users", fetch='one')
            if result:
                print(f"✅ عدد المستخدمين في قاعدة البيانات: {result[0]}")
                
            db_manager.close()
            return True
        else:
            print("❌ الاتصال بقاعدة البيانات: فشل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def generate_test_report(dialog_results, widget_results, model_results, db_result):
    """إنشاء تقرير الاختبار"""
    print("\n📋 تقرير الاختبار الشامل")
    print("=" * 60)
    
    # إحصائيات النوافذ
    dialog_success = sum(1 for result in dialog_results.values() if result)
    dialog_total = len(dialog_results)
    dialog_percentage = (dialog_success / dialog_total * 100) if dialog_total > 0 else 0
    
    print(f"🔹 النوافذ والحوارات: {dialog_success}/{dialog_total} ({dialog_percentage:.1f}%)")
    
    # إحصائيات الواجهات
    widget_success = sum(1 for result in widget_results.values() if result)
    widget_total = len(widget_results)
    widget_percentage = (widget_success / widget_total * 100) if widget_total > 0 else 0
    
    print(f"🔹 الواجهات الرئيسية: {widget_success}/{widget_total} ({widget_percentage:.1f}%)")
    
    # إحصائيات النماذج
    model_success = sum(1 for result in model_results.values() if result)
    model_total = len(model_results)
    model_percentage = (model_success / model_total * 100) if model_total > 0 else 0
    
    print(f"🔹 النماذج: {model_success}/{model_total} ({model_percentage:.1f}%)")
    
    # قاعدة البيانات
    db_status = "✅ نجح" if db_result else "❌ فشل"
    print(f"🔹 قاعدة البيانات: {db_status}")
    
    # النتيجة الإجمالية
    total_success = dialog_success + widget_success + model_success + (1 if db_result else 0)
    total_tests = dialog_total + widget_total + model_total + 1
    overall_percentage = (total_success / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n🎯 النتيجة الإجمالية: {total_success}/{total_tests} ({overall_percentage:.1f}%)")
    
    if overall_percentage >= 90:
        print("🎉 ممتاز! النظام جاهز للاستخدام")
    elif overall_percentage >= 75:
        print("👍 جيد! معظم المكونات تعمل بشكل صحيح")
    elif overall_percentage >= 50:
        print("⚠️ متوسط! يحتاج بعض الإصلاحات")
    else:
        print("🚨 ضعيف! يحتاج مراجعة شاملة")
        
    return overall_percentage

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء الاختبار الشامل لنظام إدارة مصنع الجرانيت")
    print("=" * 70)
    
    # تشغيل جميع الاختبارات
    dialog_results = test_all_dialogs()
    widget_results = test_main_widgets()
    model_results = test_models()
    db_result = test_database_connection()
    
    # إنشاء التقرير
    overall_score = generate_test_report(dialog_results, widget_results, model_results, db_result)
    
    print("\n" + "=" * 70)
    print("🏁 انتهى الاختبار الشامل")
    
    # إرجاع كود الخروج
    if overall_score >= 75:
        return 0  # نجح
    else:
        return 1  # فشل

if __name__ == "__main__":
    sys.exit(main())
