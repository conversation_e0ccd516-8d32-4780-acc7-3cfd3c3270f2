# -*- coding: utf-8 -*-
"""
نافذة إنشاء/تعديل الفاتورة
Invoice Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QDoubleSpinBox, QPushButton,
                            QMessageBox, QLabel, QGroupBox, QComboBox, QDateEdit,
                            QTableWidget, QTableWidgetItem, QSpinBox, QHeaderView,
                            QAbstractItemView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

import config
from src.models.sales_invoice import SalesInvoice
from src.models.customer import Customer
from src.models.granite_type import GraniteType

class InvoiceDialog(QDialog):
    """نافذة إنشاء/تعديل الفاتورة"""
    
    def __init__(self, parent=None, database_manager=None, user_data=None, invoice_id=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.user_data = user_data
        self.invoice_id = invoice_id
        self.invoice_model = SalesInvoice(database_manager)
        self.customer_model = Customer(database_manager)
        self.granite_type_model = GraniteType(database_manager)
        
        self.setup_ui()
        self.load_data()
        if invoice_id:
            self.load_invoice_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إنشاء فاتورة جديدة" if not self.invoice_id else "تعديل الفاتورة")
        self.setFixedSize(900, 700)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # معلومات الفاتورة
        invoice_group = QGroupBox("معلومات الفاتورة")
        invoice_layout = QFormLayout()
        
        # العميل
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        invoice_layout.addRow("العميل:", self.customer_combo)
        
        # تاريخ الفاتورة
        self.invoice_date = QDateEdit()
        self.invoice_date.setDate(QDate.currentDate())
        self.invoice_date.setCalendarPopup(True)
        invoice_layout.addRow("تاريخ الفاتورة:", self.invoice_date)
        
        # تاريخ الاستحقاق
        self.due_date = QDateEdit()
        self.due_date.setDate(QDate.currentDate().addDays(30))
        self.due_date.setCalendarPopup(True)
        invoice_layout.addRow("تاريخ الاستحقاق:", self.due_date)
        
        invoice_group.setLayout(invoice_layout)
        layout.addWidget(invoice_group)
        
        # عناصر الفاتورة
        items_group = QGroupBox("عناصر الفاتورة")
        items_layout = QVBoxLayout()
        
        # أزرار إدارة العناصر
        items_buttons_layout = QHBoxLayout()
        
        self.add_item_btn = QPushButton("إضافة عنصر")
        self.add_item_btn.clicked.connect(self.add_item)
        
        self.remove_item_btn = QPushButton("حذف العنصر")
        self.remove_item_btn.clicked.connect(self.remove_item)
        
        items_buttons_layout.addWidget(self.add_item_btn)
        items_buttons_layout.addWidget(self.remove_item_btn)
        items_buttons_layout.addStretch()
        
        items_layout.addLayout(items_buttons_layout)
        
        # جدول العناصر
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        
        headers = ["نوع الجرانيت", "الطول (سم)", "العرض (سم)", "السمك (سم)", 
                  "الكمية", "السعر/م²", "المساحة (م²)", "الإجمالي"]
        self.items_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        
        items_layout.addWidget(self.items_table)
        items_group.setLayout(items_layout)
        layout.addWidget(items_group)
        
        # الإجماليات
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QFormLayout()
        
        self.subtotal_input = QDoubleSpinBox()
        self.subtotal_input.setRange(0, 1000000)
        self.subtotal_input.setDecimals(2)
        self.subtotal_input.setSuffix(" جنيه")
        self.subtotal_input.setReadOnly(True)
        totals_layout.addRow("المجموع الفرعي:", self.subtotal_input)
        
        self.tax_input = QDoubleSpinBox()
        self.tax_input.setRange(0, 100000)
        self.tax_input.setDecimals(2)
        self.tax_input.setSuffix(" جنيه")
        self.tax_input.valueChanged.connect(self.calculate_total)
        totals_layout.addRow("الضريبة:", self.tax_input)
        
        self.discount_input = QDoubleSpinBox()
        self.discount_input.setRange(0, 100000)
        self.discount_input.setDecimals(2)
        self.discount_input.setSuffix(" جنيه")
        self.discount_input.valueChanged.connect(self.calculate_total)
        totals_layout.addRow("الخصم:", self.discount_input)
        
        self.total_input = QDoubleSpinBox()
        self.total_input.setRange(0, 1000000)
        self.total_input.setDecimals(2)
        self.total_input.setSuffix(" جنيه")
        self.total_input.setReadOnly(True)
        totals_layout.addRow("الإجمالي النهائي:", self.total_input)
        
        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)
        
        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات الفاتورة...")
        notes_layout.addWidget(self.notes_input)
        
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الفاتورة")
        self.save_btn.clicked.connect(self.save_invoice)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        self.apply_styles()
        
    def load_data(self):
        """تحميل البيانات الأساسية"""
        # تحميل العملاء
        customers = self.customer_model.get_all_customers()
        self.customer_combo.clear()
        for customer in customers:
            self.customer_combo.addItem(customer[1], customer[0])  # name, id
            
        # تحميل أنواع الجرانيت
        self.granite_types = self.granite_type_model.get_all_granite_types()
        
    def add_item(self):
        """إضافة عنصر جديد للفاتورة"""
        dialog = InvoiceItemDialog(self, self.granite_types)
        if dialog.exec_() == QDialog.Accepted:
            item_data = dialog.get_item_data()
            self.add_item_to_table(item_data)
            self.calculate_subtotal()
            
    def add_item_to_table(self, item_data):
        """إضافة عنصر إلى الجدول"""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)
        
        # حساب المساحة والإجمالي
        area = (item_data['length'] * item_data['width'] * item_data['quantity']) / 10000
        total = area * item_data['unit_price']
        
        items = [
            item_data['granite_type_name'],
            str(item_data['length']),
            str(item_data['width']),
            str(item_data['thickness']),
            str(item_data['quantity']),
            f"{item_data['unit_price']:.2f}",
            f"{area:.2f}",
            f"{total:.2f}"
        ]
        
        for col, item_text in enumerate(items):
            item = QTableWidgetItem(item_text)
            if col > 0:  # الأعمدة الرقمية
                item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, col, item)
            
        # حفظ البيانات الإضافية
        self.items_table.setItem(row, 0, QTableWidgetItem(item_data['granite_type_name']))
        self.items_table.item(row, 0).setData(Qt.UserRole, item_data)
        
    def remove_item(self):
        """حذف العنصر المحدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            self.items_table.removeRow(current_row)
            self.calculate_subtotal()
            
    def calculate_subtotal(self):
        """حساب المجموع الفرعي"""
        subtotal = 0
        for row in range(self.items_table.rowCount()):
            total_item = self.items_table.item(row, 7)
            if total_item:
                subtotal += float(total_item.text())
                
        self.subtotal_input.setValue(subtotal)
        self.calculate_total()
        
    def calculate_total(self):
        """حساب الإجمالي النهائي"""
        subtotal = self.subtotal_input.value()
        tax = self.tax_input.value()
        discount = self.discount_input.value()
        
        total = subtotal + tax - discount
        self.total_input.setValue(max(0, total))
        
    def save_invoice(self):
        """حفظ الفاتورة"""
        # التحقق من وجود عناصر
        if self.items_table.rowCount() == 0:
            QMessageBox.warning(self, "خطأ", "يجب إضافة عنصر واحد على الأقل للفاتورة")
            return
            
        # جمع بيانات الفاتورة
        invoice_data = {
            'customer_id': self.customer_combo.currentData(),
            'invoice_date': self.invoice_date.date().toString("yyyy-MM-dd"),
            'due_date': self.due_date.date().toString("yyyy-MM-dd"),
            'subtotal': self.subtotal_input.value(),
            'tax_amount': self.tax_input.value(),
            'discount_amount': self.discount_input.value(),
            'total_amount': self.total_input.value(),
            'notes': self.notes_input.toPlainText().strip()
        }
        
        # جمع عناصر الفاتورة
        items_data = []
        for row in range(self.items_table.rowCount()):
            item_data = self.items_table.item(row, 0).data(Qt.UserRole)
            items_data.append(item_data)
            
        # التحقق من صحة البيانات
        errors = self.invoice_model.validate_invoice_data(invoice_data, items_data)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
            
        # حفظ الفاتورة
        try:
            success, invoice_id, message = self.invoice_model.create_invoice(
                invoice_data, items_data, self.user_data['id']
            )
            
            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الفاتورة:\n{str(e)}")
            
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
        """)


class InvoiceItemDialog(QDialog):
    """نافذة إضافة عنصر للفاتورة"""
    
    def __init__(self, parent=None, granite_types=None):
        super().__init__(parent)
        self.granite_types = granite_types or []
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة عنصر للفاتورة")
        self.setFixedSize(400, 350)
        self.setModal(True)
        
        layout = QVBoxLayout()
        form_layout = QFormLayout()
        
        # نوع الجرانيت
        self.granite_type_combo = QComboBox()
        for granite_type in self.granite_types:
            self.granite_type_combo.addItem(granite_type[1], granite_type[0])
        form_layout.addRow("نوع الجرانيت:", self.granite_type_combo)
        
        # الأبعاد
        self.length_input = QDoubleSpinBox()
        self.length_input.setRange(1, 1000)
        self.length_input.setDecimals(1)
        self.length_input.setSuffix(" سم")
        self.length_input.valueChanged.connect(self.calculate_area)
        form_layout.addRow("الطول:", self.length_input)
        
        self.width_input = QDoubleSpinBox()
        self.width_input.setRange(1, 1000)
        self.width_input.setDecimals(1)
        self.width_input.setSuffix(" سم")
        self.width_input.valueChanged.connect(self.calculate_area)
        form_layout.addRow("العرض:", self.width_input)
        
        self.thickness_input = QDoubleSpinBox()
        self.thickness_input.setRange(1, 50)
        self.thickness_input.setDecimals(1)
        self.thickness_input.setSuffix(" سم")
        form_layout.addRow("السمك:", self.thickness_input)
        
        # الكمية
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(1, 1000)
        self.quantity_input.valueChanged.connect(self.calculate_area)
        form_layout.addRow("الكمية:", self.quantity_input)
        
        # السعر
        self.unit_price_input = QDoubleSpinBox()
        self.unit_price_input.setRange(0, 10000)
        self.unit_price_input.setDecimals(2)
        self.unit_price_input.setSuffix(" جنيه/م²")
        self.unit_price_input.valueChanged.connect(self.calculate_total)
        form_layout.addRow("السعر/م²:", self.unit_price_input)
        
        # المساحة والإجمالي (للعرض فقط)
        self.area_label = QLabel("0.00 م²")
        form_layout.addRow("المساحة:", self.area_label)
        
        self.total_label = QLabel("0.00 جنيه")
        form_layout.addRow("الإجمالي:", self.total_label)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("إضافة")
        self.add_btn.clicked.connect(self.accept)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.add_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def calculate_area(self):
        """حساب المساحة"""
        length = self.length_input.value()
        width = self.width_input.value()
        quantity = self.quantity_input.value()
        
        area = (length * width * quantity) / 10000  # تحويل من سم² إلى م²
        self.area_label.setText(f"{area:.2f} م²")
        self.calculate_total()
        
    def calculate_total(self):
        """حساب الإجمالي"""
        area_text = self.area_label.text().replace(" م²", "")
        try:
            area = float(area_text)
            unit_price = self.unit_price_input.value()
            total = area * unit_price
            self.total_label.setText(f"{total:.2f} جنيه")
        except:
            self.total_label.setText("0.00 جنيه")
            
    def get_item_data(self):
        """الحصول على بيانات العنصر"""
        return {
            'granite_type_id': self.granite_type_combo.currentData(),
            'granite_type_name': self.granite_type_combo.currentText(),
            'length': self.length_input.value(),
            'width': self.width_input.value(),
            'thickness': self.thickness_input.value(),
            'quantity': self.quantity_input.value(),
            'unit_price': self.unit_price_input.value(),
            'description': f"{self.granite_type_combo.currentText()} - {self.length_input.value()}×{self.width_input.value()}×{self.thickness_input.value()} سم"
        }
