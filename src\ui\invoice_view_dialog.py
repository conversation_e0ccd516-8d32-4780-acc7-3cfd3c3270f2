# -*- coding: utf-8 -*-
"""
نافذة عرض تفاصيل الفاتورة
Invoice View Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QPushButton, QGroupBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QTextEdit, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

import config
from src.models.sales_invoice import SalesInvoice
from src.utils.font_utils import apply_font_settings, get_responsive_style

class InvoiceViewDialog(QDialog):
    """نافذة عرض تفاصيل الفاتورة"""
    
    def __init__(self, parent=None, database_manager=None, invoice_id=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.invoice_id = invoice_id
        self.invoice_model = SalesInvoice(database_manager)
        
        self.setup_ui()
        apply_font_settings(self)
        self.load_invoice_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تفاصيل الفاتورة")
        self.setFixedSize(800, 600)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # معلومات الفاتورة
        invoice_group = QGroupBox("معلومات الفاتورة")
        invoice_layout = QFormLayout()
        
        self.invoice_number_label = QLabel()
        self.invoice_number_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        invoice_layout.addRow("رقم الفاتورة:", self.invoice_number_label)
        
        self.customer_label = QLabel()
        invoice_layout.addRow("العميل:", self.customer_label)
        
        self.invoice_date_label = QLabel()
        invoice_layout.addRow("تاريخ الفاتورة:", self.invoice_date_label)
        
        self.due_date_label = QLabel()
        invoice_layout.addRow("تاريخ الاستحقاق:", self.due_date_label)
        
        self.status_label = QLabel()
        invoice_layout.addRow("الحالة:", self.status_label)
        
        self.created_by_label = QLabel()
        invoice_layout.addRow("أنشئت بواسطة:", self.created_by_label)
        
        invoice_group.setLayout(invoice_layout)
        layout.addWidget(invoice_group)
        
        # عناصر الفاتورة
        items_group = QGroupBox("عناصر الفاتورة")
        items_layout = QVBoxLayout()
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        
        headers = ["نوع الجرانيت", "الأبعاد (سم)", "الكمية", "السعر/م²", 
                  "المساحة (م²)", "الإجمالي", "ملاحظات"]
        self.items_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        
        items_layout.addWidget(self.items_table)
        items_group.setLayout(items_layout)
        layout.addWidget(items_group)
        
        # الإجماليات
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QFormLayout()
        
        self.subtotal_label = QLabel()
        self.subtotal_label.setStyleSheet("font-weight: bold;")
        totals_layout.addRow("المجموع الفرعي:", self.subtotal_label)
        
        self.tax_label = QLabel()
        totals_layout.addRow("الضريبة:", self.tax_label)
        
        self.discount_label = QLabel()
        totals_layout.addRow("الخصم:", self.discount_label)
        
        self.total_label = QLabel()
        self.total_label.setStyleSheet("font-weight: bold; color: #E74C3C; font-size: 16px;")
        totals_layout.addRow("الإجمالي النهائي:", self.total_label)
        
        self.paid_label = QLabel()
        self.paid_label.setStyleSheet("font-weight: bold; color: #27AE60;")
        totals_layout.addRow("المدفوع:", self.paid_label)
        
        self.remaining_label = QLabel()
        self.remaining_label.setStyleSheet("font-weight: bold; color: #F39C12;")
        totals_layout.addRow("المتبقي:", self.remaining_label)
        
        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)
        
        # ملاحظات
        if hasattr(self, 'notes_text'):
            notes_group = QGroupBox("ملاحظات")
            notes_layout = QVBoxLayout()
            
            self.notes_text = QTextEdit()
            self.notes_text.setMaximumHeight(60)
            self.notes_text.setReadOnly(True)
            notes_layout.addWidget(self.notes_text)
            
            notes_group.setLayout(notes_layout)
            layout.addWidget(notes_group)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.print_btn = QPushButton("طباعة")
        self.print_btn.clicked.connect(self.print_invoice)
        
        self.add_payment_btn = QPushButton("إضافة دفعة")
        self.add_payment_btn.clicked.connect(self.add_payment)
        
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.clicked.connect(self.accept)
        
        buttons_layout.addWidget(self.print_btn)
        buttons_layout.addWidget(self.add_payment_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        self.apply_styles()
        
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        if not self.invoice_id:
            return
            
        # تحميل معلومات الفاتورة
        invoice = self.invoice_model.get_invoice_by_id(self.invoice_id)
        if invoice:
            self.invoice_number_label.setText(invoice[1])
            self.customer_label.setText(f"{invoice[16]} - {invoice[17]}")
            self.invoice_date_label.setText(str(invoice[2])[:10] if invoice[2] else "")
            self.due_date_label.setText(str(invoice[3])[:10] if invoice[3] else "")
            
            # تحديد لون الحالة
            status_text = self.get_status_text(invoice[9])
            status_color = self.get_status_color(invoice[9])
            self.status_label.setText(status_text)
            self.status_label.setStyleSheet(f"font-weight: bold; color: {status_color};")
            
            self.created_by_label.setText(invoice[19] if invoice[19] else "غير محدد")
            
            # الإجماليات
            self.subtotal_label.setText(f"{invoice[4]:,.2f} جنيه")
            self.tax_label.setText(f"{invoice[5]:,.2f} جنيه")
            self.discount_label.setText(f"{invoice[6]:,.2f} جنيه")
            self.total_label.setText(f"{invoice[7]:,.2f} جنيه")
            self.paid_label.setText(f"{invoice[8]:,.2f} جنيه")
            
            remaining = invoice[7] - invoice[8]
            self.remaining_label.setText(f"{remaining:,.2f} جنيه")
            
            # الملاحظات
            if hasattr(self, 'notes_text'):
                self.notes_text.setPlainText(invoice[10] if invoice[10] else "لا توجد ملاحظات")
        
        # تحميل عناصر الفاتورة
        items = self.invoice_model.get_invoice_items(self.invoice_id)
        self.populate_items_table(items)
        
    def populate_items_table(self, items):
        """ملء جدول العناصر"""
        self.items_table.setRowCount(len(items))
        
        for row, item in enumerate(items):
            # تكوين نص الأبعاد
            dimensions = f"{item[2]}×{item[3]}×{item[4]}"
            
            items_data = [
                str(item[11]) if item[11] else "غير محدد",  # granite_type_name
                dimensions,  # الأبعاد
                str(item[5]),  # quantity
                f"{item[6]:,.2f}",  # unit_price
                f"{item[12]:,.2f}",  # total_area
                f"{item[13]:,.2f}",  # total_price
                str(item[7]) if item[7] else ""  # notes
            ]
            
            for col, item_text in enumerate(items_data):
                item_widget = QTableWidgetItem(item_text)
                if col in [2, 3, 4, 5]:  # الأعمدة الرقمية
                    item_widget.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row, col, item_widget)
                
    def get_status_text(self, status):
        """تحويل حالة الفاتورة إلى نص عربي"""
        status_map = {
            'pending': 'معلقة',
            'partial': 'مدفوعة جزئياً',
            'paid': 'مدفوعة كاملة'
        }
        return status_map.get(status, status)
        
    def get_status_color(self, status):
        """الحصول على لون الحالة"""
        color_map = {
            'pending': '#F39C12',
            'partial': '#3498DB',
            'paid': '#27AE60'
        }
        return color_map.get(status, '#2C3E50')
        
    def print_invoice(self):
        """طباعة الفاتورة"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير وظيفة الطباعة قريباً")
        
    def add_payment(self):
        """إضافة دفعة للفاتورة"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير نافذة إضافة الدفعة قريباً")
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
        """)
