#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
"""

import sys
import os

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    
    print("✅ تم استيراد PyQt5 بنجاح")
    
    # اختبار قاعدة البيانات
    try:
        import config
        print("✅ تم استيراد ملف الإعدادات بنجاح")

        # استخدام SQLite للاختبار
        from src.database.sqlite_manager import SQLiteManager
        print("✅ تم استيراد مدير قاعدة البيانات SQLite بنجاح")

        # اختبار الاتصال
        db_manager = SQLiteManager()
        if db_manager.test_connection():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")

            # إعداد قاعدة البيانات
            if db_manager.setup_database():
                print("✅ تم إعداد قاعدة البيانات بنجاح")
            else:
                print("❌ فشل في إعداد قاعدة البيانات")
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")

    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
    
    # اختبار واجهة المستخدم
    try:
        app = QApplication(sys.argv)
        
        msg = QMessageBox()
        msg.setWindowTitle("اختبار التطبيق")
        msg.setText("تم تشغيل التطبيق بنجاح!\n\nهل تريد المتابعة لإنشاء قاعدة البيانات؟")
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        
        result = msg.exec_()
        
        if result == QMessageBox.Yes:
            # اختبار تسجيل الدخول
            try:
                from src.models.user import User
                from src.database.sqlite_manager import SQLiteManager

                db_manager = SQLiteManager()
                user_model = User(db_manager)

                # اختبار تسجيل الدخول
                user_data = user_model.authenticate("admin", "admin123")
                if user_data:
                    QMessageBox.information(None, "نجح", f"تم تسجيل الدخول بنجاح!\nمرحباً {user_data['full_name']}")
                else:
                    QMessageBox.warning(None, "خطأ", "فشل في تسجيل الدخول")
            except Exception as e:
                QMessageBox.critical(None, "خطأ", f"خطأ في اختبار تسجيل الدخول:\n{str(e)}")
        
        print("✅ تم اختبار واجهة المستخدم بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في واجهة المستخدم: {str(e)}")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {str(e)}")
    print("يرجى تثبيت المكتبات المطلوبة باستخدام: pip install -r requirements.txt")

print("\n" + "="*50)
print("انتهى الاختبار")
print("="*50)
