#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث جميع الواجهات لتصبح حديثة
Modernize All Widgets
"""

import sys
import os
import shutil

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_modern_expenses_widget():
    """إنشاء واجهة المصاريف الحديثة"""
    print("💸 إنشاء واجهة المصاريف الحديثة...")
    
    content = '''# -*- coding: utf-8 -*-
"""
واجهة إدارة المصاريف المحسنة
Modern Expenses Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTable<PERSON>idgetItem, Q<PERSON>ush<PERSON>utton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QComboBox, QFrame, QTabWidget,
                            QGridLayout, QGroupBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from src.utils.font_utils import apply_font_settings, get_responsive_style

class ExpensesWidget(QWidget):
    """واجهة إدارة المصاريف المحسنة"""
    
    def __init__(self, database_manager=None, user_data=None):
        super().__init__()
        
        # إنشاء مدير قاعدة البيانات إذا لم يتم تمريره
        if not database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()
        else:
            self.database_manager = database_manager
            
        self.user_data = user_data or {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام'}
        
        self.setup_ui()
        self.apply_modern_styles()
        apply_font_settings(self)
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # العنوان الرئيسي
        self.create_header_section(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        self.setLayout(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # العنوان مع الأيقونة
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("💸")
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel("إدارة المصاريف")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 15px;
        """)
        
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # الأزرار السريعة
        actions_layout = QHBoxLayout()
        
        add_expense_btn = QPushButton("➕ مصروف جديد")
        add_expense_btn.setObjectName("modernButton")
        add_expense_btn.clicked.connect(self.add_expense)
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setObjectName("modernButton")
        refresh_btn.clicked.connect(self.load_data)
        
        actions_layout.addWidget(add_expense_btn)
        actions_layout.addWidget(refresh_btn)
        
        header_layout.addLayout(title_layout)
        header_layout.addLayout(actions_layout)
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # إحصائيات المصاريف
        self.create_expenses_stats(content_layout)
        
        # جدول المصاريف
        self.create_expenses_table(content_layout)
        
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)
        
    def create_expenses_stats(self, layout):
        """إنشاء إحصائيات المصاريف"""
        stats_frame = QFrame()
        stats_frame.setObjectName("card")
        
        stats_layout = QGridLayout()
        
        # بيانات الإحصائيات
        stats_data = [
            ("💸", "إجمالي المصاريف", "25,000", "ريال هذا الشهر"),
            ("📊", "عدد المصاريف", "15", "مصروف"),
            ("⛽", "وقود وصيانة", "8,000", "ريال"),
            ("👥", "رواتب", "12,000", "ريال"),
        ]
        
        for i, (icon, title, value, subtitle) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, subtitle)
            stats_layout.addWidget(card, 0, i)
            
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)
        
    def create_stat_card(self, icon, title, value, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(100)
        
        layout = QVBoxLayout()
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #2c3e50;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #e74c3c;")
        
        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("font-size: 10px; color: #7f8c8d;")
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        
        card.setLayout(layout)
        return card
        
    def create_expenses_table(self, layout):
        """إنشاء جدول المصاريف"""
        table_frame = QFrame()
        table_frame.setObjectName("card")
        table_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("📋 قائمة المصاريف")
        table_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.expenses_table = QTableWidget()
        self.expenses_table.setObjectName("modernTable")
        
        # إعداد أعمدة الجدول
        columns = ["التاريخ", "الوصف", "الفئة", "المبلغ", "طريقة الدفع", "الإجراءات"]
        self.expenses_table.setColumnCount(len(columns))
        self.expenses_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.horizontalHeader().setStretchLastSection(True)
        
        table_layout.addWidget(self.expenses_table)
        table_frame.setLayout(table_layout)
        layout.addWidget(table_frame)
        
    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QFrame#headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 3px solid #e74c3c;
            }
            
            QFrame#contentFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
            }
            
            QFrame#card {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
            
            QFrame#statCard {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
            
            QFrame#statCard:hover {
                border-color: #e74c3c;
                box-shadow: 0 2px 8px rgba(231, 76, 60, 0.1);
            }
            
            QPushButton#modernButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }
            
            QPushButton#modernButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
            
            QTableWidget#modernTable {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                gridline-color: #f1f2f6;
            }
            
            QTableWidget#modernTable QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        # بيانات تجريبية
        expenses_data = [
            ("2025-01-15", "وقود الجرارات", "وقود", "1,500", "نقدي"),
            ("2025-01-14", "صيانة المعدات", "صيانة", "2,800", "شيك"),
            ("2025-01-13", "راتب العامل أحمد", "رواتب", "3,000", "تحويل"),
            ("2025-01-12", "قطع غيار", "صيانة", "850", "نقدي"),
        ]
        
        self.expenses_table.setRowCount(len(expenses_data))
        
        for row, expense in enumerate(expenses_data):
            for col, value in enumerate(expense):
                self.expenses_table.setItem(row, col, QTableWidgetItem(str(value)))
            
            # إضافة أزرار الإجراءات
            actions_widget = self.create_actions_widget(row)
            self.expenses_table.setCellWidget(row, 5, actions_widget)
            
    def create_actions_widget(self, expense_id):
        """إنشاء ويدجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_expense(expense_id))
        
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("حذف")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.delete_expense(expense_id))
        
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)
        
        widget.setLayout(layout)
        return widget
        
    def add_expense(self):
        """إضافة مصروف جديد"""
        QMessageBox.information(self, "قيد التطوير", "نافذة إضافة مصروف قيد التطوير")
        
    def edit_expense(self, expense_id):
        """تعديل مصروف"""
        QMessageBox.information(self, "قيد التطوير", f"تعديل المصروف رقم {expense_id}")
        
    def delete_expense(self, expense_id):
        """حذف مصروف"""
        reply = QMessageBox.question(self, "تأكيد الحذف", "هل تريد حذف هذا المصروف؟")
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم حذف المصروف")
'''
    
    try:
        with open("src/ui/expenses_widget.py", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ تم إنشاء واجهة المصاريف الحديثة")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء واجهة المصاريف: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎨 بدء تحديث جميع الواجهات")
    print("=" * 50)
    
    # إنشاء الواجهات الحديثة
    expenses_ok = create_modern_expenses_widget()
    
    # نسخ واجهة المبيعات الحديثة
    try:
        if os.path.exists("src/ui/sales_widget_modern.py"):
            shutil.copy("src/ui/sales_widget_modern.py", "src/ui/sales_widget.py")
            print("✅ تم تحديث واجهة المبيعات")
            sales_ok = True
        else:
            sales_ok = False
    except Exception as e:
        print(f"❌ خطأ في تحديث واجهة المبيعات: {str(e)}")
        sales_ok = False
    
    # تقرير النتائج
    print("\n📊 تقرير التحديث:")
    print("=" * 30)
    print(f"واجهة المبيعات: {'✅ محدثة' if sales_ok else '❌ فشل'}")
    print(f"واجهة المصاريف: {'✅ محدثة' if expenses_ok else '❌ فشل'}")
    
    if sales_ok and expenses_ok:
        print("\n🎉 تم تحديث جميع الواجهات بنجاح!")
        print("💡 شغل النظام: python run_modern_system.py")
        return 0
    else:
        print("\n⚠️ بعض الواجهات واجهت مشاكل")
        return 1

if __name__ == "__main__":
    sys.exit(main())
