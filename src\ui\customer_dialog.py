# -*- coding: utf-8 -*-
"""
نافذة إضافة/تعديل العملاء
Customer Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QDoubleSpinBox, QPushButton,
                            QMessageBox, QLabel, QGroupBox, QCheckBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

import config
from src.models.customer import Customer
from src.utils.font_utils import apply_font_settings, get_responsive_style

class CustomerDialog(QDialog):
    """نافذة إضافة/تعديل العملاء"""
    
    def __init__(self, parent=None, database_manager=None, user_data=None, customer_id=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.user_data = user_data
        self.customer_id = customer_id

        # إنشاء مدير قاعدة البيانات إذا لم يتم تمريره
        if not self.database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()

        self.customer_model = Customer(self.database_manager)

        self.setup_ui()
        apply_font_settings(self)
        if customer_id:
            self.load_customer_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة عميل جديد" if not self.customer_id else "تعديل العميل")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # معلومات أساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_layout = QFormLayout()
        
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم العميل (مطلوب)")
        basic_layout.addRow("الاسم:", self.name_input)
        
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        basic_layout.addRow("الهاتف:", self.phone_input)
        
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        basic_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # معلومات العنوان
        address_group = QGroupBox("معلومات العنوان")
        address_layout = QFormLayout()
        
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setPlaceholderText("العنوان التفصيلي")
        address_layout.addRow("العنوان:", self.address_input)
        
        address_group.setLayout(address_layout)
        layout.addWidget(address_group)
        
        # معلومات مالية
        financial_group = QGroupBox("المعلومات المالية")
        financial_layout = QFormLayout()
        
        self.tax_number_input = QLineEdit()
        self.tax_number_input.setPlaceholderText("الرقم الضريبي")
        financial_layout.addRow("الرقم الضريبي:", self.tax_number_input)
        
        self.credit_limit_input = QDoubleSpinBox()
        self.credit_limit_input.setRange(0, 1000000)
        self.credit_limit_input.setDecimals(2)
        self.credit_limit_input.setSuffix(" جنيه")
        financial_layout.addRow("حد الائتمان:", self.credit_limit_input)
        
        financial_group.setLayout(financial_layout)
        layout.addWidget(financial_group)
        
        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setPlaceholderText("ملاحظات إضافية عن العميل...")
        notes_layout.addWidget(self.notes_input)
        
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_customer)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        self.apply_styles()

    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        customer = self.customer_model.get_customer_by_id(self.customer_id)
        if customer:
            self.name_input.setText(customer[1] if customer[1] else "")
            self.phone_input.setText(customer[2] if customer[2] else "")
            self.address_input.setPlainText(customer[3] if customer[3] else "")
            self.email_input.setText(customer[4] if customer[4] else "")
            self.tax_number_input.setText(customer[5] if customer[5] else "")
            self.credit_limit_input.setValue(customer[6] if customer[6] else 0)
            self.notes_input.setPlainText(customer[9] if customer[9] else "")
            
    def save_customer(self):
        """حفظ العميل"""
        # جمع البيانات
        customer_data = {
            'name': self.name_input.text().strip(),
            'phone': self.phone_input.text().strip(),
            'address': self.address_input.toPlainText().strip(),
            'email': self.email_input.text().strip(),
            'tax_number': self.tax_number_input.text().strip(),
            'credit_limit': self.credit_limit_input.value(),
            'notes': self.notes_input.toPlainText().strip()
        }
        
        # التحقق من صحة البيانات
        errors = self.customer_model.validate_customer_data(customer_data)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
            
        # حفظ البيانات
        try:
            if self.customer_id:
                # تحديث
                success, message = self.customer_model.update_customer(
                    self.customer_id, customer_data, self.user_data['id']
                )
            else:
                # إضافة جديد
                success, customer_id, message = self.customer_model.add_customer(
                    customer_data, self.user_data['id']
                )
                
            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات:\n{str(e)}")
            
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(get_responsive_style(config.COLORS['secondary']))
