# -*- coding: utf-8 -*-
"""
سكريبت إنشاء جداول قاعدة البيانات
Database Tables Creation Script
"""

from src.database.database_manager import DatabaseManager
from src.utils.logger import log_database_operation, log_error

class DatabaseCreator:
    """منشئ جداول قاعدة البيانات"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        
    def create_all_tables(self):
        """إنشاء جميع الجداول"""
        try:
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            if not self.db_manager.create_database_if_not_exists():
                return False
                
            if not self.db_manager.connect():
                return False
                
            # قائمة الجداول للإنشاء
            tables = [
                self.create_users_table,
                self.create_granite_types_table,
                self.create_trucks_table,
                self.create_blocks_table,
                self.create_cutting_operations_table,
                self.create_slices_table,
                self.create_customers_table,
                self.create_sales_invoices_table,
                self.create_invoice_items_table,
                self.create_payments_table,
                self.create_expenses_table,
                self.create_orders_table,
                self.create_inventory_table,
                self.create_user_logs_table
            ]
            
            # إنشاء الجداول
            for create_table_func in tables:
                if not create_table_func():
                    return False
                    
            # إدراج البيانات الأساسية
            self.insert_initial_data()
            
            self.db_manager.disconnect()
            log_database_operation("CREATE", "ALL_TABLES", "تم إنشاء جميع الجداول بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في إنشاء الجداول: {str(e)}")
            return False
            
    def create_users_table(self):
        """إنشاء جدول المستخدمين"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
        CREATE TABLE users (
            id INT IDENTITY(1,1) PRIMARY KEY,
            username NVARCHAR(50) UNIQUE NOT NULL,
            password_hash NVARCHAR(255) NOT NULL,
            full_name NVARCHAR(100) NOT NULL,
            role NVARCHAR(20) NOT NULL,
            is_active BIT DEFAULT 1,
            created_at DATETIME DEFAULT GETDATE(),
            last_login DATETIME,
            created_by INT,
            notes NVARCHAR(500)
        )
        """
        return self.db_manager.execute_query(query) is not None
        
    def create_granite_types_table(self):
        """إنشاء جدول أنواع الجرانيت"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='granite_types' AND xtype='U')
        CREATE TABLE granite_types (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            description NVARCHAR(500),
            price_per_sqm DECIMAL(10,2),
            is_active BIT DEFAULT 1,
            created_at DATETIME DEFAULT GETDATE()
        )
        """
        return self.db_manager.execute_query(query) is not None
        
    def create_trucks_table(self):
        """إنشاء جدول الجرارات"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='trucks' AND xtype='U')
        CREATE TABLE trucks (
            id INT IDENTITY(1,1) PRIMARY KEY,
            truck_number NVARCHAR(50) NOT NULL,
            weight_tons DECIMAL(8,2) NOT NULL,
            price_per_ton DECIMAL(10,2) NOT NULL,
            total_cost DECIMAL(12,2) NOT NULL,
            arrival_date DATETIME NOT NULL,
            supplier NVARCHAR(200),
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE()
        )
        """
        return self.db_manager.execute_query(query) is not None
        
    def create_blocks_table(self):
        """إنشاء جدول البلوكات"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='blocks' AND xtype='U')
        CREATE TABLE blocks (
            id INT IDENTITY(1,1) PRIMARY KEY,
            block_number NVARCHAR(50) NOT NULL,
            truck_id INT NOT NULL,
            granite_type_id INT NOT NULL,
            length_cm DECIMAL(8,2) NOT NULL,
            width_cm DECIMAL(8,2) NOT NULL,
            height_cm DECIMAL(8,2) NOT NULL,
            weight_kg DECIMAL(10,2),
            volume_cbm AS (length_cm * width_cm * height_cm / 1000000),
            status NVARCHAR(20) DEFAULT 'available',
            location NVARCHAR(100),
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (truck_id) REFERENCES trucks(id),
            FOREIGN KEY (granite_type_id) REFERENCES granite_types(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_cutting_operations_table(self):
        """إنشاء جدول عمليات النشر"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cutting_operations' AND xtype='U')
        CREATE TABLE cutting_operations (
            id INT IDENTITY(1,1) PRIMARY KEY,
            block_id INT NOT NULL,
            operation_date DATETIME NOT NULL,
            slices_produced INT NOT NULL,
            waste_percentage DECIMAL(5,2),
            operator_name NVARCHAR(100),
            machine_used NVARCHAR(100),
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (block_id) REFERENCES blocks(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_slices_table(self):
        """إنشاء جدول الشرائح"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='slices' AND xtype='U')
        CREATE TABLE slices (
            id INT IDENTITY(1,1) PRIMARY KEY,
            slice_number NVARCHAR(50) NOT NULL,
            block_id INT NOT NULL,
            cutting_operation_id INT,
            granite_type_id INT NOT NULL,
            length_cm DECIMAL(8,2) NOT NULL,
            width_cm DECIMAL(8,2) NOT NULL,
            thickness_cm DECIMAL(6,2) NOT NULL,
            area_sqm AS (length_cm * width_cm / 10000),
            quality_grade NVARCHAR(20) DEFAULT 'A',
            status NVARCHAR(20) DEFAULT 'available',
            location NVARCHAR(100),
            price_per_sqm DECIMAL(10,2),
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (block_id) REFERENCES blocks(id),
            FOREIGN KEY (cutting_operation_id) REFERENCES cutting_operations(id),
            FOREIGN KEY (granite_type_id) REFERENCES granite_types(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_customers_table(self):
        """إنشاء جدول العملاء"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='customers' AND xtype='U')
        CREATE TABLE customers (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(200) NOT NULL,
            phone NVARCHAR(20),
            address NVARCHAR(500),
            email NVARCHAR(100),
            tax_number NVARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0,
            current_balance DECIMAL(12,2) DEFAULT 0,
            is_active BIT DEFAULT 1,
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE()
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_sales_invoices_table(self):
        """إنشاء جدول فواتير المبيعات"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sales_invoices' AND xtype='U')
        CREATE TABLE sales_invoices (
            id INT IDENTITY(1,1) PRIMARY KEY,
            invoice_number NVARCHAR(50) UNIQUE NOT NULL,
            customer_id INT NOT NULL,
            invoice_date DATETIME NOT NULL,
            due_date DATETIME,
            subtotal DECIMAL(12,2) NOT NULL,
            tax_amount DECIMAL(12,2) DEFAULT 0,
            discount_amount DECIMAL(12,2) DEFAULT 0,
            total_amount DECIMAL(12,2) NOT NULL,
            paid_amount DECIMAL(12,2) DEFAULT 0,
            remaining_amount AS (total_amount - paid_amount),
            status NVARCHAR(20) DEFAULT 'pending',
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (customer_id) REFERENCES customers(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_invoice_items_table(self):
        """إنشاء جدول عناصر الفواتير"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='invoice_items' AND xtype='U')
        CREATE TABLE invoice_items (
            id INT IDENTITY(1,1) PRIMARY KEY,
            invoice_id INT NOT NULL,
            slice_id INT,
            granite_type_id INT NOT NULL,
            description NVARCHAR(500),
            length_cm DECIMAL(8,2) NOT NULL,
            width_cm DECIMAL(8,2) NOT NULL,
            thickness_cm DECIMAL(6,2) NOT NULL,
            quantity INT NOT NULL,
            area_sqm AS (length_cm * width_cm * quantity / 10000),
            unit_price DECIMAL(10,2) NOT NULL,
            total_price AS (length_cm * width_cm * quantity * unit_price / 10000),
            notes NVARCHAR(500),
            FOREIGN KEY (invoice_id) REFERENCES sales_invoices(id),
            FOREIGN KEY (slice_id) REFERENCES slices(id),
            FOREIGN KEY (granite_type_id) REFERENCES granite_types(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_payments_table(self):
        """إنشاء جدول المدفوعات"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='payments' AND xtype='U')
        CREATE TABLE payments (
            id INT IDENTITY(1,1) PRIMARY KEY,
            invoice_id INT NOT NULL,
            payment_date DATETIME NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            payment_method NVARCHAR(50) NOT NULL,
            reference_number NVARCHAR(100),
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (invoice_id) REFERENCES sales_invoices(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_expenses_table(self):
        """إنشاء جدول المصاريف"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='expenses' AND xtype='U')
        CREATE TABLE expenses (
            id INT IDENTITY(1,1) PRIMARY KEY,
            expense_date DATETIME NOT NULL,
            expense_type NVARCHAR(100) NOT NULL,
            description NVARCHAR(500) NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            payment_method NVARCHAR(50),
            reference_number NVARCHAR(100),
            supplier NVARCHAR(200),
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE()
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_orders_table(self):
        """إنشاء جدول الطلبات والحجوزات"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='orders' AND xtype='U')
        CREATE TABLE orders (
            id INT IDENTITY(1,1) PRIMARY KEY,
            order_number NVARCHAR(50) UNIQUE NOT NULL,
            customer_id INT NOT NULL,
            order_date DATETIME NOT NULL,
            delivery_date DATETIME,
            granite_type_id INT NOT NULL,
            length_cm DECIMAL(8,2) NOT NULL,
            width_cm DECIMAL(8,2) NOT NULL,
            thickness_cm DECIMAL(6,2) NOT NULL,
            quantity INT NOT NULL,
            area_sqm AS (length_cm * width_cm * quantity / 10000),
            unit_price DECIMAL(10,2),
            total_amount DECIMAL(12,2),
            status NVARCHAR(20) DEFAULT 'pending',
            notes NVARCHAR(500),
            created_by INT NOT NULL,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (granite_type_id) REFERENCES granite_types(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_inventory_table(self):
        """إنشاء جدول المخزون"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='inventory' AND xtype='U')
        CREATE TABLE inventory (
            id INT IDENTITY(1,1) PRIMARY KEY,
            granite_type_id INT NOT NULL,
            thickness_cm DECIMAL(6,2) NOT NULL,
            total_area_sqm DECIMAL(12,2) DEFAULT 0,
            available_area_sqm DECIMAL(12,2) DEFAULT 0,
            reserved_area_sqm DECIMAL(12,2) DEFAULT 0,
            last_updated DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (granite_type_id) REFERENCES granite_types(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def create_user_logs_table(self):
        """إنشاء جدول سجل المستخدمين"""
        query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_logs' AND xtype='U')
        CREATE TABLE user_logs (
            id INT IDENTITY(1,1) PRIMARY KEY,
            user_id INT NOT NULL,
            action NVARCHAR(100) NOT NULL,
            table_name NVARCHAR(50),
            record_id INT,
            old_values NVARCHAR(MAX),
            new_values NVARCHAR(MAX),
            ip_address NVARCHAR(50),
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """
        return self.db_manager.execute_query(query) is not None

    def insert_initial_data(self):
        """إدراج البيانات الأساسية"""
        try:
            # إدراج أنواع الجرانيت
            granite_types = [
                ('جرانيت أحمر أسوان', 'جرانيت أحمر طبيعي من أسوان', 150.00),
                ('جرانيت رمادي', 'جرانيت رمادي عالي الجودة', 120.00),
                ('جرانيت أسود', 'جرانيت أسود لامع', 180.00),
                ('جرانيت وردي', 'جرانيت وردي طبيعي', 160.00),
                ('جرانيت أبيض', 'جرانيت أبيض نقي', 140.00),
                ('جرانيت بني', 'جرانيت بني طبيعي', 130.00),
                ('جرانيت أخضر', 'جرانيت أخضر نادر', 200.00)
            ]

            for name, desc, price in granite_types:
                query = """
                IF NOT EXISTS (SELECT 1 FROM granite_types WHERE name = ?)
                INSERT INTO granite_types (name, description, price_per_sqm)
                VALUES (?, ?, ?)
                """
                self.db_manager.execute_query(query, (name, name, desc, price))

            # إدراج مستخدم المدير الافتراضي
            import bcrypt
            password = "admin123"
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            admin_query = """
            IF NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin')
            INSERT INTO users (username, password_hash, full_name, role)
            VALUES ('admin', ?, 'مدير النظام', 'admin')
            """
            self.db_manager.execute_query(admin_query, (password_hash,))

            # تأكيد التغييرات
            if self.db_manager.connection:
                self.db_manager.connection.commit()

            log_database_operation("INSERT", "INITIAL_DATA", "تم إدراج البيانات الأساسية")
            return True

        except Exception as e:
            log_error(f"خطأ في إدراج البيانات الأساسية: {str(e)}")
            if self.db_manager.connection:
                self.db_manager.connection.rollback()
            return False

def create_database():
    """دالة مساعدة لإنشاء قاعدة البيانات"""
    creator = DatabaseCreator()
    return creator.create_all_tables()

if __name__ == "__main__":
    if create_database():
        print("تم إنشاء قاعدة البيانات بنجاح!")
    else:
        print("فشل في إنشاء قاعدة البيانات!")
