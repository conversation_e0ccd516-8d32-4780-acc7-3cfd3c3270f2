# نظرة عامة على النظام - مصنع الحسن للأحجار

## وصف النظام
نظام إدارة شامل ومتطور لمصنع الجرانيت يغطي جميع العمليات من استقبال المواد الخام حتى البيع النهائي والتقارير المالية.

## الهيكل العام للنظام

### 🏗️ البنية التقنية
- **اللغة**: Python 3.8+
- **واجهة المستخدم**: PyQt5
- **قاعدة البيانات**: SQLite (افتراضي) / SQL Server (اختياري)
- **التقارير**: ReportLab, OpenPyXL
- **المعمارية**: MVC (Model-View-Controller)

### 📁 هيكل المشروع
```
AL-Hassan/
├── src/                    # الكود المصدري
│   ├── models/            # نماذج البيانات
│   ├── ui/                # واجهات المستخدم
│   ├── database/          # إدارة قاعدة البيانات
│   ├── utils/             # الأدوات المساعدة
│   └── reports/           # نظام التقارير
├── database/              # ملفات قاعدة البيانات
├── logs/                  # ملفات السجلات
├── reports/               # التقارير المُنشأة
└── config.py              # إعدادات النظام
```

## الوحدات الرئيسية

### 1. 🏠 لوحة التحكم الرئيسية
**الملف**: `src/ui/dashboard_widget.py`
- عرض الإحصائيات العامة
- بطاقات الوصول السريع للأقسام
- متابعة الأنشطة الأخيرة
- مؤشرات الأداء الرئيسية

### 2. 🚛 إدارة الشاحنات والرحلات
**الملفات**: 
- `src/models/truck.py` - نموذج البيانات
- `src/ui/trucks_widget.py` - واجهة المستخدم

**الوظائف**:
- تسجيل الشاحنات الواردة والصادرة
- متابعة حالة الشاحنات (محملة، فارغة، في الطريق)
- إدارة بيانات السائقين
- تتبع الرحلات والمواعيد
- حساب تكاليف النقل

### 3. ⚙️ عمليات التقطيع والإنتاج
**الملفات**:
- `src/models/block.py` - نموذج البلوكات
- `src/models/cutting_operation.py` - عمليات التقطيع
- `src/models/slice.py` - نموذج الشرائح
- `src/ui/cutting_widget.py` - واجهة التقطيع

**الوظائف**:
- تسجيل البلوكات الواردة
- إدارة عمليات تقطيع البلوكات إلى شرائح
- متابعة حالة الشرائح (متاحة، محجوزة، مباعة)
- حساب المساحات والأبعاد تلقائياً
- تتبع كفاءة الإنتاج

### 4. 💰 إدارة المبيعات والفواتير
**الملفات**:
- `src/models/sales_invoice.py` - نموذج الفواتير
- `src/models/customer.py` - نموذج العملاء
- `src/ui/sales_widget.py` - واجهة المبيعات

**الوظائف**:
- إنشاء وإدارة الفواتير
- متابعة المدفوعات والمستحقات
- إدارة بيانات العملاء
- تتبع حالة الفواتير
- حساب الضرائب والخصومات

### 5. 📊 إدارة المصاريف التشغيلية
**الملفات**:
- `src/models/expense.py` - نموذج المصاريف
- `src/ui/expenses_widget.py` - واجهة المصاريف

**الوظائف**:
- تسجيل جميع أنواع المصاريف
- تصنيف المصاريف حسب النوع
- متابعة المصاريف الشهرية
- تقارير المصاريف المفصلة
- مراقبة الميزانية

### 6. 📋 إدارة الطلبات والحجوزات
**الملفات**:
- `src/models/order.py` - نموذج الطلبات
- `src/ui/orders_widget.py` - واجهة الطلبات

**الوظائف**:
- إنشاء وإدارة طلبات العملاء
- متابعة حالة الطلبات
- تنبيهات الطلبات المتأخرة
- ربط الطلبات بالمخزون المتاح
- جدولة التسليم

### 7. 📈 التقارير والإحصائيات
**الملفات**:
- `src/reports/report_generator.py` - مولد التقارير
- `src/ui/reports_widget.py` - واجهة التقارير

**الوظائف**:
- تقارير المبيعات والإيرادات
- تقارير المصاريف والتكاليف
- تقارير المخزون والإنتاج
- إحصائيات الأداء الشاملة
- تصدير التقارير بصيغ متعددة

### 8. 👥 نظام المستخدمين والصلاحيات
**الملفات**:
- `src/models/user.py` - نموذج المستخدمين
- `src/ui/login_window.py` - نافذة تسجيل الدخول

**الوظائف**:
- إدارة حسابات المستخدمين
- نظام صلاحيات متدرج
- تسجيل العمليات والأنشطة
- أمان البيانات والوصول

## قاعدة البيانات

### الجداول الرئيسية
1. **users** - المستخدمين
2. **trucks** - الشاحنات
3. **blocks** - البلوكات
4. **cutting_operations** - عمليات التقطيع
5. **slices** - الشرائح
6. **granite_types** - أنواع الجرانيت
7. **customers** - العملاء
8. **sales_invoices** - فواتير المبيعات
9. **invoice_items** - عناصر الفواتير
10. **payments** - المدفوعات
11. **expenses** - المصاريف
12. **orders** - الطلبات
13. **user_logs** - سجلات المستخدمين
14. **database_logs** - سجلات قاعدة البيانات

## الأمان والحماية

### مستويات الصلاحيات
- **admin**: مدير النظام - صلاحية كاملة
- **sales**: موظف مبيعات - المبيعات والعملاء
- **production**: مشرف إنتاج - التقطيع والمخزون
- **accountant**: محاسب - المالية والتقارير
- **warehouse**: أمين مخزن - المخزون فقط

### آليات الحماية
- تشفير كلمات المرور
- تسجيل جميع العمليات
- نسخ احتياطية تلقائية
- التحقق من صحة البيانات
- حماية من SQL Injection

## الأداء والتحسين

### تحسينات الأداء
- فهرسة قاعدة البيانات
- تحميل البيانات بالصفحات
- ذاكرة التخزين المؤقت
- ضغط الصور والملفات
- تحسين الاستعلامات

### المراقبة والصيانة
- ملفات السجلات التفصيلية
- مراقبة استخدام الذاكرة
- تنظيف البيانات القديمة
- نسخ احتياطية دورية
- تحديثات الأمان

## التطوير المستقبلي

### الميزات المخططة
- تطبيق ويب للوصول عن بُعد
- تطبيق موبايل للمتابعة
- تكامل مع أنظمة المحاسبة
- ذكاء اصطناعي لتحليل البيانات
- إشعارات فورية

### التحسينات المقترحة
- واجهة مستخدم محسنة
- تقارير تفاعلية
- لوحة تحكم متقدمة
- تحليلات متقدمة
- تكامل مع الأجهزة الذكية

---

**تم تطوير هذا النظام خصيصاً لمصنع الحسن للأحجار بواسطة فريق تطوير متخصص**
