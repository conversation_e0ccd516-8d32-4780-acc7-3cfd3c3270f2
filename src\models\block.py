# -*- coding: utf-8 -*-
"""
نموذج البلوكات
Block Model
"""

from datetime import datetime
from src.utils.logger import log_user_action, log_database_operation, log_error

class Block:
    """نموذج البلوكات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def add_block(self, block_data, user_id):
        """إضافة بلوك جديد"""
        try:
            query = """
            INSERT INTO blocks (block_number, truck_id, granite_type_id, length_cm, 
                              width_cm, height_cm, weight_kg, status, location, 
                              notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                block_data['block_number'],
                block_data['truck_id'],
                block_data['granite_type_id'],
                block_data['length_cm'],
                block_data['width_cm'],
                block_data['height_cm'],
                block_data.get('weight_kg'),
                block_data.get('status', 'available'),
                block_data.get('location', ''),
                block_data.get('notes', ''),
                user_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                # الحصول على ID البلوك الجديد
                block_id = self.db_manager.connection.lastrowid
                
                log_user_action(user_id, "ADD_BLOCK", f"إضافة بلوك رقم: {block_data['block_number']}")
                log_database_operation("INSERT", "blocks", f"بلوك ID: {block_id}")
                
                return True, block_id, "تم إضافة البلوك بنجاح"
            else:
                return False, None, "فشل في إضافة البلوك"
                
        except Exception as e:
            log_error(f"خطأ في إضافة البلوك: {str(e)}")
            return False, None, str(e)
            
    def get_all_blocks(self, limit=None):
        """الحصول على جميع البلوكات"""
        try:
            query = """
            SELECT b.id, b.block_number, b.length_cm, b.width_cm, b.height_cm,
                   b.weight_kg, b.status, b.location, b.notes, b.created_at,
                   t.truck_number, gt.name as granite_type_name,
                   u.full_name as created_by_name,
                   (b.length_cm * b.width_cm * b.height_cm / 1000000) as volume_cbm
            FROM blocks b
            LEFT JOIN trucks t ON b.truck_id = t.id
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            LEFT JOIN users u ON b.created_by = u.id
            ORDER BY b.created_at DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
                
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب البلوكات: {str(e)}")
            return []
            
    def get_blocks_by_truck(self, truck_id):
        """الحصول على البلوكات الخاصة بجرار معين"""
        try:
            query = """
            SELECT b.id, b.block_number, b.length_cm, b.width_cm, b.height_cm,
                   b.weight_kg, b.status, b.location, b.notes, b.created_at,
                   gt.name as granite_type_name,
                   (b.length_cm * b.width_cm * b.height_cm / 1000000) as volume_cbm
            FROM blocks b
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            WHERE b.truck_id = ?
            ORDER BY b.block_number
            """
            
            return self.db_manager.execute_query(query, (truck_id,), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب بلوكات الجرار: {str(e)}")
            return []
            
    def get_available_blocks(self):
        """الحصول على البلوكات المتاحة"""
        try:
            query = """
            SELECT b.id, b.block_number, b.length_cm, b.width_cm, b.height_cm,
                   b.weight_kg, b.location, b.notes,
                   gt.name as granite_type_name,
                   (b.length_cm * b.width_cm * b.height_cm / 1000000) as volume_cbm
            FROM blocks b
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            WHERE b.status = 'available'
            ORDER BY gt.name, b.block_number
            """
            
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب البلوكات المتاحة: {str(e)}")
            return []
            
    def get_block_by_id(self, block_id):
        """الحصول على بلوك بالمعرف"""
        try:
            query = """
            SELECT b.*, t.truck_number, gt.name as granite_type_name,
                   u.full_name as created_by_name,
                   (b.length_cm * b.width_cm * b.height_cm / 1000000) as volume_cbm
            FROM blocks b
            LEFT JOIN trucks t ON b.truck_id = t.id
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            LEFT JOIN users u ON b.created_by = u.id
            WHERE b.id = ?
            """
            
            return self.db_manager.execute_query(query, (block_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب البلوك: {str(e)}")
            return None
            
    def update_block_status(self, block_id, new_status, user_id):
        """تحديث حالة البلوك"""
        try:
            query = "UPDATE blocks SET status = ? WHERE id = ?"
            result = self.db_manager.execute_query(query, (new_status, block_id))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_BLOCK_STATUS", 
                              f"تحديث حالة البلوك ID: {block_id} إلى {new_status}")
                log_database_operation("UPDATE", "blocks", f"بلوك ID: {block_id}")
                
                return True, "تم تحديث حالة البلوك بنجاح"
            else:
                return False, "فشل في تحديث حالة البلوك"
                
        except Exception as e:
            log_error(f"خطأ في تحديث حالة البلوك: {str(e)}")
            return False, str(e)
            
    def update_block(self, block_id, block_data, user_id):
        """تحديث بيانات البلوك"""
        try:
            query = """
            UPDATE blocks 
            SET block_number = ?, granite_type_id = ?, length_cm = ?, 
                width_cm = ?, height_cm = ?, weight_kg = ?, 
                location = ?, notes = ?
            WHERE id = ?
            """
            
            params = (
                block_data['block_number'],
                block_data['granite_type_id'],
                block_data['length_cm'],
                block_data['width_cm'],
                block_data['height_cm'],
                block_data.get('weight_kg'),
                block_data.get('location', ''),
                block_data.get('notes', ''),
                block_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_BLOCK", f"تحديث بلوك ID: {block_id}")
                log_database_operation("UPDATE", "blocks", f"بلوك ID: {block_id}")
                
                return True, "تم تحديث البلوك بنجاح"
            else:
                return False, "فشل في تحديث البلوك"
                
        except Exception as e:
            log_error(f"خطأ في تحديث البلوك: {str(e)}")
            return False, str(e)
            
    def search_blocks(self, search_term):
        """البحث في البلوكات"""
        try:
            query = """
            SELECT b.id, b.block_number, b.length_cm, b.width_cm, b.height_cm,
                   b.weight_kg, b.status, b.location, b.notes, b.created_at,
                   t.truck_number, gt.name as granite_type_name,
                   (b.length_cm * b.width_cm * b.height_cm / 1000000) as volume_cbm
            FROM blocks b
            LEFT JOIN trucks t ON b.truck_id = t.id
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            WHERE b.block_number LIKE ? OR b.location LIKE ? OR b.notes LIKE ?
               OR t.truck_number LIKE ? OR gt.name LIKE ?
            ORDER BY b.created_at DESC
            """
            
            search_pattern = f"%{search_term}%"
            params = (search_pattern, search_pattern, search_pattern, 
                     search_pattern, search_pattern)
            
            return self.db_manager.execute_query(query, params, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في البحث في البلوكات: {str(e)}")
            return []
            
    def get_block_statistics(self):
        """الحصول على إحصائيات البلوكات"""
        try:
            stats = {}
            
            # إجمالي عدد البلوكات
            query = "SELECT COUNT(*) FROM blocks"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_blocks'] = result[0] if result else 0
            
            # البلوكات المتاحة
            query = "SELECT COUNT(*) FROM blocks WHERE status = 'available'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['available_blocks'] = result[0] if result else 0
            
            # البلوكات قيد النشر
            query = "SELECT COUNT(*) FROM blocks WHERE status = 'cutting'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['cutting_blocks'] = result[0] if result else 0
            
            # إجمالي الحجم
            query = """
            SELECT SUM(length_cm * width_cm * height_cm / 1000000) 
            FROM blocks WHERE status = 'available'
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_volume'] = result[0] if result and result[0] else 0
            
            # إحصائيات حسب نوع الجرانيت
            query = """
            SELECT gt.name, COUNT(b.id) as count,
                   SUM(b.length_cm * b.width_cm * b.height_cm / 1000000) as volume
            FROM blocks b
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            WHERE b.status = 'available'
            GROUP BY gt.name
            ORDER BY count DESC
            """
            result = self.db_manager.execute_query(query, fetch='all')
            stats['by_granite_type'] = result if result else []
            
            return stats
            
        except Exception as e:
            log_error(f"خطأ في جلب إحصائيات البلوكات: {str(e)}")
            return {}
            
    def validate_block_data(self, block_data):
        """التحقق من صحة بيانات البلوك"""
        errors = []
        
        # التحقق من رقم البلوك
        if not block_data.get('block_number', '').strip():
            errors.append("رقم البلوك مطلوب")
            
        # التحقق من الأبعاد
        dimensions = ['length_cm', 'width_cm', 'height_cm']
        for dim in dimensions:
            try:
                value = float(block_data.get(dim, 0))
                if value <= 0:
                    errors.append(f"{dim.replace('_cm', '')} يجب أن يكون أكبر من صفر")
            except (ValueError, TypeError):
                errors.append(f"{dim.replace('_cm', '')} يجب أن يكون رقماً صحيحاً")
                
        # التحقق من نوع الجرانيت
        if not block_data.get('granite_type_id'):
            errors.append("نوع الجرانيت مطلوب")
            
        # التحقق من الجرار
        if not block_data.get('truck_id'):
            errors.append("الجرار مطلوب")
            
        return errors
