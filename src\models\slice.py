# -*- coding: utf-8 -*-
"""
نموذج الشرائح
Slices Model
"""

from datetime import datetime
from src.utils.logger import log_user_action, log_database_operation, log_error

class Slice:
    """نموذج الشرائح"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def add_slice(self, slice_data, user_id):
        """إضافة شريحة جديدة"""
        try:
            query = """
            INSERT INTO slices (slice_number, block_id, cutting_operation_id, granite_type_id,
                              length_cm, width_cm, thickness_cm, quality_grade, status,
                              location, price_per_sqm, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                slice_data['slice_number'],
                slice_data['block_id'],
                slice_data.get('cutting_operation_id'),
                slice_data['granite_type_id'],
                slice_data['length_cm'],
                slice_data['width_cm'],
                slice_data['thickness_cm'],
                slice_data.get('quality_grade', 'A'),
                slice_data.get('status', 'available'),
                slice_data.get('location', ''),
                slice_data.get('price_per_sqm'),
                slice_data.get('notes', ''),
                user_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                # الحصول على ID الشريحة الجديدة
                slice_id = self.db_manager.connection.lastrowid
                
                log_user_action(user_id, "ADD_SLICE", f"إضافة شريحة رقم: {slice_data['slice_number']}")
                log_database_operation("INSERT", "slices", f"شريحة ID: {slice_id}")
                
                return True, slice_id, "تم إضافة الشريحة بنجاح"
            else:
                return False, None, "فشل في إضافة الشريحة"
                
        except Exception as e:
            log_error(f"خطأ في إضافة الشريحة: {str(e)}")
            return False, None, str(e)
            
    def get_all_slices(self, limit=None):
        """الحصول على جميع الشرائح"""
        try:
            query = """
            SELECT s.id, s.slice_number, s.length_cm, s.width_cm, s.thickness_cm,
                   s.quality_grade, s.status, s.location, s.price_per_sqm, s.notes,
                   s.created_at, b.block_number, gt.name as granite_type_name,
                   u.full_name as created_by_name,
                   (s.length_cm * s.width_cm / 10000) as area_sqm
            FROM slices s
            LEFT JOIN blocks b ON s.block_id = b.id
            LEFT JOIN granite_types gt ON s.granite_type_id = gt.id
            LEFT JOIN users u ON s.created_by = u.id
            ORDER BY s.created_at DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
                
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الشرائح: {str(e)}")
            return []
            
    def get_available_slices(self):
        """الحصول على الشرائح المتاحة"""
        try:
            query = """
            SELECT s.id, s.slice_number, s.length_cm, s.width_cm, s.thickness_cm,
                   s.quality_grade, s.location, s.price_per_sqm, s.notes,
                   gt.name as granite_type_name,
                   (s.length_cm * s.width_cm / 10000) as area_sqm
            FROM slices s
            LEFT JOIN granite_types gt ON s.granite_type_id = gt.id
            WHERE s.status = 'available'
            ORDER BY gt.name, s.thickness_cm, s.slice_number
            """
            
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الشرائح المتاحة: {str(e)}")
            return []
            
    def get_slices_by_block(self, block_id):
        """الحصول على الشرائح الخاصة ببلوك معين"""
        try:
            query = """
            SELECT s.id, s.slice_number, s.length_cm, s.width_cm, s.thickness_cm,
                   s.quality_grade, s.status, s.location, s.price_per_sqm, s.notes,
                   s.created_at, gt.name as granite_type_name,
                   (s.length_cm * s.width_cm / 10000) as area_sqm
            FROM slices s
            LEFT JOIN granite_types gt ON s.granite_type_id = gt.id
            WHERE s.block_id = ?
            ORDER BY s.slice_number
            """
            
            return self.db_manager.execute_query(query, (block_id,), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب شرائح البلوك: {str(e)}")
            return []
            
    def get_slice_by_id(self, slice_id):
        """الحصول على شريحة بالمعرف"""
        try:
            query = """
            SELECT s.*, b.block_number, gt.name as granite_type_name,
                   u.full_name as created_by_name,
                   (s.length_cm * s.width_cm / 10000) as area_sqm
            FROM slices s
            LEFT JOIN blocks b ON s.block_id = b.id
            LEFT JOIN granite_types gt ON s.granite_type_id = gt.id
            LEFT JOIN users u ON s.created_by = u.id
            WHERE s.id = ?
            """
            
            return self.db_manager.execute_query(query, (slice_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب الشريحة: {str(e)}")
            return None
            
    def update_slice_status(self, slice_id, new_status, user_id):
        """تحديث حالة الشريحة"""
        try:
            query = "UPDATE slices SET status = ? WHERE id = ?"
            result = self.db_manager.execute_query(query, (new_status, slice_id))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_SLICE_STATUS", 
                              f"تحديث حالة الشريحة ID: {slice_id} إلى {new_status}")
                log_database_operation("UPDATE", "slices", f"شريحة ID: {slice_id}")
                
                return True, "تم تحديث حالة الشريحة بنجاح"
            else:
                return False, "فشل في تحديث حالة الشريحة"
                
        except Exception as e:
            log_error(f"خطأ في تحديث حالة الشريحة: {str(e)}")
            return False, str(e)
            
    def update_slice(self, slice_id, slice_data, user_id):
        """تحديث بيانات الشريحة"""
        try:
            query = """
            UPDATE slices 
            SET slice_number = ?, length_cm = ?, width_cm = ?, thickness_cm = ?,
                quality_grade = ?, location = ?, price_per_sqm = ?, notes = ?
            WHERE id = ?
            """
            
            params = (
                slice_data['slice_number'],
                slice_data['length_cm'],
                slice_data['width_cm'],
                slice_data['thickness_cm'],
                slice_data.get('quality_grade', 'A'),
                slice_data.get('location', ''),
                slice_data.get('price_per_sqm'),
                slice_data.get('notes', ''),
                slice_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_SLICE", f"تحديث شريحة ID: {slice_id}")
                log_database_operation("UPDATE", "slices", f"شريحة ID: {slice_id}")
                
                return True, "تم تحديث الشريحة بنجاح"
            else:
                return False, "فشل في تحديث الشريحة"
                
        except Exception as e:
            log_error(f"خطأ في تحديث الشريحة: {str(e)}")
            return False, str(e)
            
    def search_slices(self, search_term):
        """البحث في الشرائح"""
        try:
            query = """
            SELECT s.id, s.slice_number, s.length_cm, s.width_cm, s.thickness_cm,
                   s.quality_grade, s.status, s.location, s.price_per_sqm, s.notes,
                   s.created_at, b.block_number, gt.name as granite_type_name,
                   (s.length_cm * s.width_cm / 10000) as area_sqm
            FROM slices s
            LEFT JOIN blocks b ON s.block_id = b.id
            LEFT JOIN granite_types gt ON s.granite_type_id = gt.id
            WHERE s.slice_number LIKE ? OR s.location LIKE ? OR s.notes LIKE ?
               OR b.block_number LIKE ? OR gt.name LIKE ?
            ORDER BY s.created_at DESC
            """
            
            search_pattern = f"%{search_term}%"
            params = (search_pattern, search_pattern, search_pattern, 
                     search_pattern, search_pattern)
            
            return self.db_manager.execute_query(query, params, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في البحث في الشرائح: {str(e)}")
            return []
            
    def get_slice_statistics(self):
        """الحصول على إحصائيات الشرائح"""
        try:
            stats = {}
            
            # إجمالي عدد الشرائح
            query = "SELECT COUNT(*) FROM slices"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_slices'] = result[0] if result else 0
            
            # الشرائح المتاحة
            query = "SELECT COUNT(*) FROM slices WHERE status = 'available'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['available_slices'] = result[0] if result else 0
            
            # الشرائح المباعة
            query = "SELECT COUNT(*) FROM slices WHERE status = 'sold'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['sold_slices'] = result[0] if result else 0
            
            # إجمالي المساحة المتاحة
            query = """
            SELECT SUM(length_cm * width_cm / 10000) 
            FROM slices WHERE status = 'available'
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_available_area'] = result[0] if result and result[0] else 0
            
            # إحصائيات حسب نوع الجرانيت
            query = """
            SELECT gt.name, COUNT(s.id) as count,
                   SUM(s.length_cm * s.width_cm / 10000) as area
            FROM slices s
            LEFT JOIN granite_types gt ON s.granite_type_id = gt.id
            WHERE s.status = 'available'
            GROUP BY gt.name
            ORDER BY count DESC
            """
            result = self.db_manager.execute_query(query, fetch='all')
            stats['by_granite_type'] = result if result else []
            
            # إحصائيات حسب السمك
            query = """
            SELECT thickness_cm, COUNT(*) as count,
                   SUM(length_cm * width_cm / 10000) as area
            FROM slices
            WHERE status = 'available'
            GROUP BY thickness_cm
            ORDER BY thickness_cm
            """
            result = self.db_manager.execute_query(query, fetch='all')
            stats['by_thickness'] = result if result else []
            
            return stats
            
        except Exception as e:
            log_error(f"خطأ في جلب إحصائيات الشرائح: {str(e)}")
            return {}
            
    def validate_slice_data(self, slice_data):
        """التحقق من صحة بيانات الشريحة"""
        errors = []
        
        # التحقق من رقم الشريحة
        if not slice_data.get('slice_number', '').strip():
            errors.append("رقم الشريحة مطلوب")
            
        # التحقق من الأبعاد
        dimensions = ['length_cm', 'width_cm', 'thickness_cm']
        for dim in dimensions:
            try:
                value = float(slice_data.get(dim, 0))
                if value <= 0:
                    errors.append(f"{dim.replace('_cm', '')} يجب أن يكون أكبر من صفر")
            except (ValueError, TypeError):
                errors.append(f"{dim.replace('_cm', '')} يجب أن يكون رقماً صحيحاً")
                
        # التحقق من نوع الجرانيت
        if not slice_data.get('granite_type_id'):
            errors.append("نوع الجرانيت مطلوب")
            
        # التحقق من البلوك
        if not slice_data.get('block_id'):
            errors.append("البلوك مطلوب")
            
        # التحقق من السعر
        if slice_data.get('price_per_sqm'):
            try:
                price = float(slice_data['price_per_sqm'])
                if price < 0:
                    errors.append("السعر يجب أن يكون أكبر من أو يساوي صفر")
            except (ValueError, TypeError):
                errors.append("السعر يجب أن يكون رقماً صحيحاً")
                
        return errors
