#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل برنامج الحسن ستون
Run Al-Hassan Stone ERP
"""

import sys
import os

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    
    import config
    from src.database.sqlite_manager import SQLiteManager
    from src.models.user import User
    from src.ui.login_window import LoginWindow
    from src.ui.main_window import MainWindow
    from src.utils.logger import setup_logger
    
    def main():
        """الدالة الرئيسية"""
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName(config.APP_NAME)
        app.setApplicationVersion(config.APP_VERSION)
        
        # إعداد الخط العربي
        font = QFont(config.UI_CONFIG['font_family'], config.UI_CONFIG['font_size'])
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد نظام السجلات
        logger = setup_logger()
        logger.info(f"بدء تشغيل {config.APP_NAME}")
        
        try:
            # إعداد قاعدة البيانات
            db_manager = SQLiteManager()
            if not db_manager.test_connection():
                QMessageBox.critical(None, "خطأ", "لا يمكن الاتصال بقاعدة البيانات")
                return 1
                
            if not db_manager.setup_database():
                QMessageBox.critical(None, "خطأ", "فشل في إعداد قاعدة البيانات")
                return 1
                
            # نافذة تسجيل الدخول
            login_window = LoginWindow(db_manager)
            
            if login_window.exec_() == login_window.Accepted:
                # الحصول على بيانات المستخدم من نافذة تسجيل الدخول
                user_model = User(db_manager)
                user_data = user_model.authenticate("admin", "admin123")
                
                if user_data:
                    # النافذة الرئيسية
                    main_window = MainWindow(db_manager, user_data)
                    main_window.show()
                    
                    return app.exec_()
                else:
                    QMessageBox.critical(None, "خطأ", "فشل في تسجيل الدخول")
                    return 1
            else:
                return 0
                
        except Exception as e:
            QMessageBox.critical(None, "خطأ في التطبيق", f"حدث خطأ:\n{str(e)}")
            logger.error(f"خطأ في التطبيق: {str(e)}")
            return 1
            
    if __name__ == "__main__":
        sys.exit(main())
        
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {str(e)}")
    print("يرجى تثبيت المكتبات المطلوبة:")
    print("pip install PyQt5")
    sys.exit(1)
