# -*- coding: utf-8 -*-
"""
مدير قاعدة بيانات SQLite (للاختبار)
SQLite Database Manager (for testing)
"""

import sqlite3
import os
import logging
from datetime import datetime
from src.utils.logger import log_database_operation, log_error

class SQLiteManager:
    """مدير قاعدة بيانات SQLite"""
    
    def __init__(self, db_path="granite_erp.db"):
        self.db_path = db_path
        self.connection = None
        self.logger = logging.getLogger('SQLiteManager')
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للوصول للأعمدة بالاسم
            return True
        except Exception as e:
            log_error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            return False
            
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
            
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            if self.connect():
                cursor = self.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                self.disconnect()
                return True
            return False
        except Exception as e:
            log_error(f"فشل اختبار الاتصال: {str(e)}")
            return False
            
    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام SQL"""
        try:
            if not self.connection:
                if not self.connect():
                    return None
                    
            cursor = self.connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            if fetch:
                if fetch == 'one':
                    result = cursor.fetchone()
                elif fetch == 'all':
                    result = cursor.fetchall()
                else:
                    result = cursor.fetchmany(fetch)
            else:
                result = cursor.rowcount
                
            cursor.close()
            return result
            
        except Exception as e:
            log_error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            if self.connection:
                self.connection.rollback()
            return None
            
    def execute_transaction(self, queries_with_params):
        """تنفيذ مجموعة استعلامات في معاملة واحدة"""
        try:
            if not self.connection:
                if not self.connect():
                    return False
                    
            cursor = self.connection.cursor()
            
            for query, params in queries_with_params:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                    
            self.connection.commit()
            cursor.close()
            log_database_operation("TRANSACTION", "MULTIPLE", f"تم تنفيذ {len(queries_with_params)} استعلام")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تنفيذ المعاملة: {str(e)}")
            if self.connection:
                self.connection.rollback()
            return False
            
    def create_tables(self):
        """إنشاء الجداول الأساسية"""
        tables_sql = [
            # جدول المستخدمين
            """
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                created_by INTEGER,
                notes TEXT
            )
            """,
            
            # جدول أنواع الجرانيت
            """
            CREATE TABLE IF NOT EXISTS granite_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                price_per_sqm REAL,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # جدول الجرارات
            """
            CREATE TABLE IF NOT EXISTS trucks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                truck_number TEXT NOT NULL,
                weight_tons REAL NOT NULL,
                price_per_ton REAL NOT NULL,
                total_cost REAL NOT NULL,
                arrival_date DATETIME NOT NULL,
                supplier TEXT,
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # جدول البلوكات
            """
            CREATE TABLE IF NOT EXISTS blocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                block_number TEXT NOT NULL,
                truck_id INTEGER NOT NULL,
                granite_type_id INTEGER NOT NULL,
                length_cm REAL NOT NULL,
                width_cm REAL NOT NULL,
                height_cm REAL NOT NULL,
                weight_kg REAL,
                status TEXT DEFAULT 'available',
                location TEXT,
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (truck_id) REFERENCES trucks(id),
                FOREIGN KEY (granite_type_id) REFERENCES granite_types(id)
            )
            """,
            
            # جدول الشرائح
            """
            CREATE TABLE IF NOT EXISTS slices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                slice_number TEXT NOT NULL,
                block_id INTEGER NOT NULL,
                granite_type_id INTEGER NOT NULL,
                length_cm REAL NOT NULL,
                width_cm REAL NOT NULL,
                thickness_cm REAL NOT NULL,
                quality_grade TEXT DEFAULT 'A',
                status TEXT DEFAULT 'available',
                location TEXT,
                price_per_sqm REAL,
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (block_id) REFERENCES blocks(id),
                FOREIGN KEY (granite_type_id) REFERENCES granite_types(id)
            )
            """,
            
            # جدول العملاء
            """
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                email TEXT,
                tax_number TEXT,
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # جدول فواتير المبيعات
            """
            CREATE TABLE IF NOT EXISTS sales_invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                invoice_date DATETIME NOT NULL,
                due_date DATETIME,
                subtotal REAL NOT NULL,
                tax_amount REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )
            """,
            
            # جدول المصاريف
            """
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                expense_date DATETIME NOT NULL,
                expense_type TEXT NOT NULL,
                description TEXT NOT NULL,
                amount REAL NOT NULL,
                payment_method TEXT,
                reference_number TEXT,
                supplier TEXT,
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # جدول سجل المستخدمين
            """
            CREATE TABLE IF NOT EXISTS user_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
            """
        ]
        
        try:
            if not self.connect():
                return False
                
            for sql in tables_sql:
                self.execute_query(sql)
                
            self.connection.commit()
            return True
            
        except Exception as e:
            log_error(f"خطأ في إنشاء الجداول: {str(e)}")
            return False
            
    def insert_initial_data(self):
        """إدراج البيانات الأساسية"""
        try:
            # إدراج أنواع الجرانيت
            granite_types = [
                ('جرانيت أحمر أسوان', 'جرانيت أحمر طبيعي من أسوان', 150.00),
                ('جرانيت رمادي', 'جرانيت رمادي عالي الجودة', 120.00),
                ('جرانيت أسود', 'جرانيت أسود لامع', 180.00),
                ('جرانيت وردي', 'جرانيت وردي طبيعي', 160.00),
                ('جرانيت أبيض', 'جرانيت أبيض نقي', 140.00)
            ]
            
            for name, desc, price in granite_types:
                query = """
                INSERT OR IGNORE INTO granite_types (name, description, price_per_sqm)
                VALUES (?, ?, ?)
                """
                self.execute_query(query, (name, desc, price))
            
            # إدراج مستخدم المدير الافتراضي (تشفير مبسط للاختبار)
            import hashlib
            password = "admin123"
            password_hash = hashlib.sha256(password.encode()).hexdigest()

            admin_query = """
            INSERT OR IGNORE INTO users (username, password_hash, full_name, role)
            VALUES ('admin', ?, 'مدير النظام', 'admin')
            """
            self.execute_query(admin_query, (password_hash,))
            
            # تأكيد التغييرات
            if self.connection:
                self.connection.commit()
                
            log_database_operation("INSERT", "INITIAL_DATA", "تم إدراج البيانات الأساسية")
            return True
            
        except Exception as e:
            log_error(f"خطأ في إدراج البيانات الأساسية: {str(e)}")
            if self.connection:
                self.connection.rollback()
            return False
            
    def setup_database(self):
        """إعداد قاعدة البيانات الكاملة"""
        try:
            if self.create_tables():
                if self.insert_initial_data():
                    return True
            return False
        except Exception as e:
            log_error(f"خطأ في إعداد قاعدة البيانات: {str(e)}")
            return False
