# -*- coding: utf-8 -*-
"""
نموذج الطلبات والحجوزات
Orders Model
"""

from datetime import datetime, timedelta
from src.utils.logger import log_user_action, log_database_operation, log_error

class Order:
    """نموذج الطلبات والحجوزات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def create_order(self, order_data, user_id):
        """إنشاء طلب جديد"""
        try:
            # إنشاء رقم طلب تلقائي
            order_number = self.generate_order_number()
            
            query = """
            INSERT INTO orders (order_number, customer_id, order_date, delivery_date,
                              granite_type_id, length_cm, width_cm, thickness_cm,
                              quantity, unit_price, total_amount, status, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                order_number,
                order_data['customer_id'],
                order_data['order_date'],
                order_data.get('delivery_date'),
                order_data['granite_type_id'],
                order_data['length_cm'],
                order_data['width_cm'],
                order_data['thickness_cm'],
                order_data['quantity'],
                order_data.get('unit_price'),
                order_data.get('total_amount'),
                order_data.get('status', 'pending'),
                order_data.get('notes', ''),
                user_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                order_id = self.db_manager.connection.lastrowid
                
                log_user_action(user_id, "CREATE_ORDER", f"إنشاء طلب رقم: {order_number}")
                log_database_operation("INSERT", "orders", f"طلب ID: {order_id}")
                
                return True, order_id, f"تم إنشاء الطلب رقم {order_number} بنجاح"
            else:
                return False, None, "فشل في إنشاء الطلب"
                
        except Exception as e:
            log_error(f"خطأ في إنشاء الطلب: {str(e)}")
            return False, None, str(e)
            
    def generate_order_number(self):
        """توليد رقم طلب تلقائي"""
        try:
            # الحصول على آخر رقم طلب
            query = """
            SELECT order_number FROM orders 
            ORDER BY id DESC LIMIT 1
            """
            result = self.db_manager.execute_query(query, fetch='one')
            
            if result:
                last_number = result[0]
                # استخراج الرقم من آخر طلب
                if last_number.startswith('ORD-'):
                    number_part = int(last_number.split('-')[1]) + 1
                else:
                    number_part = 1
            else:
                number_part = 1
                
            return f"ORD-{number_part:06d}"
            
        except Exception as e:
            log_error(f"خطأ في توليد رقم الطلب: {str(e)}")
            return f"ORD-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
    def get_all_orders(self, limit=None):
        """الحصول على جميع الطلبات"""
        try:
            query = """
            SELECT o.id, o.order_number, o.order_date, o.delivery_date,
                   o.length_cm, o.width_cm, o.thickness_cm, o.quantity,
                   o.unit_price, o.total_amount, o.status, o.notes,
                   o.created_at, c.name as customer_name, gt.name as granite_type_name,
                   u.full_name as created_by_name,
                   (o.length_cm * o.width_cm * o.quantity / 10000) as total_area
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            LEFT JOIN granite_types gt ON o.granite_type_id = gt.id
            LEFT JOIN users u ON o.created_by = u.id
            ORDER BY o.created_at DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
                
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الطلبات: {str(e)}")
            return []
            
    def get_order_by_id(self, order_id):
        """الحصول على طلب بالمعرف"""
        try:
            query = """
            SELECT o.*, c.name as customer_name, c.phone as customer_phone,
                   gt.name as granite_type_name, u.full_name as created_by_name,
                   (o.length_cm * o.width_cm * o.quantity / 10000) as total_area
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            LEFT JOIN granite_types gt ON o.granite_type_id = gt.id
            LEFT JOIN users u ON o.created_by = u.id
            WHERE o.id = ?
            """
            
            return self.db_manager.execute_query(query, (order_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب الطلب: {str(e)}")
            return None
            
    def update_order_status(self, order_id, new_status, user_id):
        """تحديث حالة الطلب"""
        try:
            query = "UPDATE orders SET status = ? WHERE id = ?"
            result = self.db_manager.execute_query(query, (new_status, order_id))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_ORDER_STATUS", 
                              f"تحديث حالة الطلب ID: {order_id} إلى {new_status}")
                log_database_operation("UPDATE", "orders", f"طلب ID: {order_id}")
                
                return True, "تم تحديث حالة الطلب بنجاح"
            else:
                return False, "فشل في تحديث حالة الطلب"
                
        except Exception as e:
            log_error(f"خطأ في تحديث حالة الطلب: {str(e)}")
            return False, str(e)
            
    def update_order(self, order_id, order_data, user_id):
        """تحديث بيانات الطلب"""
        try:
            query = """
            UPDATE orders 
            SET customer_id = ?, order_date = ?, delivery_date = ?,
                granite_type_id = ?, length_cm = ?, width_cm = ?, thickness_cm = ?,
                quantity = ?, unit_price = ?, total_amount = ?, notes = ?
            WHERE id = ?
            """
            
            params = (
                order_data['customer_id'],
                order_data['order_date'],
                order_data.get('delivery_date'),
                order_data['granite_type_id'],
                order_data['length_cm'],
                order_data['width_cm'],
                order_data['thickness_cm'],
                order_data['quantity'],
                order_data.get('unit_price'),
                order_data.get('total_amount'),
                order_data.get('notes', ''),
                order_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_ORDER", f"تحديث طلب ID: {order_id}")
                log_database_operation("UPDATE", "orders", f"طلب ID: {order_id}")
                
                return True, "تم تحديث الطلب بنجاح"
            else:
                return False, "فشل في تحديث الطلب"
                
        except Exception as e:
            log_error(f"خطأ في تحديث الطلب: {str(e)}")
            return False, str(e)
            
    def delete_order(self, order_id, user_id):
        """حذف الطلب"""
        try:
            query = "DELETE FROM orders WHERE id = ?"
            result = self.db_manager.execute_query(query, (order_id,))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "DELETE_ORDER", f"حذف طلب ID: {order_id}")
                log_database_operation("DELETE", "orders", f"طلب ID: {order_id}")
                
                return True, "تم حذف الطلب بنجاح"
            else:
                return False, "فشل في حذف الطلب"
                
        except Exception as e:
            log_error(f"خطأ في حذف الطلب: {str(e)}")
            return False, str(e)
            
    def get_orders_by_status(self, status):
        """الحصول على الطلبات حسب الحالة"""
        try:
            query = """
            SELECT o.id, o.order_number, o.order_date, o.delivery_date,
                   o.quantity, o.total_amount, c.name as customer_name,
                   gt.name as granite_type_name
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            LEFT JOIN granite_types gt ON o.granite_type_id = gt.id
            WHERE o.status = ?
            ORDER BY o.order_date DESC
            """
            
            return self.db_manager.execute_query(query, (status,), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الطلبات حسب الحالة: {str(e)}")
            return []
            
    def get_overdue_orders(self):
        """الحصول على الطلبات المتأخرة"""
        try:
            query = """
            SELECT o.id, o.order_number, o.order_date, o.delivery_date,
                   o.quantity, o.total_amount, c.name as customer_name,
                   gt.name as granite_type_name,
                   julianday('now') - julianday(o.delivery_date) as days_overdue
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            LEFT JOIN granite_types gt ON o.granite_type_id = gt.id
            WHERE o.status IN ('pending', 'in_progress') 
            AND o.delivery_date < date('now')
            ORDER BY days_overdue DESC
            """
            
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الطلبات المتأخرة: {str(e)}")
            return []
            
    def search_orders(self, search_term):
        """البحث في الطلبات"""
        try:
            query = """
            SELECT o.id, o.order_number, o.order_date, o.delivery_date,
                   o.quantity, o.total_amount, o.status, c.name as customer_name,
                   gt.name as granite_type_name
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            LEFT JOIN granite_types gt ON o.granite_type_id = gt.id
            WHERE o.order_number LIKE ? OR c.name LIKE ? OR o.notes LIKE ?
               OR gt.name LIKE ?
            ORDER BY o.created_at DESC
            """
            
            search_pattern = f"%{search_term}%"
            params = (search_pattern, search_pattern, search_pattern, search_pattern)
            
            return self.db_manager.execute_query(query, params, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في البحث في الطلبات: {str(e)}")
            return []
            
    def get_order_statistics(self):
        """الحصول على إحصائيات الطلبات"""
        try:
            stats = {}
            
            # إجمالي عدد الطلبات
            query = "SELECT COUNT(*) FROM orders"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_orders'] = result[0] if result else 0
            
            # الطلبات المعلقة
            query = "SELECT COUNT(*) FROM orders WHERE status = 'pending'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['pending_orders'] = result[0] if result else 0
            
            # الطلبات قيد التنفيذ
            query = "SELECT COUNT(*) FROM orders WHERE status = 'in_progress'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['in_progress_orders'] = result[0] if result else 0
            
            # الطلبات المكتملة
            query = "SELECT COUNT(*) FROM orders WHERE status = 'completed'"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['completed_orders'] = result[0] if result else 0
            
            # الطلبات المتأخرة
            query = """
            SELECT COUNT(*) FROM orders 
            WHERE status IN ('pending', 'in_progress') 
            AND delivery_date < date('now')
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['overdue_orders'] = result[0] if result else 0
            
            # إجمالي قيمة الطلبات
            query = "SELECT SUM(total_amount) FROM orders"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_value'] = result[0] if result and result[0] else 0
            
            # الطلبات هذا الشهر
            query = """
            SELECT COUNT(*) FROM orders 
            WHERE strftime('%Y-%m', order_date) = strftime('%Y-%m', 'now')
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['orders_this_month'] = result[0] if result else 0
            
            return stats
            
        except Exception as e:
            log_error(f"خطأ في جلب إحصائيات الطلبات: {str(e)}")
            return {}
            
    def validate_order_data(self, order_data):
        """التحقق من صحة بيانات الطلب"""
        errors = []
        
        # التحقق من العميل
        if not order_data.get('customer_id'):
            errors.append("العميل مطلوب")
            
        # التحقق من تاريخ الطلب
        if not order_data.get('order_date'):
            errors.append("تاريخ الطلب مطلوب")
            
        # التحقق من نوع الجرانيت
        if not order_data.get('granite_type_id'):
            errors.append("نوع الجرانيت مطلوب")
            
        # التحقق من الأبعاد
        dimensions = ['length_cm', 'width_cm', 'thickness_cm']
        for dim in dimensions:
            try:
                value = float(order_data.get(dim, 0))
                if value <= 0:
                    errors.append(f"{dim.replace('_cm', '')} يجب أن يكون أكبر من صفر")
            except (ValueError, TypeError):
                errors.append(f"{dim.replace('_cm', '')} يجب أن يكون رقماً صحيحاً")
                
        # التحقق من الكمية
        try:
            quantity = int(order_data.get('quantity', 0))
            if quantity <= 0:
                errors.append("الكمية يجب أن تكون أكبر من صفر")
        except (ValueError, TypeError):
            errors.append("الكمية يجب أن تكون رقماً صحيحاً")
            
        # التحقق من السعر إذا كان موجوداً
        if order_data.get('unit_price'):
            try:
                price = float(order_data['unit_price'])
                if price < 0:
                    errors.append("السعر يجب أن يكون أكبر من أو يساوي صفر")
            except (ValueError, TypeError):
                errors.append("السعر يجب أن يكون رقماً صحيحاً")
                
        return errors
