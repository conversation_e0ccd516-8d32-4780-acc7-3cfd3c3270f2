# -*- coding: utf-8 -*-
"""
نافذة إدارة أنواع الجرانيت
Granite Types Management Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QDoubleSpinBox, QPushButton,
                            QMessageBox, QLabel, QGroupBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

import config
from src.models.granite_type import GraniteType
from src.utils.font_utils import apply_font_settings, get_responsive_style

class GraniteTypesDialog(QDialog):
    """نافذة إدارة أنواع الجرانيت"""
    
    def __init__(self, parent=None, database_manager=None, user_data=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.user_data = user_data

        # إنشاء مدير قاعدة البيانات إذا لم يتم تمريره
        if not self.database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()

        self.granite_type_model = GraniteType(self.database_manager)
        
        self.setup_ui()
        apply_font_settings(self)
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة أنواع الجرانيت")
        self.setFixedSize(800, 600)
        self.setModal(True)
        
        layout = QHBoxLayout()
        
        # الجانب الأيسر - قائمة الأنواع
        left_layout = QVBoxLayout()
        
        # عنوان القائمة
        list_label = QLabel("أنواع الجرانيت المتاحة")
        list_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        left_layout.addWidget(list_label)
        
        # جدول الأنواع
        self.types_table = QTableWidget()
        self.types_table.setColumnCount(4)
        
        headers = ["المعرف", "الاسم", "السعر الافتراضي", "الوصف"]
        self.types_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.types_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        header = self.types_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        # أحداث الجدول
        self.types_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        
        left_layout.addWidget(self.types_table)
        
        # أزرار إدارة القائمة
        list_buttons_layout = QHBoxLayout()
        
        self.delete_btn = QPushButton("حذف النوع المحدد")
        self.delete_btn.clicked.connect(self.delete_type)
        self.delete_btn.setEnabled(False)
        
        list_buttons_layout.addWidget(self.delete_btn)
        list_buttons_layout.addStretch()
        
        left_layout.addLayout(list_buttons_layout)
        
        # الجانب الأيمن - نموذج الإدخال
        right_layout = QVBoxLayout()
        
        # نموذج إضافة/تعديل
        form_group = QGroupBox("إضافة/تعديل نوع جرانيت")
        form_layout = QFormLayout()
        
        # اسم النوع
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم نوع الجرانيت (مطلوب)")
        form_layout.addRow("الاسم:", self.name_input)
        
        # السعر الافتراضي
        self.default_price_input = QDoubleSpinBox()
        self.default_price_input.setRange(0, 10000)
        self.default_price_input.setDecimals(2)
        self.default_price_input.setSuffix(" جنيه/م²")
        form_layout.addRow("السعر الافتراضي:", self.default_price_input)
        
        # الوصف
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(100)
        self.description_input.setPlaceholderText("وصف نوع الجرانيت...")
        form_layout.addRow("الوصف:", self.description_input)
        
        form_group.setLayout(form_layout)
        right_layout.addWidget(form_group)
        
        # أزرار النموذج
        form_buttons_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("مسح النموذج")
        self.clear_btn.clicked.connect(self.clear_form)
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_type)
        
        form_buttons_layout.addWidget(self.clear_btn)
        form_buttons_layout.addWidget(self.save_btn)
        
        right_layout.addLayout(form_buttons_layout)
        right_layout.addStretch()
        
        # إحصائيات
        stats_group = QGroupBox("إحصائيات")
        stats_layout = QVBoxLayout()
        
        self.total_types_label = QLabel("إجمالي الأنواع: 0")
        self.avg_price_label = QLabel("متوسط السعر: 0.00 جنيه/م²")
        
        stats_layout.addWidget(self.total_types_label)
        stats_layout.addWidget(self.avg_price_label)
        
        stats_group.setLayout(stats_layout)
        right_layout.addWidget(stats_group)
        
        # تجميع الجوانب
        layout.addLayout(left_layout, 2)
        layout.addLayout(right_layout, 1)
        
        # أزرار النافذة
        main_layout = QVBoxLayout()
        main_layout.addLayout(layout)
        
        window_buttons_layout = QHBoxLayout()
        
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.clicked.connect(self.accept)
        
        window_buttons_layout.addStretch()
        window_buttons_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(window_buttons_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
        # متغيرات التحكم
        self.current_type_id = None
        
    def load_data(self):
        """تحميل البيانات"""
        types = self.granite_type_model.get_all_granite_types()
        self.populate_table(types)
        self.update_statistics(types)
        
    def populate_table(self, types):
        """ملء الجدول بالبيانات"""
        self.types_table.setRowCount(len(types))
        
        for row, granite_type in enumerate(types):
            items = [
                str(granite_type[0]),  # ID
                str(granite_type[1]),  # name
                f"{granite_type[2]:,.2f}" if granite_type[2] else "0.00",  # default_price
                str(granite_type[3])[:50] + "..." if granite_type[3] and len(granite_type[3]) > 50 else str(granite_type[3]) if granite_type[3] else ""  # description
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 2]:  # الأعمدة الرقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.types_table.setItem(row, col, item)
                
    def update_statistics(self, types):
        """تحديث الإحصائيات"""
        total_types = len(types)
        self.total_types_label.setText(f"إجمالي الأنواع: {total_types}")
        
        if total_types > 0:
            avg_price = sum(t[2] for t in types if t[2]) / total_types
            self.avg_price_label.setText(f"متوسط السعر: {avg_price:,.2f} جنيه/م²")
        else:
            self.avg_price_label.setText("متوسط السعر: 0.00 جنيه/م²")
            
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        selected_rows = self.types_table.selectionModel().selectedRows()
        
        if selected_rows:
            self.delete_btn.setEnabled(True)
            
            # تحميل البيانات في النموذج للتعديل
            row = selected_rows[0].row()
            type_id = int(self.types_table.item(row, 0).text())
            self.load_type_for_editing(type_id)
        else:
            self.delete_btn.setEnabled(False)
            self.clear_form()
            
    def load_type_for_editing(self, type_id):
        """تحميل نوع الجرانيت للتعديل"""
        granite_type = self.granite_type_model.get_granite_type_by_id(type_id)
        if granite_type:
            self.current_type_id = type_id
            self.name_input.setText(granite_type[1])
            self.default_price_input.setValue(granite_type[2] if granite_type[2] else 0)
            self.description_input.setPlainText(granite_type[3] if granite_type[3] else "")
            
            # تغيير نص الزر
            self.save_btn.setText("تحديث")
            
    def clear_form(self):
        """مسح النموذج"""
        self.current_type_id = None
        self.name_input.clear()
        self.default_price_input.setValue(0)
        self.description_input.clear()
        
        # إعادة تعيين نص الزر
        self.save_btn.setText("حفظ")
        
        # إلغاء التحديد
        self.types_table.clearSelection()
        
    def save_type(self):
        """حفظ نوع الجرانيت"""
        # جمع البيانات
        type_data = {
            'name': self.name_input.text().strip(),
            'default_price': self.default_price_input.value(),
            'description': self.description_input.toPlainText().strip()
        }
        
        # التحقق من صحة البيانات
        errors = self.granite_type_model.validate_granite_type_data(type_data)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
            
        # حفظ البيانات
        try:
            if self.current_type_id:
                # تحديث
                success, message = self.granite_type_model.update_granite_type(
                    self.current_type_id, type_data, self.user_data['id']
                )
            else:
                # إضافة جديد
                success, type_id, message = self.granite_type_model.add_granite_type(
                    type_data, self.user_data['id']
                )
                
            if success:
                QMessageBox.information(self, "نجح", message)
                self.load_data()
                self.clear_form()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات:\n{str(e)}")
            
    def delete_type(self):
        """حذف نوع الجرانيت المحدد"""
        current_row = self.types_table.currentRow()
        if current_row >= 0:
            type_id = int(self.types_table.item(current_row, 0).text())
            type_name = self.types_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف نوع الجرانيت:\n{type_name}؟\n\nتحذير: سيؤثر هذا على جميع البيانات المرتبطة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    success, message = self.granite_type_model.delete_granite_type(
                        type_id, self.user_data['id']
                    )
                    
                    if success:
                        QMessageBox.information(self, "نجح", message)
                        self.load_data()
                        self.clear_form()
                    else:
                        QMessageBox.warning(self, "خطأ", message)
                        
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في حذف البيانات:\n{str(e)}")
                    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QPushButton:disabled {{
                background-color: #BDC3C7;
                color: #7F8C8D;
            }}
            
            QLineEdit, QTextEdit, QDoubleSpinBox {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
