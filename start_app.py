#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل برنامج الحسن ستون - نظام إدارة مصنع الجرانيت
Al-Hassan Stone ERP System Launcher
"""

import sys
import os
import traceback

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_requirements():
    """التحقق من المتطلبات"""
    missing_modules = []
    
    try:
        import PyQt5
    except ImportError:
        missing_modules.append("PyQt5")
        
    try:
        import sqlite3
    except ImportError:
        missing_modules.append("sqlite3")
        
    if missing_modules:
        print("❌ المكتبات التالية مفقودة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\nيرجى تثبيت المكتبات المطلوبة:")
        print("pip install PyQt5")
        return False
        
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏗️  برنامج الحسن ستون - نظام إدارة مصنع الجرانيت")
    print("   Al-Hassan Stone ERP System")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("\nاضغط Enter للخروج...")
        return 1
        
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QFont, QPixmap
        
        import config
        from src.database.sqlite_manager import SQLiteManager
        from src.models.user import User
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        from src.utils.logger import setup_logger
        
        print("✅ تم تحميل جميع المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName(config.APP_NAME)
        app.setApplicationVersion(config.APP_VERSION)
        app.setOrganizationName(config.COMPANY_NAME)
        
        # إعداد الخط العربي
        font = QFont(config.UI_CONFIG['font_family'], config.UI_CONFIG['font_size'])
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إعداد التطبيق بنجاح")
        
        # إعداد نظام السجلات
        logger = setup_logger()
        logger.info(f"بدء تشغيل {config.APP_NAME} الإصدار {config.APP_VERSION}")
        
        print("✅ تم إعداد نظام السجلات")
        
        # شاشة البداية
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        splash.show()
        
        splash.showMessage(
            f"مرحباً بك في {config.APP_NAME}\n"
            f"الإصدار {config.APP_VERSION}\n"
            f"جاري تحميل النظام...",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )
        
        app.processEvents()
        
        # إعداد قاعدة البيانات
        print("🔄 جاري إعداد قاعدة البيانات...")
        db_manager = SQLiteManager()
        
        if not db_manager.test_connection():
            splash.close()
            QMessageBox.critical(None, "خطأ", "لا يمكن الاتصال بقاعدة البيانات")
            return 1
            
        if not db_manager.setup_database():
            splash.close()
            QMessageBox.critical(None, "خطأ", "فشل في إعداد قاعدة البيانات")
            return 1
            
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        
        # إغلاق شاشة البداية
        QTimer.singleShot(2000, splash.close)
        
        # نافذة تسجيل الدخول
        print("🔐 عرض نافذة تسجيل الدخول...")
        login_window = LoginWindow(db_manager)
        
        if login_window.exec_() == login_window.Accepted:
            # الحصول على بيانات المستخدم
            user_model = User(db_manager)
            user_data = user_model.authenticate("admin", "admin123")
            
            if user_data:
                print(f"✅ تم تسجيل الدخول بنجاح - مرحباً {user_data['full_name']}")
                
                # النافذة الرئيسية
                main_window = MainWindow(db_manager, user_data)
                main_window.show()
                
                print("🚀 تم تشغيل البرنامج بنجاح!")
                print("\nمعلومات تسجيل الدخول الافتراضية:")
                print("اسم المستخدم: admin")
                print("كلمة المرور: admin123")
                print("\n⚠️  يُنصح بتغيير كلمة المرور بعد أول تسجيل دخول")
                
                return app.exec_()
            else:
                QMessageBox.critical(None, "خطأ", "فشل في تسجيل الدخول")
                return 1
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
    except Exception as e:
        error_msg = f"حدث خطأ في التطبيق:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"
        print(f"❌ خطأ في التطبيق: {str(e)}")
        print(traceback.format_exc())
        
        try:
            QMessageBox.critical(None, "خطأ في التطبيق", error_msg)
        except:
            print("لا يمكن عرض رسالة الخطأ")
            
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        print(f"\n{'='*60}")
        if exit_code == 0:
            print("✅ تم إنهاء البرنامج بنجاح")
        else:
            print("❌ تم إنهاء البرنامج مع وجود أخطاء")
        print("شكراً لاستخدام برنامج الحسن ستون")
        print("="*60)
        
        input("\nاضغط Enter للخروج...")
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n\n❌ تم إيقاف البرنامج بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ خطأ غير متوقع: {str(e)}")
        sys.exit(1)
