# -*- coding: utf-8 -*-
"""
نموذج عمليات النشر
Cutting Operations Model
"""

from datetime import datetime
from src.utils.logger import log_user_action, log_database_operation, log_error

class CuttingOperation:
    """نموذج عمليات النشر"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def add_cutting_operation(self, operation_data, user_id):
        """إضافة عملية نشر جديدة"""
        try:
            # إضافة عملية النشر
            query = """
            INSERT INTO cutting_operations (block_id, operation_date, slices_produced,
                                          waste_percentage, operator_name, machine_used,
                                          notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                operation_data['block_id'],
                operation_data['operation_date'],
                operation_data['slices_produced'],
                operation_data.get('waste_percentage', 0),
                operation_data.get('operator_name', ''),
                operation_data.get('machine_used', ''),
                operation_data.get('notes', ''),
                user_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                # الحصول على ID العملية الجديدة
                operation_id = self.db_manager.connection.lastrowid
                
                # تحديث حالة البلوك إلى "قيد النشر"
                self.update_block_status(operation_data['block_id'], 'cutting')
                
                log_user_action(user_id, "ADD_CUTTING_OPERATION", 
                              f"إضافة عملية نشر للبلوك ID: {operation_data['block_id']}")
                log_database_operation("INSERT", "cutting_operations", f"عملية ID: {operation_id}")
                
                return True, operation_id, "تم إضافة عملية النشر بنجاح"
            else:
                return False, None, "فشل في إضافة عملية النشر"
                
        except Exception as e:
            log_error(f"خطأ في إضافة عملية النشر: {str(e)}")
            return False, None, str(e)
            
    def get_all_cutting_operations(self, limit=None):
        """الحصول على جميع عمليات النشر"""
        try:
            query = """
            SELECT co.id, co.operation_date, co.slices_produced, co.waste_percentage,
                   co.operator_name, co.machine_used, co.notes, co.created_at,
                   b.block_number, gt.name as granite_type_name,
                   u.full_name as created_by_name
            FROM cutting_operations co
            LEFT JOIN blocks b ON co.block_id = b.id
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            LEFT JOIN users u ON co.created_by = u.id
            ORDER BY co.operation_date DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
                
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب عمليات النشر: {str(e)}")
            return []
            
    def get_cutting_operations_by_block(self, block_id):
        """الحصول على عمليات النشر لبلوك معين"""
        try:
            query = """
            SELECT co.id, co.operation_date, co.slices_produced, co.waste_percentage,
                   co.operator_name, co.machine_used, co.notes, co.created_at,
                   u.full_name as created_by_name
            FROM cutting_operations co
            LEFT JOIN users u ON co.created_by = u.id
            WHERE co.block_id = ?
            ORDER BY co.operation_date DESC
            """
            
            return self.db_manager.execute_query(query, (block_id,), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب عمليات نشر البلوك: {str(e)}")
            return []
            
    def get_operation_by_id(self, operation_id):
        """الحصول على عملية نشر بالمعرف"""
        try:
            query = """
            SELECT co.*, b.block_number, gt.name as granite_type_name,
                   u.full_name as created_by_name
            FROM cutting_operations co
            LEFT JOIN blocks b ON co.block_id = b.id
            LEFT JOIN granite_types gt ON b.granite_type_id = gt.id
            LEFT JOIN users u ON co.created_by = u.id
            WHERE co.id = ?
            """
            
            return self.db_manager.execute_query(query, (operation_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب عملية النشر: {str(e)}")
            return None
            
    def update_cutting_operation(self, operation_id, operation_data, user_id):
        """تحديث عملية النشر"""
        try:
            query = """
            UPDATE cutting_operations 
            SET operation_date = ?, slices_produced = ?, waste_percentage = ?,
                operator_name = ?, machine_used = ?, notes = ?
            WHERE id = ?
            """
            
            params = (
                operation_data['operation_date'],
                operation_data['slices_produced'],
                operation_data.get('waste_percentage', 0),
                operation_data.get('operator_name', ''),
                operation_data.get('machine_used', ''),
                operation_data.get('notes', ''),
                operation_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_CUTTING_OPERATION", f"تحديث عملية نشر ID: {operation_id}")
                log_database_operation("UPDATE", "cutting_operations", f"عملية ID: {operation_id}")
                
                return True, "تم تحديث عملية النشر بنجاح"
            else:
                return False, "فشل في تحديث عملية النشر"
                
        except Exception as e:
            log_error(f"خطأ في تحديث عملية النشر: {str(e)}")
            return False, str(e)
            
    def delete_cutting_operation(self, operation_id, user_id):
        """حذف عملية النشر"""
        try:
            # الحصول على معرف البلوك أولاً
            operation = self.get_operation_by_id(operation_id)
            if not operation:
                return False, "عملية النشر غير موجودة"
                
            block_id = operation[1]  # block_id
            
            # التحقق من وجود شرائح مرتبطة
            check_query = "SELECT COUNT(*) FROM slices WHERE cutting_operation_id = ?"
            result = self.db_manager.execute_query(check_query, (operation_id,), fetch='one')
            
            if result and result[0] > 0:
                return False, "لا يمكن حذف عملية النشر لوجود شرائح مرتبطة بها"
                
            # حذف عملية النشر
            delete_query = "DELETE FROM cutting_operations WHERE id = ?"
            result = self.db_manager.execute_query(delete_query, (operation_id,))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                # إعادة تعيين حالة البلوك إلى متاح
                self.update_block_status(block_id, 'available')
                
                log_user_action(user_id, "DELETE_CUTTING_OPERATION", f"حذف عملية نشر ID: {operation_id}")
                log_database_operation("DELETE", "cutting_operations", f"عملية ID: {operation_id}")
                
                return True, "تم حذف عملية النشر بنجاح"
            else:
                return False, "فشل في حذف عملية النشر"
                
        except Exception as e:
            log_error(f"خطأ في حذف عملية النشر: {str(e)}")
            return False, str(e)
            
    def update_block_status(self, block_id, status):
        """تحديث حالة البلوك"""
        try:
            query = "UPDATE blocks SET status = ? WHERE id = ?"
            self.db_manager.execute_query(query, (status, block_id))
            if self.db_manager.connection:
                self.db_manager.connection.commit()
        except Exception as e:
            log_error(f"خطأ في تحديث حالة البلوك: {str(e)}")
            
    def get_cutting_statistics(self):
        """الحصول على إحصائيات النشر"""
        try:
            stats = {}
            
            # إجمالي عمليات النشر
            query = "SELECT COUNT(*) FROM cutting_operations"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_operations'] = result[0] if result else 0
            
            # إجمالي الشرائح المنتجة
            query = "SELECT SUM(slices_produced) FROM cutting_operations"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_slices'] = result[0] if result and result[0] else 0
            
            # متوسط نسبة الفاقد
            query = "SELECT AVG(waste_percentage) FROM cutting_operations WHERE waste_percentage > 0"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['avg_waste_percentage'] = result[0] if result and result[0] else 0
            
            # عمليات النشر هذا الشهر
            query = """
            SELECT COUNT(*) FROM cutting_operations 
            WHERE strftime('%Y-%m', operation_date) = strftime('%Y-%m', 'now')
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['operations_this_month'] = result[0] if result else 0
            
            # الشرائح المنتجة هذا الشهر
            query = """
            SELECT SUM(slices_produced) FROM cutting_operations 
            WHERE strftime('%Y-%m', operation_date) = strftime('%Y-%m', 'now')
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['slices_this_month'] = result[0] if result and result[0] else 0
            
            return stats
            
        except Exception as e:
            log_error(f"خطأ في جلب إحصائيات النشر: {str(e)}")
            return {}
            
    def validate_cutting_operation_data(self, operation_data):
        """التحقق من صحة بيانات عملية النشر"""
        errors = []
        
        # التحقق من البلوك
        if not operation_data.get('block_id'):
            errors.append("البلوك مطلوب")
            
        # التحقق من التاريخ
        if not operation_data.get('operation_date'):
            errors.append("تاريخ العملية مطلوب")
            
        # التحقق من عدد الشرائح
        try:
            slices = int(operation_data.get('slices_produced', 0))
            if slices <= 0:
                errors.append("عدد الشرائح المنتجة يجب أن يكون أكبر من صفر")
        except (ValueError, TypeError):
            errors.append("عدد الشرائح المنتجة يجب أن يكون رقماً صحيحاً")
            
        # التحقق من نسبة الفاقد
        try:
            waste = float(operation_data.get('waste_percentage', 0))
            if waste < 0 or waste > 100:
                errors.append("نسبة الفاقد يجب أن تكون بين 0 و 100")
        except (ValueError, TypeError):
            errors.append("نسبة الفاقد يجب أن تكون رقماً صحيحاً")
            
        return errors
