#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الكامل مع جميع الواجهات الحديثة
Run Complete Modern System
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    init_files = [
        "src/__init__.py",
        "src/models/__init__.py", 
        "src/ui/__init__.py",
        "src/database/__init__.py",
        "src/utils/__init__.py"
    ]
    
    for init_file in init_files:
        if not os.path.exists(init_file):
            os.makedirs(os.path.dirname(init_file), exist_ok=True)
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('# -*- coding: utf-8 -*-\n')

def main():
    """الدالة الرئيسية لتشغيل النظام الكامل"""
    print("🚀 تشغيل نظام إدارة مصنع الجرانيت - النسخة الكاملة الحديثة")
    print("=" * 70)
    
    # إنشاء الملفات المفقودة
    create_missing_files()
    
    try:
        # استيراد المكونات الأساسية
        from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QFont, QPixmap, QPainter, QBrush, QColor
        
        print("✅ تم تحميل PyQt5")
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setApplicationName("نظام إدارة مصنع الجرانيت - الحسن ستون")
        app.setApplicationVersion("3.0 - Modern UI")
        
        # إعداد الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إعداد التطبيق")
        
        # شاشة البداية المحسنة
        splash_pixmap = QPixmap(500, 350)
        splash_pixmap.fill(QColor(44, 62, 80))  # لون خلفية أنيق
        
        # رسم محتوى شاشة البداية
        painter = QPainter(splash_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية متدرجة
        gradient = QBrush(QColor(26, 188, 156))
        painter.fillRect(0, 0, 500, 100, gradient)
        
        # النص
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(50, 50, "🏗️ نظام إدارة مصنع الجرانيت")
        
        painter.setFont(QFont("Arial", 12))
        painter.drawText(50, 80, "الحسن ستون - النسخة الحديثة 3.0")
        
        painter.end()
        
        splash = QSplashScreen(splash_pixmap)
        splash.setStyleSheet("""
            QSplashScreen {
                border-radius: 15px;
            }
        """)
        
        splash.show()
        splash.showMessage("🔄 جاري تحميل النظام الحديث...", 
                          Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        
        app.processEvents()
        
        # تأخير لعرض شاشة البداية
        import time
        time.sleep(2)
        
        print("✅ تم عرض شاشة البداية")
        
        # تحديث رسالة التحميل
        splash.showMessage("📊 تحميل لوحة التحكم المحسنة...", 
                          Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        time.sleep(1)
        
        splash.showMessage("💰 تحميل واجهة المبيعات الحديثة...", 
                          Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        time.sleep(1)
        
        splash.showMessage("🚛 تحميل واجهة الجرارات المطورة...", 
                          Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        time.sleep(1)
        
        splash.showMessage("⚡ تحميل واجهة التقطيع المحسنة...", 
                          Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        time.sleep(1)
        
        splash.showMessage("🎨 تطبيق الأنماط الحديثة...", 
                          Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        time.sleep(1)
        
        # محاولة تحميل نافذة تسجيل الدخول
        try:
            from src.ui.login_window import LoginWindow
            
            splash.showMessage("🔐 تحميل نافذة تسجيل الدخول...", 
                              Qt.AlignCenter | Qt.AlignBottom, Qt.white)
            app.processEvents()
            time.sleep(1)
            
            splash.close()
            
            # إنشاء وعرض نافذة تسجيل الدخول
            login_window = LoginWindow()
            
            if login_window.exec_() == login_window.Accepted:
                user_data = login_window.get_user_data()
                
                # تحميل النافذة الرئيسية الحديثة
                from src.ui.main_window import MainWindow
                
                main_window = MainWindow(user_data)
                main_window.show()
                
                print("✅ تم تشغيل النظام الكامل بنجاح")
                print(f"👤 مرحباً {user_data.get('full_name', 'المستخدم')}")
                print("\n🎨 الواجهات المتاحة:")
                print("  📊 لوحة التحكم المحسنة")
                print("  💰 المبيعات والفواتير الحديثة")
                print("  💸 المصاريف المطورة")
                print("  📋 الطلبات والحجوزات المحسنة")
                print("  📈 التقارير الحديثة")
                print("  🚛 الجرارات المطورة")
                print("  ⚡ التقطيع ودفعات النشر")
                print("  🧱 إدارة البلوكات (قيد التطوير)")
                print("  📦 إدارة المخزون (قيد التطوير)")
                print("  👥 إدارة المستخدمين")
                print("  ⚙️ إعدادات النظام")
                
                return app.exec_()
            else:
                print("❌ تم إلغاء تسجيل الدخول")
                return 1
                
        except Exception as login_error:
            print(f"⚠️ خطأ في نافذة تسجيل الدخول: {str(login_error)}")
            
            # تحميل النافذة الرئيسية مباشرة
            splash.showMessage("🏠 تحميل النافذة الرئيسية مباشرة...", 
                              Qt.AlignCenter | Qt.AlignBottom, Qt.white)
            app.processEvents()
            time.sleep(1)
            
            splash.close()
            
            try:
                from src.ui.main_window import MainWindow
                
                # بيانات مستخدم افتراضية محسنة
                user_data = {
                    'id': 1,
                    'username': 'admin',
                    'full_name': 'مدير النظام',
                    'role': 'admin',
                    'permissions': {
                        'dashboard': True,
                        'trucks_management': True,
                        'blocks_management': True,
                        'cutting_management': True,
                        'inventory_management': True,
                        'sales_management': True,
                        'expenses_management': True,
                        'orders_management': True,
                        'reports_access': True,
                        'users_management': True,
                        'system_settings': True
                    }
                }
                
                # إنشاء وعرض النافذة الرئيسية
                main_window = MainWindow(user_data)
                main_window.show()
                
                print("✅ تم عرض النافذة الرئيسية الحديثة")
                print("🎉 النظام الكامل يعمل بجميع الواجهات الحديثة!")
                
                # عرض رسالة ترحيب
                QMessageBox.information(None, "مرحباً بك", 
                                       "🎉 مرحباً بك في نظام إدارة مصنع الجرانيت\\n\\n"
                                       "🎨 النسخة الحديثة 3.0\\n"
                                       "✨ جميع الواجهات محدثة ومطورة\\n\\n"
                                       "المميزات الجديدة:\\n"
                                       "• واجهات حديثة مع ألوان متدرجة\\n"
                                       "• تصميم متجاوب وأنيق\\n"
                                       "• إحصائيات تفاعلية\\n"
                                       "• نظام دفعات النشر المتكامل\\n"
                                       "• خطوط متجاوبة تلقائياً")
                
                return app.exec_()
                
            except Exception as main_error:
                print(f"❌ خطأ في النافذة الرئيسية: {str(main_error)}")
                
                # عرض نافذة خطأ
                splash.close()
                QMessageBox.critical(None, "خطأ في التشغيل", 
                                   f"فشل في تشغيل النظام:\\n\\n"
                                   f"خطأ تسجيل الدخول: {str(login_error)}\\n"
                                   f"خطأ النافذة الرئيسية: {str(main_error)}\\n\\n"
                                   f"يرجى التحقق من:\\n"
                                   f"1. تثبيت PyQt5\\n"
                                   f"2. ملفات النظام\\n"
                                   f"3. إعدادات قاعدة البيانات")
                return 1
                
    except ImportError as import_error:
        print(f"❌ خطأ في الاستيراد: {str(import_error)}")
        print("\\n💡 حلول مقترحة:")
        print("1. تثبيت PyQt5: pip install PyQt5")
        print("2. تثبيت المتطلبات: pip install -r requirements.txt")
        print("3. التحقق من Python: python --version")
        return 1
        
    except Exception as general_error:
        print(f"❌ خطأ عام: {str(general_error)}")
        print("\\n💡 جرب:")
        print("1. إعادة تشغيل الكمبيوتر")
        print("2. تحديث Python")
        print("3. إعادة تثبيت المتطلبات")
        return 1

if __name__ == "__main__":
    sys.exit(main())
