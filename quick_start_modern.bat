@echo off
chcp 65001 >nul
title نظام إدارة مصنع الجرانيت - الواجهة الحديثة

echo.
echo ===============================================
echo 🎨 نظام إدارة مصنع الجرانيت - الواجهة الحديثة
echo    Al-Hassan Stone Modern Interface
echo ===============================================
echo.

echo 🔍 فحص متطلبات النظام...

:: التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python مثبت
)

:: التحقق من PyQt5
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo ❌ PyQt5 غير مثبت
    echo 🔧 جاري تثبيت PyQt5...
    pip install PyQt5
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyQt5
        echo 💡 جرب: pip install --user PyQt5
        pause
        exit /b 1
    )
) else (
    echo ✅ PyQt5 مثبت
)

echo.
echo 🎨 تحسين مكونات الواجهة...
python enhance_ui_components.py
if errorlevel 1 (
    echo ⚠️ تحذير: بعض التحسينات واجهت مشاكل
) else (
    echo ✅ تم تحسين جميع المكونات
)

echo.
echo 🚀 بدء تشغيل الواجهة الحديثة...
echo.

:: تشغيل النظام مع الواجهة الحديثة
python run_modern_system.py
if not errorlevel 1 goto :success

:: في حالة فشل الواجهة الحديثة، جرب العادية
echo.
echo 📋 محاولة تشغيل الواجهة العادية...
python main.py
if not errorlevel 1 goto :success

:: في حالة فشل كل شيء
echo.
echo ❌ فشل في تشغيل النظام
echo.
echo 💡 حلول مقترحة:
echo 1. تأكد من تثبيت Python و PyQt5
echo 2. شغل: pip install -r requirements.txt
echo 3. تحقق من ملفات النظام
echo 4. أعد تشغيل الكمبيوتر
echo.
goto :end

:success
echo.
echo 🎉 تم تشغيل النظام بنجاح!
echo 🎨 الواجهة الحديثة جاهزة للاستخدام
echo.

:end
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
