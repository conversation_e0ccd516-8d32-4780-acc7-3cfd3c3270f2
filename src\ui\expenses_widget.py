# -*- coding: utf-8 -*-
"""
واجهة إدارة المصاريف
Expenses Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QSplitter,
                            QGroupBox, QGridLayout, QComboBox, QSpinBox,
                            QTabWidget)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

import config
from src.models.expense import Expense
from src.utils.logger import log_user_action

class ExpensesWidget(QWidget):
    """واجهة إدارة المصاريف التشغيلية"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.expense_model = Expense(database_manager)
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان والأزرار
        self.create_header(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
    def create_header(self, layout):
        """إنشاء رأس الواجهة"""
        header_layout = QHBoxLayout()
        
        # العنوان
        title_label = QLabel("إدارة المصاريف التشغيلية")
        title_label.setFont(QFont(config.UI_CONFIG['font_family'], 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        # الأزرار
        self.add_expense_btn = QPushButton("إضافة مصروف")
        self.add_expense_btn.clicked.connect(self.add_expense)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_data)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.add_expense_btn)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
        
    def create_tabs(self, layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        
        # تبويب المصاريف
        self.expenses_tab = QWidget()
        self.create_expenses_tab()
        self.tabs.addTab(self.expenses_tab, "المصاريف")
        
        # تبويب الإحصائيات
        self.stats_tab = QWidget()
        self.create_stats_tab()
        self.tabs.addTab(self.stats_tab, "الإحصائيات")
        
        # تبويب التقارير
        self.reports_tab = QWidget()
        self.create_reports_tab()
        self.tabs.addTab(self.reports_tab, "التقارير")
        
        layout.addWidget(self.tabs)
        
    def create_expenses_tab(self):
        """إنشاء تبويب المصاريف"""
        layout = QVBoxLayout()
        
        # منطقة البحث والفلترة
        filter_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في المصاريف...")
        self.search_input.textChanged.connect(self.search_expenses)
        
        self.type_filter = QComboBox()
        self.type_filter.addItem("جميع الأنواع")
        self.type_filter.addItems(config.EXPENSE_TYPES)
        self.type_filter.currentTextChanged.connect(self.filter_by_type)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        
        filter_btn = QPushButton("فلترة بالتاريخ")
        filter_btn.clicked.connect(self.filter_by_date)
        
        filter_layout.addWidget(QLabel("البحث:"))
        filter_layout.addWidget(self.search_input)
        filter_layout.addWidget(QLabel("النوع:"))
        filter_layout.addWidget(self.type_filter)
        filter_layout.addWidget(QLabel("من:"))
        filter_layout.addWidget(self.start_date)
        filter_layout.addWidget(QLabel("إلى:"))
        filter_layout.addWidget(self.end_date)
        filter_layout.addWidget(filter_btn)
        
        layout.addLayout(filter_layout)
        
        # جدول المصاريف
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(9)
        
        headers = ["المعرف", "التاريخ", "النوع", "الوصف", "المبلغ", 
                  "طريقة الدفع", "المورد", "المستخدم", "تاريخ الإدخال"]
        self.expenses_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSortingEnabled(True)
        
        # تخصيص عرض الأعمدة
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الوصف
        
        # قائمة السياق
        self.expenses_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.expenses_table.customContextMenuRequested.connect(self.show_context_menu)
        
        # النقر المزدوج للتعديل
        self.expenses_table.doubleClicked.connect(self.edit_expense)
        
        layout.addWidget(self.expenses_table)
        
        # شريط الإجمالي
        total_layout = QHBoxLayout()
        self.total_label = QLabel("الإجمالي: 0.00 جنيه")
        self.total_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        self.total_label.setStyleSheet(f"color: {config.COLORS['danger']};")
        
        total_layout.addStretch()
        total_layout.addWidget(self.total_label)
        
        layout.addLayout(total_layout)
        
        self.expenses_tab.setLayout(layout)
        
    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        layout = QVBoxLayout()
        
        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        
        # إحصائيات عامة
        general_group = QGroupBox("إحصائيات عامة")
        general_layout = QVBoxLayout()
        
        self.total_expenses_label = QLabel("إجمالي المصاريف: 0 جنيه")
        self.expenses_this_month_label = QLabel("مصاريف هذا الشهر: 0 جنيه")
        self.total_count_label = QLabel("عدد المصاريف: 0")
        self.avg_expense_label = QLabel("متوسط المصروف: 0 جنيه")
        
        general_layout.addWidget(self.total_expenses_label)
        general_layout.addWidget(self.expenses_this_month_label)
        general_layout.addWidget(self.total_count_label)
        general_layout.addWidget(self.avg_expense_label)
        general_group.setLayout(general_layout)
        
        # إحصائيات حسب النوع
        types_group = QGroupBox("المصاريف حسب النوع")
        types_layout = QVBoxLayout()
        
        self.types_table = QTableWidget()
        self.types_table.setColumnCount(3)
        self.types_table.setHorizontalHeaderLabels(["النوع", "المبلغ", "العدد"])
        self.types_table.setMaximumHeight(200)
        
        types_layout.addWidget(self.types_table)
        types_group.setLayout(types_layout)
        
        stats_layout.addWidget(general_group, 0, 0)
        stats_layout.addWidget(types_group, 0, 1)
        
        layout.addLayout(stats_layout)
        layout.addStretch()
        
        self.stats_tab.setLayout(layout)
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        layout = QVBoxLayout()
        
        reports_group = QGroupBox("تقارير المصاريف")
        reports_layout = QVBoxLayout()
        
        # تقرير شهري
        monthly_btn = QPushButton("تقرير شهري")
        monthly_btn.clicked.connect(self.generate_monthly_report)
        
        # تقرير حسب النوع
        by_type_btn = QPushButton("تقرير حسب النوع")
        by_type_btn.clicked.connect(self.generate_type_report)
        
        # تقرير مخصص
        custom_btn = QPushButton("تقرير مخصص")
        custom_btn.clicked.connect(self.generate_custom_report)
        
        reports_layout.addWidget(monthly_btn)
        reports_layout.addWidget(by_type_btn)
        reports_layout.addWidget(custom_btn)
        reports_layout.addStretch()
        
        reports_group.setLayout(reports_layout)
        layout.addWidget(reports_group)
        layout.addStretch()
        
        self.reports_tab.setLayout(layout)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QLineEdit, QComboBox, QDateEdit {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
            
            QTabWidget::pane {{
                border: 1px solid #BDC3C7;
                background-color: white;
            }}
            
            QTabBar::tab {{
                background-color: #ECF0F1;
                padding: 8px 15px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_expenses()
        self.load_statistics()
        
    def load_expenses(self):
        """تحميل المصاريف"""
        try:
            expenses = self.expense_model.get_all_expenses()
            self.populate_table(expenses)
            self.update_total(expenses)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المصاريف:\n{str(e)}")
            
    def populate_table(self, expenses):
        """ملء الجدول بالبيانات"""
        self.expenses_table.setRowCount(len(expenses))
        
        for row, expense in enumerate(expenses):
            items = [
                str(expense[0]),  # ID
                str(expense[1])[:10] if expense[1] else "",  # expense_date
                str(expense[2]),  # expense_type
                str(expense[3]),  # description
                f"{expense[4]:,.2f}",  # amount
                str(expense[5]) if expense[5] else "",  # payment_method
                str(expense[7]) if expense[7] else "",  # supplier
                str(expense[10]) if expense[10] else "",  # created_by_name
                str(expense[9])[:10] if expense[9] else ""  # created_at
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col in [0, 4]:  # أعمدة رقمية
                    item.setTextAlignment(Qt.AlignCenter)
                self.expenses_table.setItem(row, col, item)
                
    def update_total(self, expenses):
        """تحديث الإجمالي"""
        total = sum(expense[4] for expense in expenses if expense[4])
        self.total_label.setText(f"الإجمالي: {total:,.2f} جنيه")
        
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            stats = self.expense_model.get_expense_statistics()
            
            # الإحصائيات العامة
            self.total_expenses_label.setText(f"إجمالي المصاريف: {stats.get('total_expenses', 0):,.2f} جنيه")
            self.expenses_this_month_label.setText(f"مصاريف هذا الشهر: {stats.get('expenses_this_month', 0):,.2f} جنيه")
            self.total_count_label.setText(f"عدد المصاريف: {stats.get('total_count', 0)}")
            
            avg_expense = stats.get('total_expenses', 0) / max(stats.get('total_count', 1), 1)
            self.avg_expense_label.setText(f"متوسط المصروف: {avg_expense:,.2f} جنيه")
            
            # إحصائيات حسب النوع
            by_type = stats.get('by_type', [])
            self.types_table.setRowCount(len(by_type))
            
            for row, (expense_type, total, count) in enumerate(by_type):
                self.types_table.setItem(row, 0, QTableWidgetItem(str(expense_type)))
                self.types_table.setItem(row, 1, QTableWidgetItem(f"{total:,.2f}"))
                self.types_table.setItem(row, 2, QTableWidgetItem(str(count)))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الإحصائيات:\n{str(e)}")
            
    def search_expenses(self):
        """البحث في المصاريف"""
        search_term = self.search_input.text().strip()
        if search_term:
            expenses = self.expense_model.search_expenses(search_term)
            self.populate_table(expenses)
            self.update_total(expenses)
        else:
            self.load_expenses()
            
    def filter_by_type(self):
        """فلترة حسب النوع"""
        expense_type = self.type_filter.currentText()
        if expense_type != "جميع الأنواع":
            expenses = self.expense_model.get_expenses_by_type(expense_type)
            self.populate_table(expenses)
            self.update_total(expenses)
        else:
            self.load_expenses()
            
    def filter_by_date(self):
        """فلترة حسب التاريخ"""
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        
        expenses = self.expense_model.get_expenses_by_date_range(start_date, end_date)
        self.populate_table(expenses)
        self.update_total(expenses)
        
    def add_expense(self):
        """إضافة مصروف جديد"""
        dialog = ExpenseDialog(self, self.database_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
            
    def edit_expense(self):
        """تعديل مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row >= 0:
            expense_id = int(self.expenses_table.item(current_row, 0).text())
            dialog = ExpenseDialog(self, self.database_manager, expense_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()
                
    def delete_expense(self):
        """حذف مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row >= 0:
            expense_id = int(self.expenses_table.item(current_row, 0).text())
            description = self.expenses_table.item(current_row, 3).text()
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المصروف:\n{description}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success, message = self.expense_model.delete_expense(expense_id, self.user_data['id'])
                if success:
                    QMessageBox.information(self, "نجح", message)
                    self.load_data()
                else:
                    QMessageBox.warning(self, "خطأ", message)
                    
    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.expenses_table.itemAt(position):
            menu = QMenu()
            
            edit_action = QAction("تعديل", self)
            edit_action.triggered.connect(self.edit_expense)
            menu.addAction(edit_action)
            
            delete_action = QAction("حذف", self)
            delete_action.triggered.connect(self.delete_expense)
            menu.addAction(delete_action)
            
            menu.exec_(self.expenses_table.mapToGlobal(position))
            
    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير التقرير الشهري قريباً")
        
    def generate_type_report(self):
        """إنشاء تقرير حسب النوع"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير تقرير الأنواع قريباً")
        
    def generate_custom_report(self):
        """إنشاء تقرير مخصص"""
        QMessageBox.information(self, "قيد التطوير", "سيتم تطوير التقرير المخصص قريباً")


class ExpenseDialog(QDialog):
    """نافذة إضافة/تعديل المصروف"""
    
    def __init__(self, parent, database_manager, expense_id=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.expense_id = expense_id
        self.expense_model = Expense(database_manager)
        
        self.setup_ui()
        if expense_id:
            self.load_expense_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة مصروف جديد" if not self.expense_id else "تعديل المصروف")
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout()
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        
        self.type_input = QComboBox()
        self.type_input.addItems(config.EXPENSE_TYPES)
        self.type_input.setEditable(True)
        
        self.description_input = QLineEdit()
        
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, 1000000.0)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" جنيه")
        
        self.payment_method_input = QComboBox()
        self.payment_method_input.addItems(["نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان"])
        self.payment_method_input.setEditable(True)
        
        self.reference_input = QLineEdit()
        self.supplier_input = QLineEdit()
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        
        form_layout.addRow("التاريخ:", self.date_input)
        form_layout.addRow("النوع:", self.type_input)
        form_layout.addRow("الوصف:", self.description_input)
        form_layout.addRow("المبلغ:", self.amount_input)
        form_layout.addRow("طريقة الدفع:", self.payment_method_input)
        form_layout.addRow("رقم المرجع:", self.reference_input)
        form_layout.addRow("المورد:", self.supplier_input)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_expense)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def load_expense_data(self):
        """تحميل بيانات المصروف للتعديل"""
        expense = self.expense_model.get_expense_by_id(self.expense_id)
        if expense:
            # تحويل التاريخ
            if expense[1]:
                date = QDate.fromString(str(expense[1])[:10], "yyyy-MM-dd")
                self.date_input.setDate(date)
                
            self.type_input.setCurrentText(expense[2])
            self.description_input.setText(expense[3])
            self.amount_input.setValue(expense[4])
            self.payment_method_input.setCurrentText(expense[5] if expense[5] else "")
            self.reference_input.setText(expense[6] if expense[6] else "")
            self.supplier_input.setText(expense[7] if expense[7] else "")
            self.notes_input.setPlainText(expense[8] if expense[8] else "")
            
    def save_expense(self):
        """حفظ المصروف"""
        # جمع البيانات
        expense_data = {
            'expense_date': self.date_input.date().toString("yyyy-MM-dd"),
            'expense_type': self.type_input.currentText().strip(),
            'description': self.description_input.text().strip(),
            'amount': self.amount_input.value(),
            'payment_method': self.payment_method_input.currentText().strip(),
            'reference_number': self.reference_input.text().strip(),
            'supplier': self.supplier_input.text().strip(),
            'notes': self.notes_input.toPlainText().strip()
        }
        
        # التحقق من صحة البيانات
        errors = self.expense_model.validate_expense_data(expense_data)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
            
        # حفظ البيانات
        try:
            if self.expense_id:
                # تحديث
                success, message = self.expense_model.update_expense(
                    self.expense_id, expense_data, self.parent().user_data['id']
                )
            else:
                # إضافة جديد
                success, expense_id, message = self.expense_model.add_expense(
                    expense_data, self.parent().user_data['id']
                )
                
            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات:\n{str(e)}")
