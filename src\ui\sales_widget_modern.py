# -*- coding: utf-8 -*-
"""
واجهة إدارة المبيعات والفواتير المحسنة
Modern Sales Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QSplitter,
                            QGroupBox, QGridLayout, QComboBox, QSpinBox,
                            QTabWidget, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

import config
from src.models.sales_invoice import SalesInvoice
from src.models.customer import Customer
from src.models.granite_type import GraniteType
from src.utils.font_utils import apply_font_settings, get_responsive_style

class SalesWidget(QWidget):
    """واجهة إدارة المبيعات والفواتير المحسنة"""
    
    def __init__(self, database_manager=None, user_data=None):
        super().__init__()
        
        # إنشاء مدير قاعدة البيانات إذا لم يتم تمريره
        if not database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()
        else:
            self.database_manager = database_manager
            
        self.user_data = user_data or {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام'}
        
        # إنشاء النماذج
        self.invoice_model = SalesInvoice(self.database_manager)
        self.customer_model = Customer(self.database_manager)
        self.granite_type_model = GraniteType(self.database_manager)
        
        self.setup_ui()
        self.apply_modern_styles()
        apply_font_settings(self)
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # العنوان الرئيسي
        self.create_header_section(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        self.setLayout(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # العنوان مع الأيقونة
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("💰")
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel("إدارة المبيعات والفواتير")
        title_label.setObjectName("sectionTitle")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 15px;
        """)
        
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # الأزرار السريعة
        self.create_quick_actions(header_layout)
        
        header_layout.addLayout(title_layout)
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
    def create_quick_actions(self, layout):
        """إنشاء الأزرار السريعة"""
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)
        
        # زر فاتورة جديدة
        new_invoice_btn = QPushButton("📄 فاتورة جديدة")
        new_invoice_btn.setObjectName("modernButton")
        new_invoice_btn.clicked.connect(self.create_new_invoice)
        
        # زر عميل جديد
        new_customer_btn = QPushButton("👤 عميل جديد")
        new_customer_btn.setObjectName("successButton")
        new_customer_btn.clicked.connect(self.add_customer)
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setObjectName("modernButton")
        refresh_btn.clicked.connect(self.load_data)
        
        actions_layout.addWidget(new_invoice_btn)
        actions_layout.addWidget(new_customer_btn)
        actions_layout.addWidget(refresh_btn)
        
        layout.addLayout(actions_layout)
        
    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # إنشاء التبويبات
        self.create_tabs_section(content_layout)
        
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)
        
    def create_tabs_section(self, layout):
        """إنشاء قسم التبويبات"""
        self.tabs = QTabWidget()
        self.tabs.setObjectName("modernTabs")
        
        # تبويب الفواتير
        self.create_invoices_tab()
        
        # تبويب العملاء
        self.create_customers_tab()
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
        
        layout.addWidget(self.tabs)
        
    def create_invoices_tab(self):
        """إنشاء تبويب الفواتير"""
        invoices_widget = QWidget()
        invoices_layout = QVBoxLayout()
        invoices_layout.setContentsMargins(15, 15, 15, 15)
        invoices_layout.setSpacing(15)
        
        # شريط البحث والفلترة
        search_frame = QFrame()
        search_frame.setObjectName("card")
        search_layout = QHBoxLayout()
        
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في الفواتير...")
        self.search_input.textChanged.connect(self.filter_invoices)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addStretch()
        
        search_frame.setLayout(search_layout)
        invoices_layout.addWidget(search_frame)
        
        # جدول الفواتير
        self.create_invoices_table(invoices_layout)
        
        invoices_widget.setLayout(invoices_layout)
        self.tabs.addTab(invoices_widget, "📄 الفواتير")
        
    def create_invoices_table(self, layout):
        """إنشاء جدول الفواتير"""
        table_frame = QFrame()
        table_frame.setObjectName("card")
        table_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("📋 قائمة الفواتير")
        table_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.invoices_table = QTableWidget()
        self.invoices_table.setObjectName("modernTable")
        
        # إعداد أعمدة الجدول
        columns = ["رقم الفاتورة", "العميل", "التاريخ", "المبلغ الإجمالي", "المدفوع", "المتبقي", "الحالة", "الإجراءات"]
        self.invoices_table.setColumnCount(len(columns))
        self.invoices_table.setHorizontalHeaderLabels(columns)
        
        # إعداد خصائص الجدول
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.invoices_table.horizontalHeader().setStretchLastSection(True)
        
        table_layout.addWidget(self.invoices_table)
        table_frame.setLayout(table_layout)
        layout.addWidget(table_frame)
        
    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        customers_widget = QWidget()
        customers_layout = QVBoxLayout()
        customers_layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان القسم
        title = QLabel("👥 إدارة العملاء")
        title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        customers_layout.addWidget(title)
        
        # أزرار العملاء
        buttons_layout = QHBoxLayout()
        
        add_customer_btn = QPushButton("➕ إضافة عميل")
        add_customer_btn.setObjectName("successButton")
        add_customer_btn.clicked.connect(self.add_customer)
        
        manage_customers_btn = QPushButton("📝 إدارة العملاء")
        manage_customers_btn.setObjectName("modernButton")
        manage_customers_btn.clicked.connect(self.manage_customers)
        
        cutting_payments_btn = QPushButton("⚡ دفعات النشر")
        cutting_payments_btn.setObjectName("modernButton")
        cutting_payments_btn.clicked.connect(self.show_cutting_payments)
        
        buttons_layout.addWidget(add_customer_btn)
        buttons_layout.addWidget(manage_customers_btn)
        buttons_layout.addWidget(cutting_payments_btn)
        buttons_layout.addStretch()
        
        customers_layout.addLayout(buttons_layout)
        
        # محتوى مؤقت
        content = QLabel("استخدم الأزرار أعلاه لإدارة العملاء ودفعات النشر")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("font-size: 14px; color: #7f8c8d; padding: 50px;")
        customers_layout.addWidget(content)
        
        customers_widget.setLayout(customers_layout)
        self.tabs.addTab(customers_widget, "👥 العملاء")
        
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_widget = QWidget()
        stats_layout = QVBoxLayout()
        stats_layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان القسم
        title = QLabel("📊 إحصائيات المبيعات")
        title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        stats_layout.addWidget(title)
        
        # بطاقات الإحصائيات
        self.create_sales_stats_cards(stats_layout)
        
        stats_widget.setLayout(stats_layout)
        self.tabs.addTab(stats_widget, "📊 الإحصائيات")
        
    def create_sales_stats_cards(self, layout):
        """إنشاء بطاقات إحصائيات المبيعات"""
        cards_layout = QGridLayout()
        
        # بيانات الإحصائيات
        stats_data = [
            ("💰", "إجمالي المبيعات", "125,000", "ريال هذا الشهر"),
            ("📄", "عدد الفواتير", "45", "فاتورة هذا الشهر"),
            ("💳", "المدفوع", "95,000", "ريال"),
            ("⏳", "المتبقي", "30,000", "ريال"),
        ]
        
        for i, (icon, title, value, subtitle) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, subtitle)
            row = i // 2
            col = i % 2
            cards_layout.addWidget(card, row, col)
            
        layout.addLayout(cards_layout)
        
    def create_stat_card(self, icon, title, value, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("card")
        card.setFixedHeight(120)
        
        layout = QVBoxLayout()
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #3498db; margin: 10px 0;")
        
        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        
        card.setLayout(layout)
        return card

    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""
            /* الإطار الرئيسي */
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }

            /* إطار العنوان */
            QFrame#headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 3px solid #3498db;
            }

            /* إطار المحتوى */
            QFrame#contentFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
            }

            /* البطاقات */
            QFrame#card {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }

            QFrame#card:hover {
                border-color: #3498db;
                box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
            }

            /* الأزرار الحديثة */
            QPushButton#modernButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
                min-height: 20px;
            }

            QPushButton#modernButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }

            /* أزرار النجاح */
            QPushButton#successButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
            }

            QPushButton#successButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }

            /* التبويبات */
            QTabWidget#modernTabs::pane {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                margin-top: 5px;
            }

            QTabWidget#modernTabs QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 500;
            }

            QTabWidget#modernTabs QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
            }

            /* الجداول */
            QTableWidget#modernTable {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                gridline-color: #f1f2f6;
                selection-background-color: #ebf3fd;
            }

            QTableWidget#modernTable::item {
                padding: 12px;
                border-bottom: 1px solid #f1f2f6;
            }

            QTableWidget#modernTable::item:selected {
                background-color: #ebf3fd;
                color: #2c3e50;
            }

            QTableWidget#modernTable QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }

            /* حقول الإدخال */
            QLineEdit {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
                font-size: 14px;
            }

            QLineEdit:focus {
                border-color: #3498db;
                background-color: #fbfcfd;
            }
        """)

    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_invoices()
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"خطأ في تحميل البيانات: {str(e)}")

    def load_invoices(self):
        """تحميل الفواتير"""
        try:
            # الحصول على الفواتير من قاعدة البيانات
            invoices = self.invoice_model.get_all_invoices()

            # تحديث الجدول
            self.invoices_table.setRowCount(len(invoices))

            for row, invoice in enumerate(invoices):
                # رقم الفاتورة
                self.invoices_table.setItem(row, 0, QTableWidgetItem(str(invoice[1])))

                # العميل (سيتم تحسينه لاحقاً)
                self.invoices_table.setItem(row, 1, QTableWidgetItem("عميل"))

                # التاريخ
                self.invoices_table.setItem(row, 2, QTableWidgetItem(str(invoice[2])))

                # المبلغ الإجمالي
                self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{invoice[6]:.2f}"))

                # المدفوع
                self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{invoice[8]:.2f}"))

                # المتبقي
                remaining = invoice[6] - invoice[8]
                self.invoices_table.setItem(row, 5, QTableWidgetItem(f"{remaining:.2f}"))

                # الحالة
                status = "مدفوع" if remaining <= 0 else "جزئي" if invoice[8] > 0 else "غير مدفوع"
                self.invoices_table.setItem(row, 6, QTableWidgetItem(status))

                # الإجراءات (أزرار)
                actions_widget = self.create_actions_widget(invoice[0])
                self.invoices_table.setCellWidget(row, 7, actions_widget)

        except Exception as e:
            print(f"خطأ في تحميل الفواتير: {str(e)}")

    def create_actions_widget(self, invoice_id):
        """إنشاء ويدجت الإجراءات للفاتورة"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # زر عرض
        view_btn = QPushButton("👁️")
        view_btn.setToolTip("عرض الفاتورة")
        view_btn.setFixedSize(30, 30)
        view_btn.clicked.connect(lambda: self.view_invoice(invoice_id))

        # زر تعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل الفاتورة")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_invoice(invoice_id))

        # زر دفعة
        payment_btn = QPushButton("💳")
        payment_btn.setToolTip("إضافة دفعة")
        payment_btn.setFixedSize(30, 30)
        payment_btn.clicked.connect(lambda: self.add_payment(invoice_id))

        layout.addWidget(view_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(payment_btn)

        widget.setLayout(layout)
        return widget

    def filter_invoices(self):
        """فلترة الفواتير حسب النص المدخل"""
        search_text = self.search_input.text().lower()

        for row in range(self.invoices_table.rowCount()):
            show_row = False
            for col in range(self.invoices_table.columnCount() - 1):  # استثناء عمود الإجراءات
                item = self.invoices_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.invoices_table.setRowHidden(row, not show_row)

    # دوال الإجراءات
    def create_new_invoice(self):
        """إنشاء فاتورة جديدة"""
        try:
            from src.ui.invoice_dialog import InvoiceDialog
            dialog = InvoiceDialog(self, self.database_manager, self.user_data)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()
                QMessageBox.information(self, "نجح", "تم إنشاء الفاتورة بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إنشاء الفاتورة: {str(e)}")

    def add_customer(self):
        """إضافة عميل جديد"""
        try:
            from src.ui.customer_dialog import CustomerDialog
            dialog = CustomerDialog(self, self.database_manager, self.user_data)
            if dialog.exec_() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم إضافة العميل بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إضافة العميل: {str(e)}")

    def manage_customers(self):
        """إدارة العملاء"""
        QMessageBox.information(self, "قيد التطوير", "نافذة إدارة العملاء قيد التطوير")

    def show_cutting_payments(self):
        """عرض دفعات النشر"""
        try:
            from src.ui.cutting_payment_dialog import CuttingPaymentDialog
            dialog = CuttingPaymentDialog(self, self.database_manager, self.user_data, customer_id=1)
            dialog.exec_()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في عرض دفعات النشر: {str(e)}")

    def view_invoice(self, invoice_id):
        """عرض الفاتورة"""
        QMessageBox.information(self, "قيد التطوير", f"عرض الفاتورة رقم {invoice_id}")

    def edit_invoice(self, invoice_id):
        """تعديل الفاتورة"""
        QMessageBox.information(self, "قيد التطوير", f"تعديل الفاتورة رقم {invoice_id}")

    def add_payment(self, invoice_id):
        """إضافة دفعة للفاتورة"""
        try:
            from src.ui.payment_dialog import PaymentDialog
            dialog = PaymentDialog(self, self.database_manager, self.user_data, invoice_id=invoice_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()
                QMessageBox.information(self, "نجح", "تم إضافة الدفعة بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إضافة الدفعة: {str(e)}")
