#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق الواجهة الحديثة على جميع الأقسام
Apply Modern UI to All Sections
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def get_modern_styles():
    """الحصول على الأنماط الحديثة الموحدة"""
    return """
        /* الأنماط الحديثة الموحدة */
        QWidget {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        /* العناوين الرئيسية */
        QLabel#sectionTitle {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ffffff, stop:1 #f8f9fa);
            border-bottom: 3px solid #3498db;
            margin-bottom: 20px;
        }
        
        /* البطاقات */
        QFrame#card {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 10px;
        }
        
        QFrame#card:hover {
            border-color: #3498db;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
        }
        
        /* الأزرار الحديثة */
        QPushButton#modernButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            min-height: 20px;
        }
        
        QPushButton#modernButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2980b9, stop:1 #21618c);
            transform: translateY(-2px);
        }
        
        QPushButton#modernButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #21618c, stop:1 #1b4f72);
        }
        
        /* أزرار الخطر */
        QPushButton#dangerButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e74c3c, stop:1 #c0392b);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        QPushButton#dangerButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #c0392b, stop:1 #a93226);
        }
        
        /* أزرار النجاح */
        QPushButton#successButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #27ae60, stop:1 #229954);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        QPushButton#successButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #229954, stop:1 #1e8449);
        }
        
        /* الجداول */
        QTableWidget {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            gridline-color: #f1f2f6;
            selection-background-color: #ebf3fd;
        }
        
        QTableWidget::item {
            padding: 12px;
            border-bottom: 1px solid #f1f2f6;
        }
        
        QTableWidget::item:selected {
            background-color: #ebf3fd;
            color: #2c3e50;
        }
        
        QHeaderView::section {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #34495e, stop:1 #2c3e50);
            color: white;
            padding: 12px;
            border: none;
            font-weight: bold;
        }
        
        /* حقول الإدخال */
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
            background-color: white;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            font-size: 14px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #3498db;
            background-color: #fbfcfd;
        }
        
        /* التبويبات */
        QTabWidget::pane {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        QTabBar::tab {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ecf0f1, stop:1 #bdc3c7);
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        
        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
            color: white;
        }
        
        /* شريط التمرير */
        QScrollBar:vertical {
            background-color: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #bdc3c7, stop:1 #95a5a6);
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #95a5a6, stop:1 #7f8c8d);
        }
        
        /* مجموعات العناصر */
        QGroupBox {
            background-color: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-top: 10px;
            padding-top: 15px;
            font-weight: bold;
            font-size: 14px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 5px 10px;
            background-color: #3498db;
            color: white;
            border-radius: 5px;
        }
        
        /* شريط التقدم */
        QProgressBar {
            background-color: #ecf0f1;
            border: none;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1abc9c, stop:1 #16a085);
            border-radius: 8px;
        }
    """

def apply_modern_ui_to_widget(widget_path, widget_name):
    """تطبيق الواجهة الحديثة على ويدجت معين"""
    print(f"🎨 تحسين {widget_name}...")
    
    try:
        # قراءة الملف الحالي
        with open(widget_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة الاستيرادات المطلوبة إذا لم تكن موجودة
        if "from src.utils.font_utils import apply_font_settings" not in content:
            import_line = "from src.utils.font_utils import apply_font_settings, get_responsive_style\n"
            # البحث عن آخر سطر import وإضافة الاستيراد بعده
            lines = content.split('\n')
            last_import_index = -1
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    last_import_index = i
            
            if last_import_index != -1:
                lines.insert(last_import_index + 1, import_line.strip())
                content = '\n'.join(lines)
        
        # إضافة تطبيق الخطوط في __init__ إذا لم يكن موجوداً
        if "apply_font_settings(self)" not in content:
            # البحث عن __init__ وإضافة تطبيق الخطوط
            if "def __init__(self" in content:
                content = content.replace(
                    "self.setup_ui()",
                    "self.setup_ui()\n        apply_font_settings(self)"
                )
        
        # إضافة الأنماط الحديثة
        if "def apply_modern_styles(self):" not in content:
            modern_styles_method = f'''
    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""{get_modern_styles()}""")
'''
            content += modern_styles_method
        
        # إضافة استدعاء الأنماط في setup_ui
        if "self.apply_modern_styles()" not in content:
            content = content.replace(
                "self.setup_ui()",
                "self.setup_ui()\n        self.apply_modern_styles()"
            )
        
        # حفظ الملف المحدث
        with open(widget_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم تحسين {widget_name}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحسين {widget_name}: {str(e)}")
        return False

def main():
    """الدالة الرئيسية لتطبيق الواجهة الحديثة على جميع الأقسام"""
    print("🎨 بدء تطبيق الواجهة الحديثة على جميع الأقسام")
    print("=" * 60)
    
    # قائمة الويدجتات المراد تحسينها
    widgets_to_enhance = [
        ("src/ui/sales_widget.py", "واجهة المبيعات"),
        ("src/ui/expenses_widget.py", "واجهة المصاريف"),
        ("src/ui/orders_widget.py", "واجهة الطلبات"),
        ("src/ui/reports_widget.py", "واجهة التقارير"),
        ("src/ui/trucks_widget.py", "واجهة الجرارات"),
        ("src/ui/cutting_widget.py", "واجهة التقطيع"),
        ("src/ui/customer_dialog.py", "نافذة العملاء"),
        ("src/ui/order_dialog.py", "نافذة الطلبات"),
        ("src/ui/payment_dialog.py", "نافذة الدفعات"),
        ("src/ui/invoice_dialog.py", "نافذة الفواتير"),
        ("src/ui/settings_dialog.py", "نافذة الإعدادات"),
    ]
    
    success_count = 0
    total_count = len(widgets_to_enhance)
    
    for widget_path, widget_name in widgets_to_enhance:
        if os.path.exists(widget_path):
            if apply_modern_ui_to_widget(widget_path, widget_name):
                success_count += 1
        else:
            print(f"⚠️ الملف غير موجود: {widget_path}")
    
    # تقرير النتائج
    print("\n📊 تقرير تطبيق الواجهة الحديثة:")
    print("=" * 40)
    print(f"تم تحسين: {success_count}/{total_count} ويدجت")
    print(f"معدل النجاح: {(success_count/total_count)*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 تم تطبيق الواجهة الحديثة على جميع الأقسام بنجاح!")
        print("💡 شغل النظام: python run_modern_system.py")
        return 0
    else:
        print(f"\n⚠️ تم تحسين {success_count} من أصل {total_count} ويدجت")
        return 1

if __name__ == "__main__":
    sys.exit(main())
