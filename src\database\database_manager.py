# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import pyodbc
import logging
from datetime import datetime
import config
from src.utils.logger import log_database_operation, log_error

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self):
        self.connection_string = config.get_connection_string()
        self.connection = None
        self.logger = logging.getLogger('DatabaseManager')
        self.sqlite_manager = None
        self.use_sqlite = False

    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            # محاولة الاتصال بـ SQL Server أولاً
            self.connection = pyodbc.connect(self.connection_string)
            self.connection.autocommit = False
            self.use_sqlite = False
            log_database_operation("CONNECT", "SQL_SERVER", "تم الاتصال بقاعدة البيانات")
            return True
        except Exception as e:
            log_error(f"خطأ في الاتصال بـ SQL Server: {str(e)}")

            # التبديل إلى SQLite كبديل
            try:
                from src.database.sqlite_manager import SQLiteManager
                self.sqlite_manager = SQLiteManager()
                if self.sqlite_manager.connect():
                    self.sqlite_manager.setup_database()
                    self.use_sqlite = True
                    log_database_operation("CONNECT", "SQLITE", "تم الاتصال بقاعدة بيانات SQLite البديلة")
                    print("⚠️ تم التبديل إلى قاعدة بيانات SQLite البديلة")
                    return True
                else:
                    return False
            except Exception as sqlite_error:
                log_error(f"فشل الاتصال بـ SQLite أيضاً: {str(sqlite_error)}")
                return False
            
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.use_sqlite and self.sqlite_manager:
            self.sqlite_manager.disconnect()
        elif self.connection:
            self.connection.close()
            self.connection = None
            
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            if self.connect():
                if self.use_sqlite:
                    return self.sqlite_manager.test_connection()
                else:
                    cursor = self.connection.cursor()
                    cursor.execute("SELECT 1")
                    cursor.close()
                    self.disconnect()
                    return True
            return False
        except Exception as e:
            log_error(f"فشل اختبار الاتصال: {str(e)}")
            return False
            
    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام SQL"""
        try:
            # استخدام SQLite إذا كان متاحاً
            if self.use_sqlite and self.sqlite_manager:
                return self.sqlite_manager.execute_query(query, params, fetch)

            # استخدام SQL Server
            if not self.connection:
                if not self.connect():
                    return None

            cursor = self.connection.cursor()

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if fetch:
                if fetch == 'one':
                    result = cursor.fetchone()
                elif fetch == 'all':
                    result = cursor.fetchall()
                else:
                    result = cursor.fetchmany(fetch)
            else:
                result = cursor.rowcount

            cursor.close()
            return result

        except Exception as e:
            log_error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            if self.connection:
                self.connection.rollback()
            return None
            
    def execute_transaction(self, queries_with_params):
        """تنفيذ مجموعة استعلامات في معاملة واحدة"""
        try:
            # استخدام SQLite إذا كان متاحاً
            if self.use_sqlite and self.sqlite_manager:
                return self.sqlite_manager.execute_transaction(queries_with_params)

            # استخدام SQL Server
            if not self.connection:
                if not self.connect():
                    return False

            cursor = self.connection.cursor()

            for query, params in queries_with_params:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

            self.connection.commit()
            cursor.close()
            log_database_operation("TRANSACTION", "MULTIPLE", f"تم تنفيذ {len(queries_with_params)} استعلام")
            return True

        except Exception as e:
            log_error(f"خطأ في تنفيذ المعاملة: {str(e)}")
            if self.connection:
                self.connection.rollback()
            return False
            
    def create_database_if_not_exists(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # الاتصال بقاعدة البيانات الرئيسية
            master_connection_string = self.connection_string.replace(
                f"DATABASE={config.DATABASE_CONFIG['database']};", 
                "DATABASE=master;"
            )
            
            connection = pyodbc.connect(master_connection_string)
            cursor = connection.cursor()
            
            # التحقق من وجود قاعدة البيانات
            cursor.execute(
                "SELECT name FROM sys.databases WHERE name = ?", 
                (config.DATABASE_CONFIG['database'],)
            )
            
            if not cursor.fetchone():
                # إنشاء قاعدة البيانات
                cursor.execute(f"CREATE DATABASE [{config.DATABASE_CONFIG['database']}]")
                connection.commit()
                self.logger.info(f"تم إنشاء قاعدة البيانات: {config.DATABASE_CONFIG['database']}")
                
            cursor.close()
            connection.close()
            return True
            
        except Exception as e:
            log_error(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")
            return False
            
    def get_table_exists(self, table_name):
        """التحقق من وجود جدول"""
        query = """
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME = ?
        """
        result = self.execute_query(query, (table_name,), fetch='one')
        return result and result[0] > 0
        
    def backup_database(self, backup_path):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            query = f"""
            BACKUP DATABASE [{config.DATABASE_CONFIG['database']}] 
            TO DISK = '{backup_path}'
            """
            self.execute_query(query)
            log_database_operation("BACKUP", "DATABASE", f"النسخة الاحتياطية: {backup_path}")
            return True
        except Exception as e:
            log_error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            return False
