# -*- coding: utf-8 -*-
"""
نافذة إضافة الدفعات
Payment Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QDoubleSpinBox, QPushButton, QMessageBox,
                            QLabel, QGroupBox, QComboBox, QDateEdit, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

import config
from src.models.sales_invoice import SalesInvoice
from src.models.customer import Customer
from src.utils.font_utils import apply_font_settings, get_responsive_style

class PaymentDialog(QDialog):
    """نافذة إضافة دفعة للفاتورة"""
    
    def __init__(self, parent=None, database_manager=None, user_data=None, invoice_id=None, customer_id=None, payment_type='invoice'):
        super().__init__(parent)
        self.database_manager = database_manager
        self.user_data = user_data
        self.invoice_id = invoice_id
        self.customer_id = customer_id
        self.payment_type = payment_type  # 'invoice' أو 'cutting'
        self.invoice_model = SalesInvoice(database_manager)
        self.customer_model = Customer(database_manager)

        self.setup_ui()
        apply_font_settings(self)
        if payment_type == 'invoice' and invoice_id:
            self.load_invoice_info()
        elif payment_type == 'cutting' and customer_id:
            self.load_customer_cutting_info()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        if self.payment_type == 'cutting':
            self.setWindowTitle("إضافة دفعة للعميل - عمليات النشر")
        else:
            self.setWindowTitle("إضافة دفعة للفاتورة")
        self.setFixedSize(500, 500)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # معلومات الفاتورة أو العميل
        if self.payment_type == 'cutting':
            info_group = QGroupBox("معلومات العميل - عمليات النشر")
        else:
            info_group = QGroupBox("معلومات الفاتورة")
        info_layout = QFormLayout()

        if self.payment_type == 'invoice':
            self.invoice_number_label = QLabel()
            self.invoice_number_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
            info_layout.addRow("رقم الفاتورة:", self.invoice_number_label)

        self.customer_label = QLabel()
        info_layout.addRow("العميل:", self.customer_label)

        if self.payment_type == 'cutting':
            self.cutting_operations_label = QLabel()
            info_layout.addRow("عمليات النشر:", self.cutting_operations_label)

            self.total_cutting_cost_label = QLabel()
            self.total_cutting_cost_label.setStyleSheet("font-weight: bold; color: #E74C3C;")
            info_layout.addRow("إجمالي تكلفة النشر:", self.total_cutting_cost_label)
        else:
            self.total_amount_label = QLabel()
            self.total_amount_label.setStyleSheet("font-weight: bold; color: #E74C3C;")
            info_layout.addRow("إجمالي الفاتورة:", self.total_amount_label)

        self.paid_amount_label = QLabel()
        self.paid_amount_label.setStyleSheet("font-weight: bold; color: #27AE60;")
        info_layout.addRow("المدفوع سابقاً:", self.paid_amount_label)

        self.remaining_amount_label = QLabel()
        self.remaining_amount_label.setStyleSheet("font-weight: bold; color: #F39C12;")
        info_layout.addRow("المبلغ المتبقي:", self.remaining_amount_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # معلومات الدفعة
        payment_group = QGroupBox("معلومات الدفعة")
        payment_layout = QFormLayout()
        
        # تاريخ الدفعة
        self.payment_date = QDateEdit()
        self.payment_date.setDate(QDate.currentDate())
        self.payment_date.setCalendarPopup(True)
        payment_layout.addRow("تاريخ الدفعة:", self.payment_date)
        
        # مبلغ الدفعة
        self.payment_amount = QDoubleSpinBox()
        self.payment_amount.setRange(0.01, 1000000)
        self.payment_amount.setDecimals(2)
        self.payment_amount.setSuffix(" جنيه")
        self.payment_amount.valueChanged.connect(self.validate_amount)
        payment_layout.addRow("مبلغ الدفعة:", self.payment_amount)
        
        # طريقة الدفع
        self.payment_method = QComboBox()
        self.payment_method.addItems([
            "نقدي",
            "شيك",
            "تحويل بنكي",
            "بطاقة ائتمان",
            "بطاقة خصم",
            "محفظة إلكترونية",
            "أخرى"
        ])
        payment_layout.addRow("طريقة الدفع:", self.payment_method)
        
        # رقم المرجع
        self.reference_number = QLineEdit()
        self.reference_number.setPlaceholderText("رقم الشيك أو التحويل أو المرجع")
        payment_layout.addRow("رقم المرجع:", self.reference_number)
        
        payment_group.setLayout(payment_layout)
        layout.addWidget(payment_group)
        
        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات إضافية عن الدفعة...")
        notes_layout.addWidget(self.notes_input)
        
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        # رسالة التحذير
        self.warning_label = QLabel()
        self.warning_label.setStyleSheet("color: #E74C3C; font-weight: bold;")
        self.warning_label.setVisible(False)
        layout.addWidget(self.warning_label)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الدفعة")
        self.save_btn.clicked.connect(self.save_payment)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        self.apply_styles()

    def apply_font_settings(self):
        """تطبيق إعدادات الخط وتحديث أحجام الحقول"""
        font_settings = self.settings.get_font_settings()
        font = QFont(font_settings['family'], font_settings['size'])
        if font_settings['bold']:
            font.setBold(True)

        # تطبيق الخط على جميع العناصر
        self.setFont(font)

        # حساب الحجم المناسب للحقول بناءً على حجم الخط
        font_metrics = QFont(font_settings['family'], font_settings['size']).pointSize()
        field_height = max(25, font_metrics + 10)  # حد أدنى 25 بكسل

        # تطبيق الحجم على جميع حقول الإدخال
        for widget in self.findChildren((QLineEdit, QDoubleSpinBox, QComboBox, QDateEdit, QTextEdit)):
            widget.setMinimumHeight(field_height)
            if isinstance(widget, QTextEdit):
                widget.setMinimumHeight(field_height * 2)  # النصوص الطويلة تحتاج مساحة أكبر

    def load_customer_cutting_info(self):
        """تحميل معلومات العميل وعمليات النشر"""
        if not self.customer_id:
            return

        # تحميل معلومات العميل
        customer = self.customer_model.get_customer_by_id(self.customer_id)
        if customer:
            self.customer_label.setText(customer[1])

            # حساب إجمالي تكلفة النشر للعميل (سيتم تطويرها لاحقاً)
            # هنا يمكن إضافة استعلام لحساب تكلفة عمليات النشر
            total_cutting_cost = 0  # مؤقت
            paid_amount = 0  # مؤقت

            self.cutting_operations_label.setText("عمليات متعددة")
            self.total_cutting_cost_label.setText(f"{total_cutting_cost:,.2f} جنيه")
            self.paid_amount_label.setText(f"{paid_amount:,.2f} جنيه")

            remaining = total_cutting_cost - paid_amount
            self.remaining_amount_label.setText(f"{remaining:,.2f} جنيه")

            # تعيين الحد الأقصى لمبلغ الدفعة
            self.payment_amount.setMaximum(max(remaining, 1000000))
            self.payment_amount.setValue(min(remaining, 1000))

            self.remaining_amount = remaining

    def load_invoice_info(self):
        """تحميل معلومات الفاتورة"""
        if not self.invoice_id:
            return
            
        invoice = self.invoice_model.get_invoice_by_id(self.invoice_id)
        if invoice:
            self.invoice_number_label.setText(invoice[1])
            self.customer_label.setText(invoice[16] if invoice[16] else "غير محدد")
            self.total_amount_label.setText(f"{invoice[7]:,.2f} جنيه")
            self.paid_amount_label.setText(f"{invoice[8]:,.2f} جنيه")
            
            remaining = invoice[7] - invoice[8]
            self.remaining_amount_label.setText(f"{remaining:,.2f} جنيه")
            
            # تعيين الحد الأقصى لمبلغ الدفعة
            self.payment_amount.setMaximum(remaining)
            self.payment_amount.setValue(remaining)
            
            self.remaining_amount = remaining
            
    def validate_amount(self):
        """التحقق من صحة المبلغ"""
        amount = self.payment_amount.value()
        
        if amount > self.remaining_amount:
            self.warning_label.setText("⚠️ مبلغ الدفعة أكبر من المبلغ المتبقي")
            self.warning_label.setVisible(True)
            self.save_btn.setEnabled(False)
        elif amount <= 0:
            self.warning_label.setText("⚠️ مبلغ الدفعة يجب أن يكون أكبر من صفر")
            self.warning_label.setVisible(True)
            self.save_btn.setEnabled(False)
        else:
            self.warning_label.setVisible(False)
            self.save_btn.setEnabled(True)
            
    def save_payment(self):
        """حفظ الدفعة"""
        # التحقق من صحة البيانات
        if self.payment_amount.value() <= 0:
            QMessageBox.warning(self, "خطأ", "مبلغ الدفعة يجب أن يكون أكبر من صفر")
            return

        if hasattr(self, 'remaining_amount') and self.payment_amount.value() > self.remaining_amount:
            QMessageBox.warning(self, "خطأ", "مبلغ الدفعة أكبر من المبلغ المتبقي")
            return

        # حفظ الدفعة
        try:
            if self.payment_type == 'invoice':
                # دفعة للفاتورة
                success, message = self.invoice_model.add_payment(
                    self.invoice_id,
                    self.payment_amount.value(),
                    self.payment_method.currentText(),
                    self.reference_number.text().strip(),
                    self.user_data['id']
                )
            else:
                # دفعة لعمليات النشر
                success, message = self.save_cutting_payment()

            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الدفعة:\n{str(e)}")

    def save_cutting_payment(self):
        """حفظ دفعة عمليات النشر"""
        # هنا سيتم إضافة منطق حفظ دفعات عمليات النشر
        # يمكن إنشاء جدول منفصل للدفعات أو ربطها بجدول العمليات

        payment_data = {
            'customer_id': self.customer_id,
            'amount': self.payment_amount.value(),
            'payment_method': self.payment_method.currentText(),
            'reference_number': self.reference_number.text().strip(),
            'payment_date': self.payment_date.date().toString("yyyy-MM-dd"),
            'notes': self.notes_input.toPlainText().strip(),
            'payment_type': 'cutting',
            'created_by': self.user_data['id']
        }

        # مؤقتاً نعيد نجح - سيتم تطوير هذا لاحقاً مع قاعدة البيانات
        return True, "تم حفظ دفعة عمليات النشر بنجاح"
            
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QPushButton:disabled {{
                background-color: #BDC3C7;
                color: #7F8C8D;
            }}
            
            QLineEdit, QTextEdit, QDoubleSpinBox, QComboBox, QDateEdit {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
        """)
