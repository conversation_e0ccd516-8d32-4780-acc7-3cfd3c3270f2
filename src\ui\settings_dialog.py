# -*- coding: utf-8 -*-
"""
نافذة الإعدادات
Settings Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QSpinBox, QComboBox, QCheckBox,
                            QPushButton, QGroupBox, QGridLayout, QFormLayout,
                            QColorDialog, QFontDialog, QMessageBox, QSlider,
                            QLineEdit, QFileDialog, QTextEdit, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

import config
from src.models.settings import Settings

class SettingsDialog(QDialog):
    """نافذة إعدادات النظام"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, parent=None, user_data=None):
        super().__init__(parent)
        self.user_data = user_data
        self.settings = Settings()
        self.setup_ui()
        self.load_current_settings()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إعدادات النظام")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الخط والمظهر
        self.create_appearance_tab()
        self.tabs.addTab(self.appearance_tab, "المظهر والخط")
        
        # تبويب النافذة
        self.create_window_tab()
        self.tabs.addTab(self.window_tab, "النافذة")
        
        # تبويب التقارير
        self.create_reports_tab()
        self.tabs.addTab(self.reports_tab, "التقارير")
        
        # تبويب قاعدة البيانات
        self.create_database_tab()
        self.tabs.addTab(self.database_tab, "قاعدة البيانات")
        
        # تبويب الأمان
        self.create_security_tab()
        self.tabs.addTab(self.security_tab, "الأمان")
        
        # تبويب متقدم
        self.create_advanced_tab()
        self.tabs.addTab(self.advanced_tab, "متقدم")
        
        layout.addWidget(self.tabs)
        
        # الأزرار
        self.create_buttons(layout)
        
        self.setLayout(layout)
        self.apply_styles()
        
    def create_appearance_tab(self):
        """إنشاء تبويب المظهر والخط"""
        self.appearance_tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات الخط
        font_group = QGroupBox("إعدادات الخط")
        font_layout = QFormLayout()
        
        # نوع الخط
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems([
            "Segoe UI", "Arial", "Tahoma", "Calibri", "Times New Roman",
            "Cairo", "Amiri", "Scheherazade", "Noto Sans Arabic"
        ])
        font_layout.addRow("نوع الخط:", self.font_family_combo)
        
        # حجم الخط
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 72)
        self.font_size_spin.setSuffix(" pt")
        font_layout.addRow("حجم الخط:", self.font_size_spin)
        
        # خط عريض
        self.font_bold_check = QCheckBox("خط عريض")
        font_layout.addRow("", self.font_bold_check)
        
        # معاينة الخط
        self.font_preview = QLabel("معاينة النص - Preview Text - 123")
        self.font_preview.setStyleSheet("border: 1px solid #ccc; padding: 10px; background: white;")
        self.font_preview.setAlignment(Qt.AlignCenter)
        font_layout.addRow("المعاينة:", self.font_preview)
        
        # زر اختيار الخط
        font_button = QPushButton("اختيار خط مخصص...")
        font_button.clicked.connect(self.choose_custom_font)
        font_layout.addRow("", font_button)
        
        font_group.setLayout(font_layout)
        layout.addWidget(font_group)
        
        # مجموعة الثيمات
        theme_group = QGroupBox("الثيمات والألوان")
        theme_layout = QFormLayout()
        
        # اختيار الثيم
        self.theme_combo = QComboBox()
        themes = self.settings.get_themes()
        for key, value in themes.items():
            self.theme_combo.addItem(value, key)
        self.theme_combo.currentTextChanged.connect(self.preview_theme)
        theme_layout.addRow("الثيم:", self.theme_combo)
        
        # ألوان مخصصة
        colors_layout = QGridLayout()
        
        self.primary_color_btn = self.create_color_button("اللون الأساسي")
        self.secondary_color_btn = self.create_color_button("اللون الثانوي")
        self.success_color_btn = self.create_color_button("لون النجاح")
        self.warning_color_btn = self.create_color_button("لون التحذير")
        self.danger_color_btn = self.create_color_button("لون الخطر")
        
        colors_layout.addWidget(QLabel("اللون الأساسي:"), 0, 0)
        colors_layout.addWidget(self.primary_color_btn, 0, 1)
        colors_layout.addWidget(QLabel("اللون الثانوي:"), 0, 2)
        colors_layout.addWidget(self.secondary_color_btn, 0, 3)
        
        colors_layout.addWidget(QLabel("لون النجاح:"), 1, 0)
        colors_layout.addWidget(self.success_color_btn, 1, 1)
        colors_layout.addWidget(QLabel("لون التحذير:"), 1, 2)
        colors_layout.addWidget(self.warning_color_btn, 1, 3)
        
        colors_layout.addWidget(QLabel("لون الخطر:"), 2, 0)
        colors_layout.addWidget(self.danger_color_btn, 2, 1)
        
        theme_layout.addRow("الألوان المخصصة:", colors_layout)
        
        theme_group.setLayout(theme_layout)
        layout.addWidget(theme_group)
        
        # ربط الأحداث
        self.font_family_combo.currentTextChanged.connect(self.update_font_preview)
        self.font_size_spin.valueChanged.connect(self.update_font_preview)
        self.font_bold_check.toggled.connect(self.update_font_preview)
        
        layout.addStretch()
        self.appearance_tab.setLayout(layout)
        
    def create_window_tab(self):
        """إنشاء تبويب النافذة"""
        self.window_tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة أبعاد النافذة
        window_group = QGroupBox("أبعاد النافذة")
        window_layout = QFormLayout()
        
        # العرض
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2560)
        self.window_width_spin.setSuffix(" px")
        window_layout.addRow("العرض:", self.window_width_spin)
        
        # الارتفاع
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1440)
        self.window_height_spin.setSuffix(" px")
        window_layout.addRow("الارتفاع:", self.window_height_spin)
        
        # تكبير تلقائي
        self.window_maximized_check = QCheckBox("تكبير النافذة تلقائياً عند البدء")
        window_layout.addRow("", self.window_maximized_check)
        
        window_group.setLayout(window_layout)
        layout.addWidget(window_group)
        
        # مجموعة إعدادات عامة
        general_group = QGroupBox("إعدادات عامة")
        general_layout = QFormLayout()
        
        # عرض شاشة البداية
        self.show_splash_check = QCheckBox("عرض شاشة البداية عند التشغيل")
        general_layout.addRow("", self.show_splash_check)
        
        # حفظ تلقائي
        self.auto_save_check = QCheckBox("حفظ تلقائي للبيانات")
        general_layout.addRow("", self.auto_save_check)
        
        # نسخ احتياطية تلقائية
        self.auto_backup_check = QCheckBox("إنشاء نسخ احتياطية تلقائية")
        general_layout.addRow("", self.auto_backup_check)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        layout.addStretch()
        self.window_tab.setLayout(layout)
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        self.reports_tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات التقارير
        reports_group = QGroupBox("إعدادات التقارير")
        reports_layout = QFormLayout()
        
        # تنسيق التقرير الافتراضي
        self.report_format_combo = QComboBox()
        self.report_format_combo.addItems(["txt", "pdf", "excel"])
        reports_layout.addRow("التنسيق الافتراضي:", self.report_format_combo)
        
        # عدد العناصر في الصفحة
        self.reports_per_page_spin = QSpinBox()
        self.reports_per_page_spin.setRange(10, 1000)
        reports_layout.addRow("عناصر لكل صفحة:", self.reports_per_page_spin)
        
        # فتح التقارير تلقائياً
        self.auto_open_reports_check = QCheckBox("فتح التقارير تلقائياً بعد الإنشاء")
        reports_layout.addRow("", self.auto_open_reports_check)
        
        reports_group.setLayout(reports_layout)
        layout.addWidget(reports_group)
        
        layout.addStretch()
        self.reports_tab.setLayout(layout)
        
    def create_database_tab(self):
        """إنشاء تبويب قاعدة البيانات"""
        self.database_tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة النسخ الاحتياطية
        backup_group = QGroupBox("النسخ الاحتياطية")
        backup_layout = QFormLayout()
        
        # فترة النسخ الاحتياطي
        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setRange(1, 168)
        self.backup_interval_spin.setSuffix(" ساعة")
        backup_layout.addRow("فترة النسخ الاحتياطي:", self.backup_interval_spin)
        
        # عدد النسخ المحفوظة
        self.max_backup_files_spin = QSpinBox()
        self.max_backup_files_spin.setRange(1, 100)
        backup_layout.addRow("عدد النسخ المحفوظة:", self.max_backup_files_spin)
        
        # ضغط النسخ
        self.compress_backups_check = QCheckBox("ضغط ملفات النسخ الاحتياطية")
        backup_layout.addRow("", self.compress_backups_check)
        
        backup_group.setLayout(backup_layout)
        layout.addWidget(backup_group)
        
        layout.addStretch()
        self.database_tab.setLayout(layout)
        
    def create_security_tab(self):
        """إنشاء تبويب الأمان"""
        self.security_tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات الجلسة
        session_group = QGroupBox("إعدادات الجلسة")
        session_layout = QFormLayout()
        
        # مهلة الجلسة
        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 480)
        self.session_timeout_spin.setSuffix(" دقيقة")
        session_layout.addRow("مهلة انتهاء الجلسة:", self.session_timeout_spin)
        
        # محاولات تسجيل الدخول
        self.max_login_attempts_spin = QSpinBox()
        self.max_login_attempts_spin.setRange(1, 10)
        session_layout.addRow("محاولات تسجيل الدخول:", self.max_login_attempts_spin)
        
        # انتهاء كلمة المرور
        self.password_expiry_spin = QSpinBox()
        self.password_expiry_spin.setRange(30, 365)
        self.password_expiry_spin.setSuffix(" يوم")
        session_layout.addRow("انتهاء كلمة المرور:", self.password_expiry_spin)
        
        session_group.setLayout(session_layout)
        layout.addWidget(session_group)
        
        layout.addStretch()
        self.security_tab.setLayout(layout)
        
    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        self.advanced_tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات النظام
        system_group = QGroupBox("إعدادات النظام")
        system_layout = QFormLayout()
        
        # وضع التطوير
        self.debug_mode_check = QCheckBox("تفعيل وضع التطوير")
        system_layout.addRow("", self.debug_mode_check)
        
        # مستوى السجلات
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        system_layout.addRow("مستوى السجلات:", self.log_level_combo)
        
        # حجم الذاكرة المؤقتة
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(50, 1000)
        self.cache_size_spin.setSuffix(" MB")
        system_layout.addRow("حجم الذاكرة المؤقتة:", self.cache_size_spin)
        
        system_group.setLayout(system_layout)
        layout.addWidget(system_group)
        
        # أزرار الإدارة
        management_group = QGroupBox("إدارة الإعدادات")
        management_layout = QVBoxLayout()
        
        buttons_layout = QHBoxLayout()
        
        export_btn = QPushButton("تصدير الإعدادات")
        export_btn.clicked.connect(self.export_settings)
        
        import_btn = QPushButton("استيراد الإعدادات")
        import_btn.clicked.connect(self.import_settings)
        
        reset_btn = QPushButton("إعادة تعيين للافتراضي")
        reset_btn.clicked.connect(self.reset_to_defaults)
        
        buttons_layout.addWidget(export_btn)
        buttons_layout.addWidget(import_btn)
        buttons_layout.addWidget(reset_btn)
        
        management_layout.addLayout(buttons_layout)
        management_group.setLayout(management_layout)
        layout.addWidget(management_group)
        
        layout.addStretch()
        self.advanced_tab.setLayout(layout)
        
    def create_buttons(self, layout):
        """إنشاء أزرار النافذة"""
        buttons_layout = QHBoxLayout()
        
        # زر المعاينة
        self.preview_btn = QPushButton("معاينة")
        self.preview_btn.clicked.connect(self.preview_settings)
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_settings)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        # زر تطبيق
        self.apply_btn = QPushButton("تطبيق")
        self.apply_btn.clicked.connect(self.apply_settings)
        
        buttons_layout.addWidget(self.preview_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_color_button(self, text):
        """إنشاء زر اختيار اللون"""
        button = QPushButton(text)
        button.setFixedSize(100, 30)
        button.clicked.connect(lambda: self.choose_color(button))
        return button
        
    def choose_color(self, button):
        """اختيار لون"""
        color = QColorDialog.getColor()
        if color.isValid():
            button.setStyleSheet(f"background-color: {color.name()};")
            button.color = color.name()
            
    def choose_custom_font(self):
        """اختيار خط مخصص"""
        current_font = QFont(
            self.font_family_combo.currentText(),
            self.font_size_spin.value()
        )
        current_font.setBold(self.font_bold_check.isChecked())
        
        font, ok = QFontDialog.getFont(current_font, self)
        if ok:
            self.font_family_combo.setCurrentText(font.family())
            self.font_size_spin.setValue(font.pointSize())
            self.font_bold_check.setChecked(font.bold())
            self.update_font_preview()
            
    def update_font_preview(self):
        """تحديث معاينة الخط"""
        font = QFont(
            self.font_family_combo.currentText(),
            self.font_size_spin.value()
        )
        font.setBold(self.font_bold_check.isChecked())
        self.font_preview.setFont(font)
        
    def preview_theme(self):
        """معاينة الثيم"""
        theme_name = self.theme_combo.currentData()
        if theme_name:
            # تطبيق مؤقت للثيم
            pass
            
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        # إعدادات الخط
        self.font_family_combo.setCurrentText(self.settings.get_setting('font_family'))
        self.font_size_spin.setValue(self.settings.get_setting('font_size'))
        self.font_bold_check.setChecked(self.settings.get_setting('font_bold'))
        
        # إعدادات النافذة
        self.window_width_spin.setValue(self.settings.get_setting('window_width'))
        self.window_height_spin.setValue(self.settings.get_setting('window_height'))
        self.window_maximized_check.setChecked(self.settings.get_setting('window_maximized'))
        
        # إعدادات عامة
        self.show_splash_check.setChecked(self.settings.get_setting('show_splash'))
        self.auto_save_check.setChecked(self.settings.get_setting('auto_save'))
        self.auto_backup_check.setChecked(self.settings.get_setting('auto_backup'))
        
        # إعدادات التقارير
        self.report_format_combo.setCurrentText(self.settings.get_setting('default_report_format'))
        self.reports_per_page_spin.setValue(self.settings.get_setting('reports_per_page'))
        self.auto_open_reports_check.setChecked(self.settings.get_setting('auto_open_reports'))
        
        # إعدادات قاعدة البيانات
        self.backup_interval_spin.setValue(self.settings.get_setting('backup_interval'))
        self.max_backup_files_spin.setValue(self.settings.get_setting('max_backup_files'))
        self.compress_backups_check.setChecked(self.settings.get_setting('compress_backups'))
        
        # إعدادات الأمان
        self.session_timeout_spin.setValue(self.settings.get_setting('session_timeout'))
        self.max_login_attempts_spin.setValue(self.settings.get_setting('max_login_attempts'))
        self.password_expiry_spin.setValue(self.settings.get_setting('password_expiry'))
        
        # إعدادات متقدمة
        self.debug_mode_check.setChecked(self.settings.get_setting('debug_mode'))
        self.log_level_combo.setCurrentText(self.settings.get_setting('log_level'))
        self.cache_size_spin.setValue(self.settings.get_setting('cache_size'))
        
        # تحديث معاينة الخط
        self.update_font_preview()
        
        # تحديث ألوان الأزرار
        self.update_color_buttons()
        
    def update_color_buttons(self):
        """تحديث ألوان الأزرار"""
        colors = self.settings.get_color_settings()
        
        self.primary_color_btn.setStyleSheet(f"background-color: {colors['primary']};")
        self.primary_color_btn.color = colors['primary']
        
        self.secondary_color_btn.setStyleSheet(f"background-color: {colors['secondary']};")
        self.secondary_color_btn.color = colors['secondary']
        
        self.success_color_btn.setStyleSheet(f"background-color: {colors['success']};")
        self.success_color_btn.color = colors['success']
        
        self.warning_color_btn.setStyleSheet(f"background-color: {colors['warning']};")
        self.warning_color_btn.color = colors['warning']
        
        self.danger_color_btn.setStyleSheet(f"background-color: {colors['danger']};")
        self.danger_color_btn.color = colors['danger']
        
    def collect_settings(self):
        """جمع الإعدادات من الواجهة"""
        new_settings = {
            # إعدادات الخط
            'font_family': self.font_family_combo.currentText(),
            'font_size': self.font_size_spin.value(),
            'font_bold': self.font_bold_check.isChecked(),
            
            # إعدادات النافذة
            'window_width': self.window_width_spin.value(),
            'window_height': self.window_height_spin.value(),
            'window_maximized': self.window_maximized_check.isChecked(),
            
            # إعدادات عامة
            'show_splash': self.show_splash_check.isChecked(),
            'auto_save': self.auto_save_check.isChecked(),
            'auto_backup': self.auto_backup_check.isChecked(),
            
            # إعدادات التقارير
            'default_report_format': self.report_format_combo.currentText(),
            'reports_per_page': self.reports_per_page_spin.value(),
            'auto_open_reports': self.auto_open_reports_check.isChecked(),
            
            # إعدادات قاعدة البيانات
            'backup_interval': self.backup_interval_spin.value(),
            'max_backup_files': self.max_backup_files_spin.value(),
            'compress_backups': self.compress_backups_check.isChecked(),
            
            # إعدادات الأمان
            'session_timeout': self.session_timeout_spin.value(),
            'max_login_attempts': self.max_login_attempts_spin.value(),
            'password_expiry': self.password_expiry_spin.value(),
            
            # إعدادات متقدمة
            'debug_mode': self.debug_mode_check.isChecked(),
            'log_level': self.log_level_combo.currentText(),
            'cache_size': self.cache_size_spin.value(),
        }
        
        # إضافة الألوان إذا تم تغييرها
        if hasattr(self.primary_color_btn, 'color'):
            new_settings['primary_color'] = self.primary_color_btn.color
        if hasattr(self.secondary_color_btn, 'color'):
            new_settings['secondary_color'] = self.secondary_color_btn.color
        if hasattr(self.success_color_btn, 'color'):
            new_settings['success_color'] = self.success_color_btn.color
        if hasattr(self.warning_color_btn, 'color'):
            new_settings['warning_color'] = self.warning_color_btn.color
        if hasattr(self.danger_color_btn, 'color'):
            new_settings['danger_color'] = self.danger_color_btn.color
            
        return new_settings
        
    def preview_settings(self):
        """معاينة الإعدادات"""
        new_settings = self.collect_settings()
        
        # التحقق من صحة الإعدادات
        self.settings.update_settings(new_settings)
        errors = self.settings.validate_settings()
        
        if errors:
            QMessageBox.warning(self, "خطأ في الإعدادات", "\n".join(errors))
            return
            
        # تطبيق مؤقت للإعدادات
        QMessageBox.information(self, "معاينة", "سيتم تطبيق الإعدادات مؤقتاً للمعاينة")
        
    def apply_settings(self):
        """تطبيق الإعدادات"""
        new_settings = self.collect_settings()
        
        # التحقق من صحة الإعدادات
        self.settings.update_settings(new_settings)
        errors = self.settings.validate_settings()
        
        if errors:
            QMessageBox.warning(self, "خطأ في الإعدادات", "\n".join(errors))
            return
            
        # حفظ الإعدادات
        success, message = self.settings.save_settings(
            self.user_data['id'] if self.user_data else None
        )
        
        if success:
            self.settings_changed.emit()
            QMessageBox.information(self, "نجح", "تم تطبيق الإعدادات بنجاح")
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ الإعدادات:\n{message}")
            
    def save_settings(self):
        """حفظ الإعدادات والإغلاق"""
        self.apply_settings()
        self.accept()
        
    def export_settings(self):
        """تصدير الإعدادات"""
        filepath, _ = QFileDialog.getSaveFileName(
            self, "تصدير الإعدادات", "settings.json", "JSON Files (*.json)"
        )
        
        if filepath:
            success, message = self.settings.export_settings(filepath)
            if success:
                QMessageBox.information(self, "نجح", message)
            else:
                QMessageBox.warning(self, "خطأ", message)
                
    def import_settings(self):
        """استيراد الإعدادات"""
        filepath, _ = QFileDialog.getOpenFileName(
            self, "استيراد الإعدادات", "", "JSON Files (*.json)"
        )
        
        if filepath:
            success, message = self.settings.import_settings(filepath)
            if success:
                self.load_current_settings()
                QMessageBox.information(self, "نجح", message)
            else:
                QMessageBox.warning(self, "خطأ", message)
                
    def reset_to_defaults(self):
        """إعادة تعيين للإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, "تأكيد", 
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.settings.reset_to_defaults()
            self.load_current_settings()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات للقيم الافتراضية")
            
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QTabWidget::pane {{
                border: 1px solid #BDC3C7;
                background-color: white;
            }}
            
            QTabBar::tab {{
                background-color: #ECF0F1;
                padding: 8px 15px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
