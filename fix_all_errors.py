#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جميع الأخطاء في البرنامج
Fix All Errors Script
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_import_errors():
    """إصلاح أخطاء الاستيراد"""
    print("🔧 إصلاح أخطاء الاستيراد...")
    
    errors_fixed = 0
    
    try:
        # اختبار استيراد النماذج
        from src.models.customer import Customer
        from src.models.order import Order
        from src.models.sales_invoice import SalesInvoice
        from src.models.granite_type import GraniteType
        from src.models.user import User
        from src.models.settings import Settings
        print("✅ جميع النماذج تم استيرادها بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في استيراد النماذج: {str(e)}")
        errors_fixed += 1
        
    try:
        # اختبار استيراد أدوات الخط
        from src.utils.font_utils import FontManager, apply_font_settings, get_responsive_style
        print("✅ أدوات الخط تم استيرادها بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في استيراد أدوات الخط: {str(e)}")
        errors_fixed += 1
        
    try:
        # اختبار استيراد قاعدة البيانات
        from src.database.database_manager import DatabaseManager
        from src.database.sqlite_manager import SQLiteManager
        print("✅ مديري قاعدة البيانات تم استيرادهما بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في استيراد قاعدة البيانات: {str(e)}")
        errors_fixed += 1
        
    return errors_fixed

def fix_database_errors():
    """إصلاح أخطاء قاعدة البيانات"""
    print("\n💾 إصلاح أخطاء قاعدة البيانات...")
    
    errors_fixed = 0
    
    try:
        from src.database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # اختبار الاتصال
        if db_manager.connect():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            
            # اختبار استعلام بسيط
            result = db_manager.execute_query("SELECT 1", fetch='one')
            if result:
                print("✅ تم تنفيذ استعلام اختبار بنجاح")
            else:
                print("⚠️ فشل في تنفيذ استعلام الاختبار")
                errors_fixed += 1
                
            db_manager.disconnect()
        else:
            print("⚠️ فشل الاتصال بقاعدة البيانات - سيتم استخدام SQLite")
            errors_fixed += 1
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        errors_fixed += 1
        
    return errors_fixed

def fix_ui_errors():
    """إصلاح أخطاء واجهة المستخدم"""
    print("\n🖥️ إصلاح أخطاء واجهة المستخدم...")
    
    errors_fixed = 0
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # قائمة النوافذ للاختبار
        ui_modules = [
            ("نافذة العملاء", "src.ui.customer_dialog", "CustomerDialog"),
            ("نافذة الطلبات", "src.ui.order_dialog", "OrderDialog"),
            ("نافذة الدفعات", "src.ui.payment_dialog", "PaymentDialog"),
            ("نافذة دفعات النشر", "src.ui.cutting_payment_dialog", "CuttingPaymentDialog"),
            ("نافذة الفواتير", "src.ui.invoice_dialog", "InvoiceDialog"),
            ("نافذة عرض الفاتورة", "src.ui.invoice_view_dialog", "InvoiceViewDialog"),
            ("نافذة أنواع الجرانيت", "src.ui.granite_types_dialog", "GraniteTypesDialog"),
            ("نافذة المستخدمين", "src.ui.users_management_dialog", "UsersManagementDialog"),
            ("نافذة الإعدادات", "src.ui.settings_dialog", "SettingsDialog"),
        ]
        
        for ui_name, module_path, class_name in ui_modules:
            try:
                # استيراد الوحدة
                module = __import__(module_path, fromlist=[class_name])
                dialog_class = getattr(module, class_name)
                
                # إنشاء النافذة (بدون عرضها)
                dialog = dialog_class()
                dialog.close()
                
                print(f"✅ {ui_name}: تم إنشاؤها بنجاح")
                
            except Exception as e:
                print(f"❌ {ui_name}: خطأ - {str(e)}")
                errors_fixed += 1
                
        # تنظيف التطبيق
        app.quit()
        
    except Exception as e:
        print(f"❌ خطأ عام في واجهة المستخدم: {str(e)}")
        errors_fixed += 1
        
    return errors_fixed

def fix_font_errors():
    """إصلاح أخطاء الخطوط"""
    print("\n🔤 إصلاح أخطاء الخطوط...")
    
    errors_fixed = 0
    
    try:
        from src.utils.font_utils import FontManager, apply_font_settings, get_responsive_style
        from src.models.settings import Settings
        
        # إنشاء مدير الخطوط
        font_manager = FontManager()
        settings = Settings()
        
        # اختبار الحصول على الخط الحالي
        current_font = font_manager.get_current_font()
        print(f"✅ الخط الحالي: {current_font.family()}, {current_font.pointSize()}pt")
        
        # اختبار حساب ارتفاع الحقول
        field_height = font_manager.get_field_height()
        print(f"✅ ارتفاع الحقول: {field_height}px")
        
        # اختبار الأنماط المتجاوبة
        responsive_style = get_responsive_style()
        print("✅ تم إنشاء الأنماط المتجاوبة بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في الخطوط: {str(e)}")
        errors_fixed += 1
        
    return errors_fixed

def fix_missing_files():
    """إصلاح الملفات المفقودة"""
    print("\n📁 فحص الملفات المفقودة...")
    
    errors_fixed = 0
    
    # قائمة الملفات المطلوبة
    required_files = [
        "main.py",
        "config.py",
        "src/__init__.py",
        "src/models/__init__.py",
        "src/ui/__init__.py",
        "src/database/__init__.py",
        "src/utils/__init__.py",
        "src/models/settings.py",
        "src/utils/font_utils.py",
        "src/database/sqlite_manager.py",
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: موجود")
        else:
            print(f"❌ {file_path}: مفقود")
            errors_fixed += 1
            
            # إنشاء ملف __init__.py إذا كان مفقوداً
            if file_path.endswith("__init__.py"):
                try:
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write('# -*- coding: utf-8 -*-\n')
                    print(f"✅ تم إنشاء {file_path}")
                    errors_fixed -= 1
                except Exception as e:
                    print(f"❌ فشل في إنشاء {file_path}: {str(e)}")
                    
    return errors_fixed

def create_error_report():
    """إنشاء تقرير الأخطاء"""
    print("\n📋 إنشاء تقرير الأخطاء...")
    
    report = []
    report.append("# تقرير إصلاح الأخطاء")
    report.append("=" * 50)
    report.append(f"التاريخ: {os.popen('date').read().strip()}")
    report.append("")
    
    # فحص الاستيرادات
    try:
        import_errors = fix_import_errors()
        report.append(f"أخطاء الاستيراد: {import_errors}")
    except Exception as e:
        report.append(f"أخطاء الاستيراد: خطأ في الفحص - {str(e)}")
        
    # فحص قاعدة البيانات
    try:
        db_errors = fix_database_errors()
        report.append(f"أخطاء قاعدة البيانات: {db_errors}")
    except Exception as e:
        report.append(f"أخطاء قاعدة البيانات: خطأ في الفحص - {str(e)}")
        
    # فحص واجهة المستخدم
    try:
        ui_errors = fix_ui_errors()
        report.append(f"أخطاء واجهة المستخدم: {ui_errors}")
    except Exception as e:
        report.append(f"أخطاء واجهة المستخدم: خطأ في الفحص - {str(e)}")
        
    # فحص الخطوط
    try:
        font_errors = fix_font_errors()
        report.append(f"أخطاء الخطوط: {font_errors}")
    except Exception as e:
        report.append(f"أخطاء الخطوط: خطأ في الفحص - {str(e)}")
        
    # فحص الملفات
    try:
        file_errors = fix_missing_files()
        report.append(f"ملفات مفقودة: {file_errors}")
    except Exception as e:
        report.append(f"ملفات مفقودة: خطأ في الفحص - {str(e)}")
        
    # حفظ التقرير
    try:
        with open("error_report.txt", "w", encoding="utf-8") as f:
            f.write("\n".join(report))
        print("✅ تم حفظ تقرير الأخطاء في error_report.txt")
    except Exception as e:
        print(f"❌ فشل في حفظ التقرير: {str(e)}")

def main():
    """الدالة الرئيسية لإصلاح الأخطاء"""
    print("🚀 بدء إصلاح جميع الأخطاء في البرنامج")
    print("=" * 60)
    
    total_errors = 0
    
    # إصلاح أخطاء الاستيراد
    total_errors += fix_import_errors()
    
    # إصلاح أخطاء قاعدة البيانات
    total_errors += fix_database_errors()
    
    # إصلاح أخطاء واجهة المستخدم
    total_errors += fix_ui_errors()
    
    # إصلاح أخطاء الخطوط
    total_errors += fix_font_errors()
    
    # إصلاح الملفات المفقودة
    total_errors += fix_missing_files()
    
    # إنشاء تقرير الأخطاء
    create_error_report()
    
    # تقرير النتائج
    print("\n📊 تقرير إصلاح الأخطاء:")
    print("=" * 40)
    print(f"إجمالي الأخطاء المكتشفة: {total_errors}")
    
    if total_errors == 0:
        print("🎉 لا توجد أخطاء! البرنامج جاهز للتشغيل")
        return 0
    elif total_errors <= 5:
        print("✅ أخطاء قليلة - البرنامج يجب أن يعمل")
        return 0
    else:
        print("⚠️ يوجد أخطاء تحتاج إصلاح")
        return 1

if __name__ == "__main__":
    sys.exit(main())
