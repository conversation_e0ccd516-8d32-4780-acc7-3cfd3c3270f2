# -*- coding: utf-8 -*-
"""
نافذة إدارة المستخدمين
Users Management Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QPushButton, QMessageBox, QLabel,
                            QGroupBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QComboBox,
                            QCheckBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

import config
from src.models.user import User

class UsersManagementDialog(QDialog):
    """نافذة إدارة المستخدمين"""
    
    def __init__(self, parent=None, database_manager=None, user_data=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.user_data = user_data
        self.user_model = User(database_manager)
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المستخدمين")
        self.setFixedSize(900, 600)
        self.setModal(True)
        
        layout = QHBoxLayout()
        
        # الجانب الأيسر - قائمة المستخدمين
        left_layout = QVBoxLayout()
        
        # عنوان القائمة
        list_label = QLabel("المستخدمين المسجلين")
        list_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        left_layout.addWidget(list_label)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        
        headers = ["المعرف", "اسم المستخدم", "الاسم الكامل", "الدور", "الحالة"]
        self.users_table.setHorizontalHeaderLabels(headers)
        
        # إعدادات الجدول
        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        
        # أحداث الجدول
        self.users_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        
        left_layout.addWidget(self.users_table)
        
        # أزرار إدارة القائمة
        list_buttons_layout = QHBoxLayout()
        
        self.delete_btn = QPushButton("حذف المستخدم")
        self.delete_btn.clicked.connect(self.delete_user)
        self.delete_btn.setEnabled(False)
        
        self.reset_password_btn = QPushButton("إعادة تعيين كلمة المرور")
        self.reset_password_btn.clicked.connect(self.reset_password)
        self.reset_password_btn.setEnabled(False)
        
        list_buttons_layout.addWidget(self.delete_btn)
        list_buttons_layout.addWidget(self.reset_password_btn)
        
        left_layout.addLayout(list_buttons_layout)
        
        # الجانب الأيمن - نموذج الإدخال
        right_layout = QVBoxLayout()
        
        # نموذج إضافة/تعديل
        form_group = QGroupBox("إضافة/تعديل مستخدم")
        form_layout = QFormLayout()
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم (مطلوب)")
        form_layout.addRow("اسم المستخدم:", self.username_input)
        
        # كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("كلمة المرور (مطلوبة للمستخدم الجديد)")
        form_layout.addRow("كلمة المرور:", self.password_input)
        
        # تأكيد كلمة المرور
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setPlaceholderText("تأكيد كلمة المرور")
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_input)
        
        # الاسم الكامل
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText("الاسم الكامل (مطلوب)")
        form_layout.addRow("الاسم الكامل:", self.full_name_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        # الدور
        self.role_combo = QComboBox()
        for role_key, role_name in config.USER_ROLES.items():
            self.role_combo.addItem(role_name, role_key)
        form_layout.addRow("الدور:", self.role_combo)
        
        # الحالة
        self.is_active_check = QCheckBox("المستخدم نشط")
        self.is_active_check.setChecked(True)
        form_layout.addRow("", self.is_active_check)
        
        form_group.setLayout(form_layout)
        right_layout.addWidget(form_group)
        
        # أزرار النموذج
        form_buttons_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("مسح النموذج")
        self.clear_btn.clicked.connect(self.clear_form)
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.clicked.connect(self.save_user)
        
        form_buttons_layout.addWidget(self.clear_btn)
        form_buttons_layout.addWidget(self.save_btn)
        
        right_layout.addLayout(form_buttons_layout)
        
        # إحصائيات
        stats_group = QGroupBox("إحصائيات")
        stats_layout = QVBoxLayout()
        
        self.total_users_label = QLabel("إجمالي المستخدمين: 0")
        self.active_users_label = QLabel("المستخدمين النشطين: 0")
        self.admin_users_label = QLabel("المديرين: 0")
        
        stats_layout.addWidget(self.total_users_label)
        stats_layout.addWidget(self.active_users_label)
        stats_layout.addWidget(self.admin_users_label)
        
        stats_group.setLayout(stats_layout)
        right_layout.addWidget(stats_group)
        
        right_layout.addStretch()
        
        # تجميع الجوانب
        layout.addLayout(left_layout, 2)
        layout.addLayout(right_layout, 1)
        
        # أزرار النافذة
        main_layout = QVBoxLayout()
        main_layout.addLayout(layout)
        
        window_buttons_layout = QHBoxLayout()
        
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.clicked.connect(self.accept)
        
        window_buttons_layout.addStretch()
        window_buttons_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(window_buttons_layout)
        
        self.setLayout(main_layout)
        self.apply_styles()
        
        # متغيرات التحكم
        self.current_user_id = None
        
    def load_data(self):
        """تحميل البيانات"""
        users = self.user_model.get_all_users()
        self.populate_table(users)
        self.update_statistics(users)
        
    def populate_table(self, users):
        """ملء الجدول بالبيانات"""
        self.users_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            # تحويل الدور إلى نص عربي
            role_text = config.USER_ROLES.get(user[4], user[4])
            
            # تحويل الحالة إلى نص
            status_text = "نشط" if user[6] else "غير نشط"
            
            items = [
                str(user[0]),  # ID
                str(user[1]),  # username
                str(user[2]),  # full_name
                role_text,     # role
                status_text    # is_active
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                if col == 0:  # المعرف
                    item.setTextAlignment(Qt.AlignCenter)
                elif col == 4:  # الحالة
                    item.setTextAlignment(Qt.AlignCenter)
                    # تلوين الحالة
                    if status_text == "نشط":
                        item.setBackground(Qt.green)
                    else:
                        item.setBackground(Qt.red)
                        
                self.users_table.setItem(row, col, item)
                
    def update_statistics(self, users):
        """تحديث الإحصائيات"""
        total_users = len(users)
        active_users = sum(1 for user in users if user[6])
        admin_users = sum(1 for user in users if user[4] == 'admin')
        
        self.total_users_label.setText(f"إجمالي المستخدمين: {total_users}")
        self.active_users_label.setText(f"المستخدمين النشطين: {active_users}")
        self.admin_users_label.setText(f"المديرين: {admin_users}")
        
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        selected_rows = self.users_table.selectionModel().selectedRows()
        
        if selected_rows:
            self.delete_btn.setEnabled(True)
            self.reset_password_btn.setEnabled(True)
            
            # تحميل البيانات في النموذج للتعديل
            row = selected_rows[0].row()
            user_id = int(self.users_table.item(row, 0).text())
            
            # منع تعديل المستخدم الحالي
            if user_id != self.user_data['id']:
                self.load_user_for_editing(user_id)
            else:
                self.delete_btn.setEnabled(False)
        else:
            self.delete_btn.setEnabled(False)
            self.reset_password_btn.setEnabled(False)
            self.clear_form()
            
    def load_user_for_editing(self, user_id):
        """تحميل المستخدم للتعديل"""
        user = self.user_model.get_user_by_id(user_id)
        if user:
            self.current_user_id = user_id
            self.username_input.setText(user[1])
            self.full_name_input.setText(user[2])
            self.email_input.setText(user[3] if user[3] else "")
            
            # تعيين الدور
            role_index = self.role_combo.findData(user[4])
            if role_index >= 0:
                self.role_combo.setCurrentIndex(role_index)
                
            self.is_active_check.setChecked(user[6])
            
            # مسح كلمات المرور
            self.password_input.clear()
            self.confirm_password_input.clear()
            
            # تغيير نص الزر
            self.save_btn.setText("تحديث")
            
    def clear_form(self):
        """مسح النموذج"""
        self.current_user_id = None
        self.username_input.clear()
        self.password_input.clear()
        self.confirm_password_input.clear()
        self.full_name_input.clear()
        self.email_input.clear()
        self.role_combo.setCurrentIndex(0)
        self.is_active_check.setChecked(True)
        
        # إعادة تعيين نص الزر
        self.save_btn.setText("حفظ")
        
        # إلغاء التحديد
        self.users_table.clearSelection()
        
    def save_user(self):
        """حفظ المستخدم"""
        # التحقق من كلمات المرور
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()
        
        if not self.current_user_id and not password:
            QMessageBox.warning(self, "خطأ", "كلمة المرور مطلوبة للمستخدم الجديد")
            return
            
        if password and password != confirm_password:
            QMessageBox.warning(self, "خطأ", "كلمات المرور غير متطابقة")
            return
            
        # جمع البيانات
        user_data = {
            'username': self.username_input.text().strip(),
            'full_name': self.full_name_input.text().strip(),
            'email': self.email_input.text().strip(),
            'role': self.role_combo.currentData(),
            'is_active': self.is_active_check.isChecked()
        }
        
        if password:
            user_data['password'] = password
            
        # التحقق من صحة البيانات
        errors = self.user_model.validate_user_data(user_data)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
            
        # حفظ البيانات
        try:
            if self.current_user_id:
                # تحديث
                success, message = self.user_model.update_user(
                    self.current_user_id, user_data, self.user_data['id']
                )
            else:
                # إضافة جديد
                success, user_id, message = self.user_model.create_user(
                    user_data, self.user_data['id']
                )
                
            if success:
                QMessageBox.information(self, "نجح", message)
                self.load_data()
                self.clear_form()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات:\n{str(e)}")
            
    def delete_user(self):
        """حذف المستخدم المحدد"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            user_id = int(self.users_table.item(current_row, 0).text())
            username = self.users_table.item(current_row, 1).text()
            
            # منع حذف المستخدم الحالي
            if user_id == self.user_data['id']:
                QMessageBox.warning(self, "خطأ", "لا يمكن حذف المستخدم الحالي")
                return
                
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم:\n{username}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    success, message = self.user_model.delete_user(
                        user_id, self.user_data['id']
                    )
                    
                    if success:
                        QMessageBox.information(self, "نجح", message)
                        self.load_data()
                        self.clear_form()
                    else:
                        QMessageBox.warning(self, "خطأ", message)
                        
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في حذف البيانات:\n{str(e)}")
                    
    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            user_id = int(self.users_table.item(current_row, 0).text())
            username = self.users_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(
                self, "تأكيد إعادة التعيين",
                f"هل تريد إعادة تعيين كلمة مرور المستخدم:\n{username}؟\n\nستصبح كلمة المرور الجديدة: 123456",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    success, message = self.user_model.reset_password(
                        user_id, "123456", self.user_data['id']
                    )
                    
                    if success:
                        QMessageBox.information(self, "نجح", f"{message}\n\nكلمة المرور الجديدة: 123456")
                    else:
                        QMessageBox.warning(self, "خطأ", message)
                        
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في إعادة تعيين كلمة المرور:\n{str(e)}")
                    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QPushButton:disabled {{
                background-color: #BDC3C7;
                color: #7F8C8D;
            }}
            
            QLineEdit, QComboBox {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
            
            QTableWidget {{
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }}
            
            QTableWidget::item:selected {{
                background-color: {config.COLORS['secondary']};
                color: white;
            }}
        """)
