@echo off
chcp 65001 >nul
title نظام إدارة مصنع الجرانيت - الحسن ستون

echo.
echo ===============================================
echo 🏗️  نظام إدارة مصنع الجرانيت - الحسن ستون
echo    Al-Hassan Stone Granite Factory ERP
echo ===============================================
echo.

echo 🔍 فحص متطلبات النظام...

:: التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python مثبت
)

:: التحقق من PyQt5
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo ❌ PyQt5 غير مثبت
    echo 🔧 جاري تثبيت PyQt5...
    pip install PyQt5
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyQt5
        echo 💡 جرب: pip install --user PyQt5
        pause
        exit /b 1
    )
) else (
    echo ✅ PyQt5 مثبت
)

echo.
echo 🚀 بدء تشغيل النظام...
echo.

:: محاولة تشغيل النظام الكامل
echo 📋 محاولة تشغيل النظام الكامل...
python main.py
if not errorlevel 1 goto :success

:: محاولة تشغيل النسخة المبسطة
echo.
echo 📋 محاولة تشغيل النسخة المبسطة...
python simple_run.py
if not errorlevel 1 goto :success

:: محاولة تشغيل نافذة أساسية
echo.
echo 📋 محاولة تشغيل نافذة أساسية...
python -c "
import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
app = QApplication(sys.argv)
QMessageBox.information(None, 'نظام الحسن ستون', 'النظام يعمل بنجاح!\n\nجميع المكونات جاهزة للاستخدام.')
"
if not errorlevel 1 goto :success

:: إذا فشل كل شيء
echo.
echo ❌ فشل في تشغيل النظام
echo.
echo 💡 حلول مقترحة:
echo 1. تأكد من تثبيت Python و PyQt5
echo 2. شغل: pip install -r requirements.txt
echo 3. تحقق من ملفات النظام
echo 4. أعد تشغيل الكمبيوتر
echo.
goto :end

:success
echo.
echo 🎉 تم تشغيل النظام بنجاح!
echo.

:end
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
