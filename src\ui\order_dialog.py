# -*- coding: utf-8 -*-
"""
نافذة إضافة/تعديل الطلبات
Order Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QTextEdit, QDoubleSpinBox, QPushButton,
                            QMessageBox, QLabel, QGroupBox, QComboBox, QDateEdit,
                            QSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

import config
from src.models.order import Order
from src.models.customer import Customer
from src.models.granite_type import GraniteType

class OrderDialog(QDialog):
    """نافذة إضافة/تعديل الطلبات"""
    
    def __init__(self, parent=None, database_manager=None, user_data=None, order_id=None):
        super().__init__(parent)
        self.database_manager = database_manager
        self.user_data = user_data
        self.order_id = order_id
        self.order_model = Order(database_manager)
        self.customer_model = Customer(database_manager)
        self.granite_type_model = GraniteType(database_manager)
        
        self.setup_ui()
        self.load_data()
        if order_id:
            self.load_order_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة طلب جديد" if not self.order_id else "تعديل الطلب")
        self.setFixedSize(600, 550)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # معلومات الطلب
        order_group = QGroupBox("معلومات الطلب")
        order_layout = QFormLayout()
        
        # العميل
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        order_layout.addRow("العميل:", self.customer_combo)
        
        # تاريخ الطلب
        self.order_date = QDateEdit()
        self.order_date.setDate(QDate.currentDate())
        self.order_date.setCalendarPopup(True)
        order_layout.addRow("تاريخ الطلب:", self.order_date)
        
        # تاريخ التسليم المطلوب
        self.delivery_date = QDateEdit()
        self.delivery_date.setDate(QDate.currentDate().addDays(7))
        self.delivery_date.setCalendarPopup(True)
        order_layout.addRow("تاريخ التسليم المطلوب:", self.delivery_date)
        
        order_group.setLayout(order_layout)
        layout.addWidget(order_group)
        
        # مواصفات المنتج
        product_group = QGroupBox("مواصفات المنتج")
        product_layout = QFormLayout()
        
        # نوع الجرانيت
        self.granite_type_combo = QComboBox()
        product_layout.addRow("نوع الجرانيت:", self.granite_type_combo)
        
        # الأبعاد
        self.length_input = QDoubleSpinBox()
        self.length_input.setRange(1, 1000)
        self.length_input.setDecimals(1)
        self.length_input.setSuffix(" سم")
        self.length_input.valueChanged.connect(self.calculate_area_and_total)
        product_layout.addRow("الطول:", self.length_input)
        
        self.width_input = QDoubleSpinBox()
        self.width_input.setRange(1, 1000)
        self.width_input.setDecimals(1)
        self.width_input.setSuffix(" سم")
        self.width_input.valueChanged.connect(self.calculate_area_and_total)
        product_layout.addRow("العرض:", self.width_input)
        
        self.thickness_input = QDoubleSpinBox()
        self.thickness_input.setRange(1, 50)
        self.thickness_input.setDecimals(1)
        self.thickness_input.setSuffix(" سم")
        product_layout.addRow("السمك:", self.thickness_input)
        
        # الكمية
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(1, 1000)
        self.quantity_input.valueChanged.connect(self.calculate_area_and_total)
        product_layout.addRow("الكمية:", self.quantity_input)
        
        product_group.setLayout(product_layout)
        layout.addWidget(product_group)
        
        # معلومات السعر
        price_group = QGroupBox("معلومات السعر")
        price_layout = QFormLayout()
        
        # السعر لكل متر مربع
        self.unit_price_input = QDoubleSpinBox()
        self.unit_price_input.setRange(0, 10000)
        self.unit_price_input.setDecimals(2)
        self.unit_price_input.setSuffix(" جنيه/م²")
        self.unit_price_input.valueChanged.connect(self.calculate_area_and_total)
        price_layout.addRow("السعر/م²:", self.unit_price_input)
        
        # المساحة الإجمالية (للعرض فقط)
        self.total_area_label = QLabel("0.00 م²")
        self.total_area_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        price_layout.addRow("المساحة الإجمالية:", self.total_area_label)
        
        # الإجمالي (للعرض فقط)
        self.total_amount_label = QLabel("0.00 جنيه")
        self.total_amount_label.setStyleSheet("font-weight: bold; color: #E74C3C; font-size: 14px;")
        price_layout.addRow("الإجمالي:", self.total_amount_label)
        
        price_group.setLayout(price_layout)
        layout.addWidget(price_group)
        
        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات خاصة بالطلب...")
        notes_layout.addWidget(self.notes_input)
        
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الطلب")
        self.save_btn.clicked.connect(self.save_order)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        self.apply_styles()
        
    def load_data(self):
        """تحميل البيانات الأساسية"""
        # تحميل العملاء
        customers = self.customer_model.get_all_customers()
        self.customer_combo.clear()
        for customer in customers:
            self.customer_combo.addItem(customer[1], customer[0])  # name, id
            
        # تحميل أنواع الجرانيت
        granite_types = self.granite_type_model.get_all_granite_types()
        self.granite_type_combo.clear()
        for granite_type in granite_types:
            self.granite_type_combo.addItem(granite_type[1], granite_type[0])  # name, id
            
    def load_order_data(self):
        """تحميل بيانات الطلب للتعديل"""
        order = self.order_model.get_order_by_id(self.order_id)
        if order:
            # تعيين العميل
            customer_index = self.customer_combo.findData(order[1])
            if customer_index >= 0:
                self.customer_combo.setCurrentIndex(customer_index)
                
            # تعيين التواريخ
            if order[2]:
                date = QDate.fromString(str(order[2])[:10], "yyyy-MM-dd")
                self.order_date.setDate(date)
            if order[3]:
                date = QDate.fromString(str(order[3])[:10], "yyyy-MM-dd")
                self.delivery_date.setDate(date)
                
            # تعيين نوع الجرانيت
            granite_index = self.granite_type_combo.findData(order[4])
            if granite_index >= 0:
                self.granite_type_combo.setCurrentIndex(granite_index)
                
            # تعيين الأبعاد والكمية
            self.length_input.setValue(order[5] if order[5] else 0)
            self.width_input.setValue(order[6] if order[6] else 0)
            self.thickness_input.setValue(order[7] if order[7] else 0)
            self.quantity_input.setValue(order[8] if order[8] else 1)
            self.unit_price_input.setValue(order[9] if order[9] else 0)
            
            # تعيين الملاحظات
            self.notes_input.setPlainText(order[11] if order[11] else "")
            
            # حساب المساحة والإجمالي
            self.calculate_area_and_total()
            
    def calculate_area_and_total(self):
        """حساب المساحة والإجمالي"""
        length = self.length_input.value()
        width = self.width_input.value()
        quantity = self.quantity_input.value()
        unit_price = self.unit_price_input.value()
        
        # حساب المساحة الإجمالية (بالمتر المربع)
        total_area = (length * width * quantity) / 10000
        self.total_area_label.setText(f"{total_area:.2f} م²")
        
        # حساب الإجمالي
        total_amount = total_area * unit_price
        self.total_amount_label.setText(f"{total_amount:,.2f} جنيه")
        
    def save_order(self):
        """حفظ الطلب"""
        # جمع البيانات
        order_data = {
            'customer_id': self.customer_combo.currentData(),
            'order_date': self.order_date.date().toString("yyyy-MM-dd"),
            'delivery_date': self.delivery_date.date().toString("yyyy-MM-dd"),
            'granite_type_id': self.granite_type_combo.currentData(),
            'length_cm': self.length_input.value(),
            'width_cm': self.width_input.value(),
            'thickness_cm': self.thickness_input.value(),
            'quantity': self.quantity_input.value(),
            'unit_price': self.unit_price_input.value(),
            'total_amount': self.calculate_total_amount(),
            'notes': self.notes_input.toPlainText().strip()
        }
        
        # التحقق من صحة البيانات
        errors = self.order_model.validate_order_data(order_data)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
            
        # حفظ البيانات
        try:
            if self.order_id:
                # تحديث
                success, message = self.order_model.update_order(
                    self.order_id, order_data, self.user_data['id']
                )
            else:
                # إضافة جديد
                success, order_id, message = self.order_model.create_order(
                    order_data, self.user_data['id']
                )
                
            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات:\n{str(e)}")
            
    def calculate_total_amount(self):
        """حساب الإجمالي"""
        length = self.length_input.value()
        width = self.width_input.value()
        quantity = self.quantity_input.value()
        unit_price = self.unit_price_input.value()
        
        total_area = (length * width * quantity) / 10000
        return total_area * unit_price
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {config.COLORS['light']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['secondary']};
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #2980B9;
            }}
            
            QLineEdit, QTextEdit, QDoubleSpinBox, QSpinBox, QComboBox, QDateEdit {{
                padding: 5px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
            }}
        """)
