# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QStatusBar, QToolBar, QAction, QLabel,
                            QFrame, QPushButton, QGridLayout, QMessageBox,
                            QSplitter, QTextEdit, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap

import config
from src.utils.logger import log_user_action
from src.ui.dashboard_widget import DashboardWidget
from src.ui.trucks_widget import TrucksWidget
from src.ui.cutting_widget import CuttingWidget

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, database_manager, user_data):
        super().__init__()
        self.database_manager = database_manager
        self.user_data = user_data
        self.current_widget = None
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.load_dashboard()
        
        # تسجيل فتح النافذة الرئيسية
        log_user_action(self.user_data['id'], "OPEN_MAIN_WINDOW", "فتح النافذة الرئيسية")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        self.setWindowTitle(f"{config.APP_NAME} - {self.user_data['full_name']}")
        self.setGeometry(100, 100, config.UI_CONFIG['window_width'], config.UI_CONFIG['window_height'])
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)
        
        # الشريط الجانبي
        self.create_sidebar(main_layout)
        
        # المنطقة الرئيسية
        self.create_main_area(main_layout)
        
        self.apply_styles()
        
    def create_sidebar(self, main_layout):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = QFrame()
        sidebar_frame.setFixedWidth(250)
        sidebar_frame.setFrameStyle(QFrame.StyledPanel)
        
        sidebar_layout = QVBoxLayout()
        sidebar_layout.setSpacing(5)
        sidebar_layout.setContentsMargins(10, 10, 10, 10)
        
        # معلومات المستخدم
        self.create_user_info(sidebar_layout)
        
        # أزرار التنقل
        self.create_navigation_buttons(sidebar_layout)
        
        sidebar_frame.setLayout(sidebar_layout)
        main_layout.addWidget(sidebar_frame)
        
    def create_user_info(self, layout):
        """إنشاء معلومات المستخدم"""
        user_frame = QFrame()
        user_frame.setFrameStyle(QFrame.Box)
        user_layout = QVBoxLayout()
        
        # اسم المستخدم
        name_label = QLabel(self.user_data['full_name'])
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setFont(QFont(config.UI_CONFIG['font_family'], 12, QFont.Bold))
        
        # دور المستخدم
        role_label = QLabel(config.USER_ROLES.get(self.user_data['role'], self.user_data['role']))
        role_label.setAlignment(Qt.AlignCenter)
        role_label.setStyleSheet("color: #7F8C8D; font-size: 10px;")
        
        user_layout.addWidget(name_label)
        user_layout.addWidget(role_label)
        user_frame.setLayout(user_layout)
        
        layout.addWidget(user_frame)
        
    def create_navigation_buttons(self, layout):
        """إنشاء أزرار التنقل"""
        # قائمة الأزرار مع الصلاحيات
        buttons_config = [
            ("لوحة التحكم", "dashboard", "dashboard", True),
            ("استلام الجرارات", "trucks", "trucks_management", True),
            ("إدارة البلوكات", "blocks", "blocks_management", True),
            ("عمليات النشر", "cutting", "cutting_management", True),
            ("إدارة المخزون", "inventory", "inventory_management", True),
            ("المبيعات والفواتير", "sales", "sales_management", True),
            ("المصاريف", "expenses", "expenses_management", True),
            ("الطلبات والحجوزات", "orders", "orders_management", True),
            ("التقارير", "reports", "reports_access", True),
            ("إدارة المستخدمين", "users", "users_management", False),
            ("إعدادات النظام", "settings", "system_settings", False)
        ]
        
        self.nav_buttons = {}
        
        for text, key, permission, default_visible in buttons_config:
            # التحقق من الصلاحية
            has_permission = self.user_data['permissions'].get(permission, default_visible)
            
            if has_permission:
                button = QPushButton(text)
                button.setMinimumHeight(40)
                button.clicked.connect(lambda checked, k=key: self.navigate_to(k))
                
                self.nav_buttons[key] = button
                layout.addWidget(button)
        
        # زر تسجيل الخروج
        layout.addStretch()
        logout_button = QPushButton("تسجيل الخروج")
        logout_button.setMinimumHeight(40)
        logout_button.clicked.connect(self.logout)
        layout.addWidget(logout_button)
        
    def create_main_area(self, main_layout):
        """إنشاء المنطقة الرئيسية"""
        self.main_area = QFrame()
        self.main_area.setFrameStyle(QFrame.StyledPanel)
        
        self.main_area_layout = QVBoxLayout()
        self.main_area.setLayout(self.main_area_layout)
        
        main_layout.addWidget(self.main_area, 1)
        
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        # إنشاء نسخة احتياطية
        backup_action = QAction('إنشاء نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # أزرار سريعة
        dashboard_action = QAction('لوحة التحكم', self)
        dashboard_action.triggered.connect(lambda: self.navigate_to('dashboard'))
        toolbar.addAction(dashboard_action)
        
        toolbar.addSeparator()
        
        sales_action = QAction('مبيعات جديدة', self)
        sales_action.triggered.connect(lambda: self.navigate_to('sales'))
        toolbar.addAction(sales_action)
        
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.user_data['full_name']} | الدور: {config.USER_ROLES.get(self.user_data['role'])}"
        self.status_bar.showMessage(user_info)
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.update_time()
        
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {config.COLORS['light']};
            }}
            
            QFrame {{
                background-color: {config.COLORS['white']};
                border: 1px solid #BDC3C7;
            }}
            
            QPushButton {{
                background-color: {config.COLORS['primary']};
                color: white;
                border: none;
                padding: 10px;
                text-align: left;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: {config.COLORS['dark']};
            }}
            
            QPushButton:pressed {{
                background-color: {config.COLORS['secondary']};
            }}
            
            QMenuBar {{
                background-color: {config.COLORS['primary']};
                color: white;
                border: none;
            }}
            
            QMenuBar::item {{
                background-color: transparent;
                padding: 5px 10px;
            }}
            
            QMenuBar::item:selected {{
                background-color: {config.COLORS['secondary']};
            }}
            
            QToolBar {{
                background-color: {config.COLORS['light']};
                border: 1px solid #BDC3C7;
                spacing: 3px;
            }}
            
            QStatusBar {{
                background-color: {config.COLORS['primary']};
                color: white;
            }}
        """)
        
    def navigate_to(self, section):
        """التنقل إلى قسم معين"""
        # إزالة الويدجت الحالي
        if self.current_widget:
            self.main_area_layout.removeWidget(self.current_widget)
            self.current_widget.deleteLater()
            
        # تحديد الويدجت الجديد
        if section == 'dashboard':
            self.current_widget = DashboardWidget(self.database_manager, self.user_data)
        elif section == 'trucks':
            self.current_widget = TrucksWidget(self.database_manager, self.user_data)
        elif section == 'cutting':
            self.current_widget = CuttingWidget(self.database_manager, self.user_data)
        else:
            # ويدجت مؤقت للأقسام الأخرى
            self.current_widget = QLabel(f"قسم {section} قيد التطوير...")
            self.current_widget.setAlignment(Qt.AlignCenter)
            self.current_widget.setFont(QFont(config.UI_CONFIG['font_family'], 16))
            
        self.main_area_layout.addWidget(self.current_widget)
        
        # تسجيل التنقل
        log_user_action(self.user_data['id'], "NAVIGATE", f"التنقل إلى قسم: {section}")
        
    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        self.navigate_to('dashboard')
        
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        QMessageBox.information(self, "نسخة احتياطية", "سيتم تطوير هذه الوظيفة قريباً")
        
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
        <h2>{config.APP_NAME}</h2>
        <p><b>الإصدار:</b> {config.APP_VERSION}</p>
        <p><b>الوصف:</b> {config.APP_DESCRIPTION}</p>
        <p><b>الشركة:</b> {config.COMPANY_NAME}</p>
        <p><b>حقوق النشر:</b> © 2025 جميع الحقوق محفوظة</p>
        """
        QMessageBox.about(self, "حول البرنامج", about_text)
        
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, 
            "تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            log_user_action(self.user_data['id'], "LOGOUT", "تسجيل خروج")
            self.close()
            
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        log_user_action(self.user_data['id'], "CLOSE_MAIN_WINDOW", "إغلاق النافذة الرئيسية")
        event.accept()
