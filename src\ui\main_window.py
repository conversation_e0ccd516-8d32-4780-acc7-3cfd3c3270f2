# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QStatusBar, QToolBar, QAction, QLabel,
                            QFrame, QPushButton, QGridLayout, QMessageBox,
                            QSplitter, QTextEdit, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap

import config
from src.utils.logger import log_user_action
from src.ui.dashboard_widget import DashboardWidget
from src.ui.trucks_widget import TrucksWidget
from src.ui.cutting_widget import CuttingWidget
from src.ui.sales_widget import SalesWidget
from src.ui.expenses_widget import ExpensesWidget
from src.ui.orders_widget import OrdersWidget
from src.ui.reports_widget import ReportsWidget
from src.ui.settings_dialog import SettingsDialog
from src.ui.users_management_dialog import UsersManagementDialog

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, user_data=None, database_manager=None):
        super().__init__()

        # إعداد البيانات الافتراضية
        self.user_data = user_data or {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin',
            'permissions': {
                'dashboard': True,
                'trucks_management': True,
                'blocks_management': True,
                'cutting_management': True,
                'inventory_management': True,
                'sales_management': True,
                'expenses_management': True,
                'orders_management': True,
                'reports_access': True,
                'users_management': True,
                'system_settings': True
            }
        }

        # إنشاء مدير قاعدة البيانات إذا لم يتم تمريره
        if not database_manager:
            from src.database.database_manager import DatabaseManager
            self.database_manager = DatabaseManager()
            self.database_manager.connect()
        else:
            self.database_manager = database_manager

        self.current_widget = None
        self.nav_buttons = {}

        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.load_dashboard()

        # تطبيق الخطوط المتجاوبة
        try:
            from src.utils.font_utils import apply_font_settings
            apply_font_settings(self)
        except:
            pass

        # تسجيل فتح النافذة الرئيسية
        try:
            log_user_action(self.user_data['id'], "OPEN_MAIN_WINDOW", "فتح النافذة الرئيسية")
        except:
            pass
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        try:
            app_name = config.APP_NAME
        except:
            app_name = "نظام إدارة مصنع الجرانيت"

        self.setWindowTitle(f"{app_name} - {self.user_data['full_name']}")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 700)

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        central_widget.setLayout(main_layout)

        # الشريط الجانبي
        self.create_modern_sidebar(main_layout)

        # المنطقة الرئيسية
        self.create_modern_main_area(main_layout)

        self.apply_modern_styles()
        
    def create_modern_sidebar(self, main_layout):
        """إنشاء الشريط الجانبي الحديث"""
        sidebar_frame = QFrame()
        sidebar_frame.setFixedWidth(280)
        sidebar_frame.setObjectName("sidebar")

        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # رأس الشريط الجانبي
        self.create_sidebar_header(sidebar_layout)

        # معلومات المستخدم
        self.create_modern_user_info(sidebar_layout)

        # أزرار التنقل
        self.create_modern_navigation_buttons(sidebar_layout)

        # تذييل الشريط الجانبي
        self.create_sidebar_footer(sidebar_layout)

        sidebar_frame.setLayout(sidebar_layout)
        main_layout.addWidget(sidebar_frame)

    def create_sidebar_header(self, layout):
        """إنشاء رأس الشريط الجانبي"""
        header_frame = QFrame()
        header_frame.setObjectName("sidebarHeader")
        header_frame.setFixedHeight(80)

        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(20, 15, 20, 15)

        # شعار النظام
        logo_label = QLabel("🏗️")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 32px; color: white;")

        # اسم النظام
        title_label = QLabel("الحسن ستون")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white; margin-top: 5px;")

        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_frame.setLayout(header_layout)

        layout.addWidget(header_frame)

    def create_modern_user_info(self, layout):
        """إنشاء معلومات المستخدم الحديثة"""
        user_frame = QFrame()
        user_frame.setObjectName("userInfo")
        user_frame.setFixedHeight(100)

        user_layout = QVBoxLayout()
        user_layout.setContentsMargins(20, 15, 20, 15)

        # صورة المستخدم (رمز)
        avatar_label = QLabel("👤")
        avatar_label.setAlignment(Qt.AlignCenter)
        avatar_label.setStyleSheet("font-size: 24px; margin-bottom: 5px;")

        # اسم المستخدم
        name_label = QLabel(self.user_data['full_name'])
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 2px;")

        # دور المستخدم
        try:
            role_text = config.USER_ROLES.get(self.user_data['role'], self.user_data['role'])
        except:
            role_text = "مدير النظام"

        role_label = QLabel(role_text)
        role_label.setAlignment(Qt.AlignCenter)
        role_label.setStyleSheet("font-size: 11px; color: #7f8c8d;")

        user_layout.addWidget(avatar_label)
        user_layout.addWidget(name_label)
        user_layout.addWidget(role_label)
        user_frame.setLayout(user_layout)

        layout.addWidget(user_frame)

    def create_modern_navigation_buttons(self, layout):
        """إنشاء أزرار التنقل الحديثة"""
        # منطقة التنقل
        nav_scroll = QScrollArea()
        nav_scroll.setWidgetResizable(True)
        nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        nav_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        nav_scroll.setObjectName("navScroll")

        nav_widget = QWidget()
        nav_layout = QVBoxLayout()
        nav_layout.setContentsMargins(10, 10, 10, 10)
        nav_layout.setSpacing(5)

        # قائمة الأزرار مع الأيقونات
        buttons_config = [
            ("📊 لوحة التحكم", "dashboard", "dashboard", "📊", True),
            ("🚛 استلام الجرارات", "trucks", "trucks_management", "🚛", True),
            ("🧱 إدارة البلوكات", "blocks", "blocks_management", "🧱", True),
            ("⚡ عمليات النشر", "cutting", "cutting_management", "⚡", True),
            ("📦 إدارة المخزون", "inventory", "inventory_management", "📦", True),
            ("💰 المبيعات والفواتير", "sales", "sales_management", "💰", True),
            ("💸 المصاريف", "expenses", "expenses_management", "💸", True),
            ("📋 الطلبات والحجوزات", "orders", "orders_management", "📋", True),
            ("📈 التقارير", "reports", "reports_access", "📈", True),
            ("👥 إدارة المستخدمين", "users", "users_management", "👥", True),
            ("⚙️ إعدادات النظام", "settings", "system_settings", "⚙️", True)
        ]

        self.nav_buttons = {}

        for text, key, permission, icon, default_visible in buttons_config:
            # التحقق من الصلاحية
            has_permission = self.user_data['permissions'].get(permission, default_visible)

            if has_permission:
                button = QPushButton(text)
                button.setMinimumHeight(45)
                button.setObjectName("navButton")
                button.clicked.connect(lambda checked=False, k=key: self.navigate_to(k))

                self.nav_buttons[key] = button
                nav_layout.addWidget(button)

        nav_layout.addStretch()
        nav_widget.setLayout(nav_layout)
        nav_scroll.setWidget(nav_widget)

        layout.addWidget(nav_scroll)

    def create_sidebar_footer(self, layout):
        """إنشاء تذييل الشريط الجانبي"""
        footer_frame = QFrame()
        footer_frame.setObjectName("sidebarFooter")
        footer_frame.setFixedHeight(60)

        footer_layout = QVBoxLayout()
        footer_layout.setContentsMargins(10, 10, 10, 10)

        # زر تسجيل الخروج
        logout_button = QPushButton("🚪 تسجيل الخروج")
        logout_button.setMinimumHeight(40)
        logout_button.setObjectName("logoutButton")
        logout_button.clicked.connect(self.logout)

        footer_layout.addWidget(logout_button)
        footer_frame.setLayout(footer_layout)

        layout.addWidget(footer_frame)

    def create_modern_main_area(self, main_layout):
        """إنشاء المنطقة الرئيسية الحديثة"""
        # إطار المحتوى الرئيسي
        main_content_frame = QFrame()
        main_content_frame.setObjectName("mainContent")

        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # شريط العنوان
        self.create_title_bar(content_layout)

        # منطقة المحتوى
        self.main_area = QFrame()
        self.main_area.setObjectName("contentArea")

        self.main_area_layout = QVBoxLayout()
        self.main_area_layout.setContentsMargins(20, 20, 20, 20)
        self.main_area.setLayout(self.main_area_layout)

        content_layout.addWidget(self.main_area, 1)
        main_content_frame.setLayout(content_layout)

        main_layout.addWidget(main_content_frame)

    def create_title_bar(self, layout):
        """إنشاء شريط العنوان"""
        title_frame = QFrame()
        title_frame.setObjectName("titleBar")
        title_frame.setFixedHeight(60)

        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(20, 10, 20, 10)

        # عنوان القسم الحالي
        self.section_title = QLabel("لوحة التحكم")
        self.section_title.setObjectName("sectionTitle")
        self.section_title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50;")

        # أزرار سريعة
        quick_actions_layout = QHBoxLayout()

        # زر إضافة سريع
        add_button = QPushButton("➕ إضافة")
        add_button.setObjectName("quickButton")
        add_button.clicked.connect(self.quick_add)

        # زر بحث
        search_button = QPushButton("🔍 بحث")
        search_button.setObjectName("quickButton")
        search_button.clicked.connect(self.quick_search)

        # زر تحديث
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setObjectName("quickButton")
        refresh_button.clicked.connect(self.refresh_current_view)

        quick_actions_layout.addWidget(add_button)
        quick_actions_layout.addWidget(search_button)
        quick_actions_layout.addWidget(refresh_button)

        title_layout.addWidget(self.section_title)
        title_layout.addStretch()
        title_layout.addLayout(quick_actions_layout)

        title_frame.setLayout(title_layout)
        layout.addWidget(title_frame)
        
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        # الإعدادات
        settings_action = QAction('الإعدادات', self)
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)

        file_menu.addSeparator()

        # إنشاء نسخة احتياطية
        backup_action = QAction('إنشاء نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)

        file_menu.addSeparator()

        # خروج
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة الأدوات
        tools_menu = menubar.addMenu('أدوات')

        # إدارة المستخدمين (للمديرين فقط)
        if self.user_data['role'] == 'admin':
            users_action = QAction('إدارة المستخدمين', self)
            users_action.triggered.connect(self.manage_users)
            tools_menu.addAction(users_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # أزرار سريعة
        dashboard_action = QAction('لوحة التحكم', self)
        dashboard_action.triggered.connect(lambda: self.navigate_to('dashboard'))
        toolbar.addAction(dashboard_action)
        
        toolbar.addSeparator()
        
        sales_action = QAction('مبيعات جديدة', self)
        sales_action.triggered.connect(lambda: self.navigate_to('sales'))
        toolbar.addAction(sales_action)

        toolbar.addSeparator()

        # زر الإعدادات
        settings_action = QAction('الإعدادات', self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.user_data['full_name']} | الدور: {config.USER_ROLES.get(self.user_data['role'])}"
        self.status_bar.showMessage(user_info)
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.update_time()
        
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
        
    def apply_modern_styles(self):
        """تطبيق الأنماط الحديثة"""
        self.setStyleSheet("""
            /* النافذة الرئيسية */
            QMainWindow {
                background-color: #f8f9fa;
            }

            /* الشريط الجانبي */
            QFrame#sidebar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border: none;
            }

            /* رأس الشريط الجانبي */
            QFrame#sidebarHeader {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1abc9c, stop:1 #16a085);
                border: none;
            }

            /* معلومات المستخدم */
            QFrame#userInfo {
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 10px;
                margin: 10px;
            }

            /* منطقة التنقل */
            QScrollArea#navScroll {
                background-color: transparent;
                border: none;
            }

            QScrollArea#navScroll QWidget {
                background-color: transparent;
            }

            /* أزرار التنقل */
            QPushButton#navButton {
                background-color: transparent;
                color: white;
                border: none;
                padding: 12px 20px;
                text-align: left;
                font-size: 14px;
                font-weight: 500;
                border-radius: 8px;
                margin: 2px 5px;
            }

            QPushButton#navButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                transform: translateX(5px);
            }

            QPushButton#navButton:pressed {
                background-color: rgba(255, 255, 255, 0.2);
            }

            /* تذييل الشريط الجانبي */
            QFrame#sidebarFooter {
                background-color: rgba(0, 0, 0, 0.1);
                border: none;
            }

            /* زر تسجيل الخروج */
            QPushButton#logoutButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
            }

            QPushButton#logoutButton:hover {
                background-color: #c0392b;
            }

            /* المحتوى الرئيسي */
            QFrame#mainContent {
                background-color: white;
                border: none;
            }

            /* شريط العنوان */
            QFrame#titleBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 2px solid #e9ecef;
            }

            /* منطقة المحتوى */
            QFrame#contentArea {
                background-color: white;
                border: none;
            }

            /* الأزرار السريعة */
            QPushButton#quickButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 6px;
                font-weight: 500;
                margin: 0 3px;
            }

            QPushButton#quickButton:hover {
                background-color: #2980b9;
                transform: translateY(-1px);
            }

            QPushButton#quickButton:pressed {
                background-color: #21618c;
            }

            /* شريط القوائم */
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2c3e50, stop:1 #34495e);
                color: white;
                border: none;
                padding: 5px;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 8px 15px;
                border-radius: 4px;
                margin: 2px;
            }

            QMenuBar::item:selected {
                background-color: rgba(255, 255, 255, 0.1);
            }

            /* شريط الأدوات */
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                border: none;
                spacing: 5px;
                padding: 5px;
            }

            QToolBar QAction {
                padding: 8px 12px;
                margin: 2px;
                border-radius: 4px;
            }

            /* شريط الحالة */
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: white;
                border: none;
                padding: 5px;
            }

            /* التمرير */
            QScrollBar:vertical {
                background-color: #ecf0f1;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
        """)
        
    def navigate_to(self, section):
        """التنقل إلى قسم معين"""
        # تحديث عنوان القسم
        section_titles = {
            'dashboard': '📊 لوحة التحكم',
            'trucks': '🚛 استلام الجرارات',
            'blocks': '🧱 إدارة البلوكات',
            'cutting': '⚡ عمليات النشر',
            'inventory': '📦 إدارة المخزون',
            'sales': '💰 المبيعات والفواتير',
            'expenses': '💸 المصاريف',
            'orders': '📋 الطلبات والحجوزات',
            'reports': '📈 التقارير',
            'users': '👥 إدارة المستخدمين',
            'settings': '⚙️ إعدادات النظام'
        }

        self.section_title.setText(section_titles.get(section, f"قسم {section}"))

        # تحديث حالة الأزرار
        for key, button in self.nav_buttons.items():
            if key == section:
                button.setStyleSheet("""
                    QPushButton#navButton {
                        background-color: rgba(255, 255, 255, 0.2);
                        color: white;
                        border-left: 4px solid #1abc9c;
                    }
                """)
            else:
                button.setStyleSheet("")

        # إزالة الويدجت الحالي
        if self.current_widget:
            self.main_area_layout.removeWidget(self.current_widget)
            self.current_widget.deleteLater()

        # تحديد الويدجت الجديد
        try:
            if section == 'dashboard':
                self.current_widget = DashboardWidget(self.database_manager, self.user_data)
            elif section == 'trucks':
                self.current_widget = TrucksWidget(self.database_manager, self.user_data)
            elif section == 'blocks':
                self.current_widget = self.create_blocks_widget()
            elif section == 'cutting':
                self.current_widget = CuttingWidget(self.database_manager, self.user_data)
            elif section == 'inventory':
                self.current_widget = self.create_inventory_widget()
            elif section == 'sales':
                self.current_widget = SalesWidget(self.database_manager, self.user_data)
            elif section == 'expenses':
                self.current_widget = ExpensesWidget(self.database_manager, self.user_data)
            elif section == 'orders':
                self.current_widget = OrdersWidget(self.database_manager, self.user_data)
            elif section == 'reports':
                self.current_widget = ReportsWidget(self.database_manager, self.user_data)
            elif section == 'users':
                self.current_widget = self.create_users_widget()
            elif section == 'settings':
                self.show_settings()
                return  # لا نحتاج لتغيير الويدجت
            else:
                self.current_widget = self.create_placeholder_widget(section)

        except Exception as e:
            # في حالة فشل تحميل الويدجت، عرض رسالة خطأ
            self.current_widget = self.create_error_widget(section, str(e))

        self.main_area_layout.addWidget(self.current_widget)

        # تسجيل التنقل
        try:
            log_user_action(self.user_data['id'], "NAVIGATE", f"التنقل إلى قسم: {section}")
        except:
            pass

    def create_blocks_widget(self):
        """إنشاء ويدجت إدارة البلوكات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان القسم
        title = QLabel("🧱 إدارة البلوكات")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)

        # محتوى مؤقت
        content = QLabel("قسم إدارة البلوكات قيد التطوير...\n\nسيتضمن:\n• عرض جميع البلوكات\n• إضافة بلوكات جديدة\n• تعديل معلومات البلوكات\n• تتبع حالة البلوكات")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("font-size: 14px; color: #7f8c8d; padding: 50px;")
        layout.addWidget(content)

        widget.setLayout(layout)
        return widget

    def create_inventory_widget(self):
        """إنشاء ويدجت إدارة المخزون"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان القسم
        title = QLabel("📦 إدارة المخزون")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)

        # محتوى مؤقت
        content = QLabel("قسم إدارة المخزون قيد التطوير...\n\nسيتضمن:\n• جرد المخزون\n• تتبع الكميات\n• تقارير المخزون\n• إنذارات النفاد")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("font-size: 14px; color: #7f8c8d; padding: 50px;")
        layout.addWidget(content)

        widget.setLayout(layout)
        return widget

    def create_users_widget(self):
        """إنشاء ويدجت إدارة المستخدمين"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان القسم
        title = QLabel("👥 إدارة المستخدمين")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)

        # زر فتح نافذة إدارة المستخدمين
        open_users_btn = QPushButton("فتح نافذة إدارة المستخدمين")
        open_users_btn.setStyleSheet("font-size: 14px; padding: 10px 20px; background-color: #3498db; color: white; border: none; border-radius: 5px;")
        open_users_btn.clicked.connect(self.manage_users)
        layout.addWidget(open_users_btn)

        # محتوى مؤقت
        content = QLabel("أو استخدم النافذة المنفصلة لإدارة المستخدمين")
        content.setAlignment(Qt.AlignCenter)
        content.setStyleSheet("font-size: 14px; color: #7f8c8d; padding: 30px;")
        layout.addWidget(content)

        widget.setLayout(layout)
        return widget

    def create_placeholder_widget(self, section):
        """إنشاء ويدجت مؤقت للأقسام قيد التطوير"""
        widget = QWidget()
        layout = QVBoxLayout()

        # أيقونة
        icon_label = QLabel("🚧")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px; margin-bottom: 20px;")
        layout.addWidget(icon_label)

        # رسالة
        message = QLabel(f"قسم {section} قيد التطوير...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("font-size: 18px; font-weight: bold; color: #e67e22; margin-bottom: 10px;")
        layout.addWidget(message)

        # تفاصيل
        details = QLabel("سيتم إضافة هذا القسم في التحديثات القادمة")
        details.setAlignment(Qt.AlignCenter)
        details.setStyleSheet("font-size: 14px; color: #7f8c8d;")
        layout.addWidget(details)

        widget.setLayout(layout)
        return widget

    def create_error_widget(self, section, error):
        """إنشاء ويدجت عرض الأخطاء"""
        widget = QWidget()
        layout = QVBoxLayout()

        # أيقونة خطأ
        icon_label = QLabel("❌")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px; margin-bottom: 20px;")
        layout.addWidget(icon_label)

        # رسالة خطأ
        error_message = QLabel(f"خطأ في تحميل قسم {section}")
        error_message.setAlignment(Qt.AlignCenter)
        error_message.setStyleSheet("font-size: 18px; font-weight: bold; color: #e74c3c; margin-bottom: 10px;")
        layout.addWidget(error_message)

        # تفاصيل الخطأ
        error_details = QLabel(f"تفاصيل الخطأ: {error}")
        error_details.setAlignment(Qt.AlignCenter)
        error_details.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        error_details.setWordWrap(True)
        layout.addWidget(error_details)

        # زر إعادة المحاولة
        retry_btn = QPushButton("إعادة المحاولة")
        retry_btn.setStyleSheet("font-size: 14px; padding: 10px 20px; background-color: #3498db; color: white; border: none; border-radius: 5px;")
        retry_btn.clicked.connect(lambda: self.navigate_to(section))
        layout.addWidget(retry_btn)

        widget.setLayout(layout)
        return widget

    def quick_add(self):
        """إضافة سريعة"""
        QMessageBox.information(self, "إضافة سريعة", "ميزة الإضافة السريعة قيد التطوير")

    def quick_search(self):
        """بحث سريع"""
        QMessageBox.information(self, "بحث سريع", "ميزة البحث السريع قيد التطوير")

    def refresh_current_view(self):
        """تحديث العرض الحالي"""
        # إعادة تحميل الويدجت الحالي
        if hasattr(self, 'current_section'):
            self.navigate_to(self.current_section)
        else:
            self.load_dashboard()

    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        self.navigate_to('dashboard')
        
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        settings_dialog = SettingsDialog(self, self.user_data)
        settings_dialog.settings_changed.connect(self.apply_new_settings)
        settings_dialog.exec_()

    def apply_new_settings(self):
        """تطبيق الإعدادات الجديدة"""
        # إعادة تحميل الإعدادات وتطبيقها
        from src.models.settings import Settings
        settings = Settings()

        # تطبيق إعدادات الخط
        font_settings = settings.get_font_settings()
        font = QFont(font_settings['family'], font_settings['size'])
        font.setBold(font_settings['bold'])
        self.setFont(font)

        # تطبيق إعدادات النافذة
        window_settings = settings.get_window_settings()
        if not window_settings['maximized']:
            self.resize(window_settings['width'], window_settings['height'])
        else:
            self.showMaximized()

        # إعادة تطبيق الأنماط
        self.apply_styles()

        # إشعار المستخدم
        self.status_bar.showMessage("تم تطبيق الإعدادات الجديدة", 3000)

    def manage_users(self):
        """إدارة المستخدمين"""
        dialog = UsersManagementDialog(self, self.database_manager, self.user_data)
        dialog.exec_()

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        QMessageBox.information(self, "نسخة احتياطية", "سيتم تطوير هذه الوظيفة قريباً")
        
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
        <h2>{config.APP_NAME}</h2>
        <p><b>الإصدار:</b> {config.APP_VERSION}</p>
        <p><b>الوصف:</b> {config.APP_DESCRIPTION}</p>
        <p><b>الشركة:</b> {config.COMPANY_NAME}</p>
        <p><b>حقوق النشر:</b> © 2025 جميع الحقوق محفوظة</p>
        """
        QMessageBox.about(self, "حول البرنامج", about_text)
        
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, 
            "تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            log_user_action(self.user_data['id'], "LOGOUT", "تسجيل خروج")
            self.close()
            
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        log_user_action(self.user_data['id'], "CLOSE_MAIN_WINDOW", "إغلاق النافذة الرئيسية")
        event.accept()
