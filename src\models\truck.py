# -*- coding: utf-8 -*-
"""
نموذج الجرارات
Truck Model
"""

from datetime import datetime
from src.utils.logger import log_user_action, log_database_operation, log_error

class Truck:
    """نموذج الجرارات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def add_truck(self, truck_data, user_id):
        """إضافة جرار جديد"""
        try:
            query = """
            INSERT INTO trucks (truck_number, weight_tons, price_per_ton, total_cost, 
                              arrival_date, supplier, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                truck_data['truck_number'],
                truck_data['weight_tons'],
                truck_data['price_per_ton'],
                truck_data['total_cost'],
                truck_data['arrival_date'],
                truck_data.get('supplier', ''),
                truck_data.get('notes', ''),
                user_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                # الحصول على ID الجرار الجديد
                truck_id = self.db_manager.connection.lastrowid
                
                log_user_action(user_id, "ADD_TRUCK", f"إضافة جرار رقم: {truck_data['truck_number']}")
                log_database_operation("INSERT", "trucks", f"جرار ID: {truck_id}")
                
                return True, truck_id, "تم إضافة الجرار بنجاح"
            else:
                return False, None, "فشل في إضافة الجرار"
                
        except Exception as e:
            log_error(f"خطأ في إضافة الجرار: {str(e)}")
            return False, None, str(e)
            
    def get_all_trucks(self, limit=None):
        """الحصول على جميع الجرارات"""
        try:
            query = """
            SELECT t.id, t.truck_number, t.weight_tons, t.price_per_ton, 
                   t.total_cost, t.arrival_date, t.supplier, t.notes,
                   t.created_at, u.full_name as created_by_name,
                   COUNT(b.id) as blocks_count
            FROM trucks t
            LEFT JOIN users u ON t.created_by = u.id
            LEFT JOIN blocks b ON t.id = b.truck_id
            GROUP BY t.id, t.truck_number, t.weight_tons, t.price_per_ton, 
                     t.total_cost, t.arrival_date, t.supplier, t.notes,
                     t.created_at, u.full_name
            ORDER BY t.arrival_date DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
                
            return self.db_manager.execute_query(query, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الجرارات: {str(e)}")
            return []
            
    def get_truck_by_id(self, truck_id):
        """الحصول على جرار بالمعرف"""
        try:
            query = """
            SELECT t.*, u.full_name as created_by_name
            FROM trucks t
            LEFT JOIN users u ON t.created_by = u.id
            WHERE t.id = ?
            """
            
            return self.db_manager.execute_query(query, (truck_id,), fetch='one')
            
        except Exception as e:
            log_error(f"خطأ في جلب الجرار: {str(e)}")
            return None
            
    def update_truck(self, truck_id, truck_data, user_id):
        """تحديث بيانات الجرار"""
        try:
            query = """
            UPDATE trucks 
            SET truck_number = ?, weight_tons = ?, price_per_ton = ?, 
                total_cost = ?, arrival_date = ?, supplier = ?, notes = ?
            WHERE id = ?
            """
            
            params = (
                truck_data['truck_number'],
                truck_data['weight_tons'],
                truck_data['price_per_ton'],
                truck_data['total_cost'],
                truck_data['arrival_date'],
                truck_data.get('supplier', ''),
                truck_data.get('notes', ''),
                truck_id
            )
            
            result = self.db_manager.execute_query(query, params)
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "UPDATE_TRUCK", f"تحديث جرار ID: {truck_id}")
                log_database_operation("UPDATE", "trucks", f"جرار ID: {truck_id}")
                
                return True, "تم تحديث الجرار بنجاح"
            else:
                return False, "فشل في تحديث الجرار"
                
        except Exception as e:
            log_error(f"خطأ في تحديث الجرار: {str(e)}")
            return False, str(e)
            
    def delete_truck(self, truck_id, user_id):
        """حذف جرار"""
        try:
            # التحقق من وجود بلوكات مرتبطة
            check_query = "SELECT COUNT(*) FROM blocks WHERE truck_id = ?"
            result = self.db_manager.execute_query(check_query, (truck_id,), fetch='one')
            
            if result and result[0] > 0:
                return False, "لا يمكن حذف الجرار لوجود بلوكات مرتبطة به"
                
            # حذف الجرار
            delete_query = "DELETE FROM trucks WHERE id = ?"
            result = self.db_manager.execute_query(delete_query, (truck_id,))
            
            if result is not None:
                if self.db_manager.connection:
                    self.db_manager.connection.commit()
                    
                log_user_action(user_id, "DELETE_TRUCK", f"حذف جرار ID: {truck_id}")
                log_database_operation("DELETE", "trucks", f"جرار ID: {truck_id}")
                
                return True, "تم حذف الجرار بنجاح"
            else:
                return False, "فشل في حذف الجرار"
                
        except Exception as e:
            log_error(f"خطأ في حذف الجرار: {str(e)}")
            return False, str(e)
            
    def search_trucks(self, search_term):
        """البحث في الجرارات"""
        try:
            query = """
            SELECT t.id, t.truck_number, t.weight_tons, t.price_per_ton, 
                   t.total_cost, t.arrival_date, t.supplier, t.notes,
                   t.created_at, u.full_name as created_by_name
            FROM trucks t
            LEFT JOIN users u ON t.created_by = u.id
            WHERE t.truck_number LIKE ? OR t.supplier LIKE ? OR t.notes LIKE ?
            ORDER BY t.arrival_date DESC
            """
            
            search_pattern = f"%{search_term}%"
            params = (search_pattern, search_pattern, search_pattern)
            
            return self.db_manager.execute_query(query, params, fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في البحث في الجرارات: {str(e)}")
            return []
            
    def get_trucks_by_date_range(self, start_date, end_date):
        """الحصول على الجرارات في فترة زمنية"""
        try:
            query = """
            SELECT t.id, t.truck_number, t.weight_tons, t.price_per_ton, 
                   t.total_cost, t.arrival_date, t.supplier, t.notes,
                   t.created_at, u.full_name as created_by_name
            FROM trucks t
            LEFT JOIN users u ON t.created_by = u.id
            WHERE DATE(t.arrival_date) BETWEEN ? AND ?
            ORDER BY t.arrival_date DESC
            """
            
            return self.db_manager.execute_query(query, (start_date, end_date), fetch='all')
            
        except Exception as e:
            log_error(f"خطأ في جلب الجرارات بالتاريخ: {str(e)}")
            return []
            
    def get_truck_statistics(self):
        """الحصول على إحصائيات الجرارات"""
        try:
            stats = {}
            
            # إجمالي عدد الجرارات
            query = "SELECT COUNT(*) FROM trucks"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_trucks'] = result[0] if result else 0
            
            # إجمالي الوزن
            query = "SELECT SUM(weight_tons) FROM trucks"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_weight'] = result[0] if result and result[0] else 0
            
            # إجمالي التكلفة
            query = "SELECT SUM(total_cost) FROM trucks"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['total_cost'] = result[0] if result and result[0] else 0
            
            # متوسط سعر الطن
            query = "SELECT AVG(price_per_ton) FROM trucks"
            result = self.db_manager.execute_query(query, fetch='one')
            stats['avg_price_per_ton'] = result[0] if result and result[0] else 0
            
            # الجرارات هذا الشهر
            query = """
            SELECT COUNT(*) FROM trucks 
            WHERE strftime('%Y-%m', arrival_date) = strftime('%Y-%m', 'now')
            """
            result = self.db_manager.execute_query(query, fetch='one')
            stats['trucks_this_month'] = result[0] if result else 0
            
            return stats
            
        except Exception as e:
            log_error(f"خطأ في جلب إحصائيات الجرارات: {str(e)}")
            return {}
            
    def validate_truck_data(self, truck_data):
        """التحقق من صحة بيانات الجرار"""
        errors = []
        
        # التحقق من رقم الجرار
        if not truck_data.get('truck_number', '').strip():
            errors.append("رقم الجرار مطلوب")
        elif len(truck_data['truck_number'].strip()) < 3:
            errors.append("رقم الجرار يجب أن يكون 3 أحرف على الأقل")
            
        # التحقق من الوزن
        try:
            weight = float(truck_data.get('weight_tons', 0))
            if weight <= 0:
                errors.append("وزن الجرار يجب أن يكون أكبر من صفر")
        except (ValueError, TypeError):
            errors.append("وزن الجرار يجب أن يكون رقماً صحيحاً")
            
        # التحقق من سعر الطن
        try:
            price = float(truck_data.get('price_per_ton', 0))
            if price <= 0:
                errors.append("سعر الطن يجب أن يكون أكبر من صفر")
        except (ValueError, TypeError):
            errors.append("سعر الطن يجب أن يكون رقماً صحيحاً")
            
        # التحقق من التاريخ
        if not truck_data.get('arrival_date'):
            errors.append("تاريخ الوصول مطلوب")
            
        return errors
