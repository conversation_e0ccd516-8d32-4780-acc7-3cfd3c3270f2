#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الكامل لإدارة مصنع الجرانيت
Complete System Runner for Granite Factory Management
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل النظام الكامل"""
    print("=" * 60)
    print("🏭 نظام إدارة مصنع الجرانيت - الحسن للأحجار")
    print("=" * 60)
    print()
    print("الميزات المتاحة في النظام:")
    print("🚛 إدارة الشاحنات والرحلات")
    print("⚙️  عمليات التقطيع والإنتاج")
    print("💰 إدارة المبيعات والفواتير")
    print("👥 إدارة العملاء")
    print("📊 إدارة المصاريف التشغيلية")
    print("📋 إدارة الطلبات والحجوزات")
    print("📦 إدارة المخزون والشرائح")
    print("👤 نظام المستخدمين والصلاحيات")
    print("📈 التقارير والإحصائيات المتقدمة")
    print("🏠 لوحة تحكم شاملة")
    print()
    print("=" * 60)
    print()
    
    try:
        # استيراد وتشغيل التطبيق الرئيسي
        from main import main as run_app
        return run_app()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("تأكد من تثبيت جميع المتطلبات:")
        print("pip install PyQt5 pyodbc")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
